import React, { useState } from 'react';
import { useSynergyAI } from '../hooks/use-synergy-ai';

export const ConnectionTest: React.FC = () => {
  const { apiConnected, websocketConnected, loadProjects, loadAgents } = useSynergyAI();
  const [testResults, setTestResults] = useState<string[]>([]);
  const [testing, setTesting] = useState(false);

  const runConnectionTest = async () => {
    setTesting(true);
    setTestResults([]);
    const results: string[] = [];

    try {
      results.push('🔄 Testing API connection...');
      setTestResults([...results]);

      await loadProjects();
      results.push('✅ Projects API: Success');
      setTestResults([...results]);

      await loadAgents();
      results.push('✅ Agents API: Success');
      setTestResults([...results]);

      results.push('🎉 All tests passed!');
    } catch (error: any) {
      results.push(`❌ Test failed: ${error.message || 'Unknown error'}`);
    } finally {
      setTestResults(results);
      setTesting(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Backend Connection Test</h3>
      
      <div className="space-y-3 mb-4">
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${apiConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
          <span className="text-sm">API Connection: {apiConnected ? 'Connected' : 'Disconnected'}</span>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${websocketConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
          <span className="text-sm">WebSocket: {websocketConnected ? 'Connected' : 'Disconnected'}</span>
        </div>
      </div>

      <button
        onClick={runConnectionTest}
        disabled={testing}
        className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300 text-white font-medium px-4 py-2 rounded-lg transition-colors duration-200 mb-4"
      >
        {testing ? 'Testing...' : 'Test Backend APIs'}
      </button>

      {testResults.length > 0 && (
        <div className="bg-gray-50 rounded-lg p-3">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Test Results:</h4>
          <div className="space-y-1">
            {testResults.map((result, index) => (
              <div key={index} className="text-sm text-gray-600 font-mono">
                {result}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

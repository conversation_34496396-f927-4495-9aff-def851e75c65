(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const f of document.querySelectorAll('link[rel="modulepreload"]'))c(f);new MutationObserver(f=>{for(const d of f)if(d.type==="childList")for(const y of d.addedNodes)y.tagName==="LINK"&&y.rel==="modulepreload"&&c(y)}).observe(document,{childList:!0,subtree:!0});function s(f){const d={};return f.integrity&&(d.integrity=f.integrity),f.referrerPolicy&&(d.referrerPolicy=f.referrerPolicy),f.crossOrigin==="use-credentials"?d.credentials="include":f.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function c(f){if(f.ep)return;f.ep=!0;const d=s(f);fetch(f.href,d)}})();function o0(u){return u&&u.__esModule&&Object.prototype.hasOwnProperty.call(u,"default")?u.default:u}var Kc={exports:{}},Ya={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Td;function f0(){if(Td)return Ya;Td=1;var u=Symbol.for("react.transitional.element"),a=Symbol.for("react.fragment");function s(c,f,d){var y=null;if(d!==void 0&&(y=""+d),f.key!==void 0&&(y=""+f.key),"key"in f){d={};for(var E in f)E!=="key"&&(d[E]=f[E])}else d=f;return f=d.ref,{$$typeof:u,type:c,key:y,ref:f!==void 0?f:null,props:d}}return Ya.Fragment=a,Ya.jsx=s,Ya.jsxs=s,Ya}var Od;function h0(){return Od||(Od=1,Kc.exports=f0()),Kc.exports}var ut=h0(),Jc={exports:{}},tt={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Rd;function d0(){if(Rd)return tt;Rd=1;var u=Symbol.for("react.transitional.element"),a=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),f=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),y=Symbol.for("react.context"),E=Symbol.for("react.forward_ref"),T=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),b=Symbol.for("react.lazy"),D=Symbol.iterator;function z(g){return g===null||typeof g!="object"?null:(g=D&&g[D]||g["@@iterator"],typeof g=="function"?g:null)}var L={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},B=Object.assign,j={};function q(g,M,X){this.props=g,this.context=M,this.refs=j,this.updater=X||L}q.prototype.isReactComponent={},q.prototype.setState=function(g,M){if(typeof g!="object"&&typeof g!="function"&&g!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,g,M,"setState")},q.prototype.forceUpdate=function(g){this.updater.enqueueForceUpdate(this,g,"forceUpdate")};function Q(){}Q.prototype=q.prototype;function P(g,M,X){this.props=g,this.context=M,this.refs=j,this.updater=X||L}var et=P.prototype=new Q;et.constructor=P,B(et,q.prototype),et.isPureReactComponent=!0;var Rt=Array.isArray,F={H:null,A:null,T:null,S:null,V:null},At=Object.prototype.hasOwnProperty;function Nt(g,M,X,H,k,rt){return X=rt.ref,{$$typeof:u,type:g,key:M,ref:X!==void 0?X:null,props:rt}}function qt(g,M){return Nt(g.type,M,void 0,void 0,void 0,g.props)}function ie(g){return typeof g=="object"&&g!==null&&g.$$typeof===u}function Pn(g){var M={"=":"=0",":":"=2"};return"$"+g.replace(/[=:]/g,function(X){return M[X]})}var Ye=/\/+/g;function Qt(g,M){return typeof g=="object"&&g!==null&&g.key!=null?Pn(""+g.key):M.toString(36)}function wn(){}function xn(g){switch(g.status){case"fulfilled":return g.value;case"rejected":throw g.reason;default:switch(typeof g.status=="string"?g.then(wn,wn):(g.status="pending",g.then(function(M){g.status==="pending"&&(g.status="fulfilled",g.value=M)},function(M){g.status==="pending"&&(g.status="rejected",g.reason=M)})),g.status){case"fulfilled":return g.value;case"rejected":throw g.reason}}throw g}function kt(g,M,X,H,k){var rt=typeof g;(rt==="undefined"||rt==="boolean")&&(g=null);var $=!1;if(g===null)$=!0;else switch(rt){case"bigint":case"string":case"number":$=!0;break;case"object":switch(g.$$typeof){case u:case a:$=!0;break;case b:return $=g._init,kt($(g._payload),M,X,H,k)}}if($)return k=k(g),$=H===""?"."+Qt(g,0):H,Rt(k)?(X="",$!=null&&(X=$.replace(Ye,"$&/")+"/"),kt(k,M,X,"",function(nn){return nn})):k!=null&&(ie(k)&&(k=qt(k,X+(k.key==null||g&&g.key===k.key?"":(""+k.key).replace(Ye,"$&/")+"/")+$)),M.push(k)),1;$=0;var ue=H===""?".":H+":";if(Rt(g))for(var St=0;St<g.length;St++)H=g[St],rt=ue+Qt(H,St),$+=kt(H,M,X,rt,k);else if(St=z(g),typeof St=="function")for(g=St.call(g),St=0;!(H=g.next()).done;)H=H.value,rt=ue+Qt(H,St++),$+=kt(H,M,X,rt,k);else if(rt==="object"){if(typeof g.then=="function")return kt(xn(g),M,X,H,k);throw M=String(g),Error("Objects are not valid as a React child (found: "+(M==="[object Object]"?"object with keys {"+Object.keys(g).join(", ")+"}":M)+"). If you meant to render a collection of children, use an array instead.")}return $}function x(g,M,X){if(g==null)return g;var H=[],k=0;return kt(g,H,"","",function(rt){return M.call(X,rt,k++)}),H}function Y(g){if(g._status===-1){var M=g._result;M=M(),M.then(function(X){(g._status===0||g._status===-1)&&(g._status=1,g._result=X)},function(X){(g._status===0||g._status===-1)&&(g._status=2,g._result=X)}),g._status===-1&&(g._status=0,g._result=M)}if(g._status===1)return g._result.default;throw g._result}var J=typeof reportError=="function"?reportError:function(g){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var M=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof g=="object"&&g!==null&&typeof g.message=="string"?String(g.message):String(g),error:g});if(!window.dispatchEvent(M))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",g);return}console.error(g)};function gt(){}return tt.Children={map:x,forEach:function(g,M,X){x(g,function(){M.apply(this,arguments)},X)},count:function(g){var M=0;return x(g,function(){M++}),M},toArray:function(g){return x(g,function(M){return M})||[]},only:function(g){if(!ie(g))throw Error("React.Children.only expected to receive a single React element child.");return g}},tt.Component=q,tt.Fragment=s,tt.Profiler=f,tt.PureComponent=P,tt.StrictMode=c,tt.Suspense=T,tt.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=F,tt.__COMPILER_RUNTIME={__proto__:null,c:function(g){return F.H.useMemoCache(g)}},tt.cache=function(g){return function(){return g.apply(null,arguments)}},tt.cloneElement=function(g,M,X){if(g==null)throw Error("The argument must be a React element, but you passed "+g+".");var H=B({},g.props),k=g.key,rt=void 0;if(M!=null)for($ in M.ref!==void 0&&(rt=void 0),M.key!==void 0&&(k=""+M.key),M)!At.call(M,$)||$==="key"||$==="__self"||$==="__source"||$==="ref"&&M.ref===void 0||(H[$]=M[$]);var $=arguments.length-2;if($===1)H.children=X;else if(1<$){for(var ue=Array($),St=0;St<$;St++)ue[St]=arguments[St+2];H.children=ue}return Nt(g.type,k,void 0,void 0,rt,H)},tt.createContext=function(g){return g={$$typeof:y,_currentValue:g,_currentValue2:g,_threadCount:0,Provider:null,Consumer:null},g.Provider=g,g.Consumer={$$typeof:d,_context:g},g},tt.createElement=function(g,M,X){var H,k={},rt=null;if(M!=null)for(H in M.key!==void 0&&(rt=""+M.key),M)At.call(M,H)&&H!=="key"&&H!=="__self"&&H!=="__source"&&(k[H]=M[H]);var $=arguments.length-2;if($===1)k.children=X;else if(1<$){for(var ue=Array($),St=0;St<$;St++)ue[St]=arguments[St+2];k.children=ue}if(g&&g.defaultProps)for(H in $=g.defaultProps,$)k[H]===void 0&&(k[H]=$[H]);return Nt(g,rt,void 0,void 0,null,k)},tt.createRef=function(){return{current:null}},tt.forwardRef=function(g){return{$$typeof:E,render:g}},tt.isValidElement=ie,tt.lazy=function(g){return{$$typeof:b,_payload:{_status:-1,_result:g},_init:Y}},tt.memo=function(g,M){return{$$typeof:m,type:g,compare:M===void 0?null:M}},tt.startTransition=function(g){var M=F.T,X={};F.T=X;try{var H=g(),k=F.S;k!==null&&k(X,H),typeof H=="object"&&H!==null&&typeof H.then=="function"&&H.then(gt,J)}catch(rt){J(rt)}finally{F.T=M}},tt.unstable_useCacheRefresh=function(){return F.H.useCacheRefresh()},tt.use=function(g){return F.H.use(g)},tt.useActionState=function(g,M,X){return F.H.useActionState(g,M,X)},tt.useCallback=function(g,M){return F.H.useCallback(g,M)},tt.useContext=function(g){return F.H.useContext(g)},tt.useDebugValue=function(){},tt.useDeferredValue=function(g,M){return F.H.useDeferredValue(g,M)},tt.useEffect=function(g,M,X){var H=F.H;if(typeof X=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return H.useEffect(g,M)},tt.useId=function(){return F.H.useId()},tt.useImperativeHandle=function(g,M,X){return F.H.useImperativeHandle(g,M,X)},tt.useInsertionEffect=function(g,M){return F.H.useInsertionEffect(g,M)},tt.useLayoutEffect=function(g,M){return F.H.useLayoutEffect(g,M)},tt.useMemo=function(g,M){return F.H.useMemo(g,M)},tt.useOptimistic=function(g,M){return F.H.useOptimistic(g,M)},tt.useReducer=function(g,M,X){return F.H.useReducer(g,M,X)},tt.useRef=function(g){return F.H.useRef(g)},tt.useState=function(g){return F.H.useState(g)},tt.useSyncExternalStore=function(g,M,X){return F.H.useSyncExternalStore(g,M,X)},tt.useTransition=function(){return F.H.useTransition()},tt.version="19.1.0",tt}var Nd;function Er(){return Nd||(Nd=1,Jc.exports=d0()),Jc.exports}var ne=Er();const Dd=o0(ne);var Wc={exports:{}},Xa={},Fc={exports:{}},$c={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wd;function y0(){return wd||(wd=1,function(u){function a(x,Y){var J=x.length;x.push(Y);t:for(;0<J;){var gt=J-1>>>1,g=x[gt];if(0<f(g,Y))x[gt]=Y,x[J]=g,J=gt;else break t}}function s(x){return x.length===0?null:x[0]}function c(x){if(x.length===0)return null;var Y=x[0],J=x.pop();if(J!==Y){x[0]=J;t:for(var gt=0,g=x.length,M=g>>>1;gt<M;){var X=2*(gt+1)-1,H=x[X],k=X+1,rt=x[k];if(0>f(H,J))k<g&&0>f(rt,H)?(x[gt]=rt,x[k]=J,gt=k):(x[gt]=H,x[X]=J,gt=X);else if(k<g&&0>f(rt,J))x[gt]=rt,x[k]=J,gt=k;else break t}}return Y}function f(x,Y){var J=x.sortIndex-Y.sortIndex;return J!==0?J:x.id-Y.id}if(u.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;u.unstable_now=function(){return d.now()}}else{var y=Date,E=y.now();u.unstable_now=function(){return y.now()-E}}var T=[],m=[],b=1,D=null,z=3,L=!1,B=!1,j=!1,q=!1,Q=typeof setTimeout=="function"?setTimeout:null,P=typeof clearTimeout=="function"?clearTimeout:null,et=typeof setImmediate<"u"?setImmediate:null;function Rt(x){for(var Y=s(m);Y!==null;){if(Y.callback===null)c(m);else if(Y.startTime<=x)c(m),Y.sortIndex=Y.expirationTime,a(T,Y);else break;Y=s(m)}}function F(x){if(j=!1,Rt(x),!B)if(s(T)!==null)B=!0,At||(At=!0,Qt());else{var Y=s(m);Y!==null&&kt(F,Y.startTime-x)}}var At=!1,Nt=-1,qt=5,ie=-1;function Pn(){return q?!0:!(u.unstable_now()-ie<qt)}function Ye(){if(q=!1,At){var x=u.unstable_now();ie=x;var Y=!0;try{t:{B=!1,j&&(j=!1,P(Nt),Nt=-1),L=!0;var J=z;try{e:{for(Rt(x),D=s(T);D!==null&&!(D.expirationTime>x&&Pn());){var gt=D.callback;if(typeof gt=="function"){D.callback=null,z=D.priorityLevel;var g=gt(D.expirationTime<=x);if(x=u.unstable_now(),typeof g=="function"){D.callback=g,Rt(x),Y=!0;break e}D===s(T)&&c(T),Rt(x)}else c(T);D=s(T)}if(D!==null)Y=!0;else{var M=s(m);M!==null&&kt(F,M.startTime-x),Y=!1}}break t}finally{D=null,z=J,L=!1}Y=void 0}}finally{Y?Qt():At=!1}}}var Qt;if(typeof et=="function")Qt=function(){et(Ye)};else if(typeof MessageChannel<"u"){var wn=new MessageChannel,xn=wn.port2;wn.port1.onmessage=Ye,Qt=function(){xn.postMessage(null)}}else Qt=function(){Q(Ye,0)};function kt(x,Y){Nt=Q(function(){x(u.unstable_now())},Y)}u.unstable_IdlePriority=5,u.unstable_ImmediatePriority=1,u.unstable_LowPriority=4,u.unstable_NormalPriority=3,u.unstable_Profiling=null,u.unstable_UserBlockingPriority=2,u.unstable_cancelCallback=function(x){x.callback=null},u.unstable_forceFrameRate=function(x){0>x||125<x?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):qt=0<x?Math.floor(1e3/x):5},u.unstable_getCurrentPriorityLevel=function(){return z},u.unstable_next=function(x){switch(z){case 1:case 2:case 3:var Y=3;break;default:Y=z}var J=z;z=Y;try{return x()}finally{z=J}},u.unstable_requestPaint=function(){q=!0},u.unstable_runWithPriority=function(x,Y){switch(x){case 1:case 2:case 3:case 4:case 5:break;default:x=3}var J=z;z=x;try{return Y()}finally{z=J}},u.unstable_scheduleCallback=function(x,Y,J){var gt=u.unstable_now();switch(typeof J=="object"&&J!==null?(J=J.delay,J=typeof J=="number"&&0<J?gt+J:gt):J=gt,x){case 1:var g=-1;break;case 2:g=250;break;case 5:g=1073741823;break;case 4:g=1e4;break;default:g=5e3}return g=J+g,x={id:b++,callback:Y,priorityLevel:x,startTime:J,expirationTime:g,sortIndex:-1},J>gt?(x.sortIndex=J,a(m,x),s(T)===null&&x===s(m)&&(j?(P(Nt),Nt=-1):j=!0,kt(F,J-gt))):(x.sortIndex=g,a(T,x),B||L||(B=!0,At||(At=!0,Qt()))),x},u.unstable_shouldYield=Pn,u.unstable_wrapCallback=function(x){var Y=z;return function(){var J=z;z=Y;try{return x.apply(this,arguments)}finally{z=J}}}}($c)),$c}var xd;function p0(){return xd||(xd=1,Fc.exports=y0()),Fc.exports}var Pc={exports:{}},Jt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Cd;function m0(){if(Cd)return Jt;Cd=1;var u=Er();function a(T){var m="https://react.dev/errors/"+T;if(1<arguments.length){m+="?args[]="+encodeURIComponent(arguments[1]);for(var b=2;b<arguments.length;b++)m+="&args[]="+encodeURIComponent(arguments[b])}return"Minified React error #"+T+"; visit "+m+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(){}var c={d:{f:s,r:function(){throw Error(a(522))},D:s,C:s,L:s,m:s,X:s,S:s,M:s},p:0,findDOMNode:null},f=Symbol.for("react.portal");function d(T,m,b){var D=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:f,key:D==null?null:""+D,children:T,containerInfo:m,implementation:b}}var y=u.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function E(T,m){if(T==="font")return"";if(typeof m=="string")return m==="use-credentials"?m:""}return Jt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=c,Jt.createPortal=function(T,m){var b=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!m||m.nodeType!==1&&m.nodeType!==9&&m.nodeType!==11)throw Error(a(299));return d(T,m,null,b)},Jt.flushSync=function(T){var m=y.T,b=c.p;try{if(y.T=null,c.p=2,T)return T()}finally{y.T=m,c.p=b,c.d.f()}},Jt.preconnect=function(T,m){typeof T=="string"&&(m?(m=m.crossOrigin,m=typeof m=="string"?m==="use-credentials"?m:"":void 0):m=null,c.d.C(T,m))},Jt.prefetchDNS=function(T){typeof T=="string"&&c.d.D(T)},Jt.preinit=function(T,m){if(typeof T=="string"&&m&&typeof m.as=="string"){var b=m.as,D=E(b,m.crossOrigin),z=typeof m.integrity=="string"?m.integrity:void 0,L=typeof m.fetchPriority=="string"?m.fetchPriority:void 0;b==="style"?c.d.S(T,typeof m.precedence=="string"?m.precedence:void 0,{crossOrigin:D,integrity:z,fetchPriority:L}):b==="script"&&c.d.X(T,{crossOrigin:D,integrity:z,fetchPriority:L,nonce:typeof m.nonce=="string"?m.nonce:void 0})}},Jt.preinitModule=function(T,m){if(typeof T=="string")if(typeof m=="object"&&m!==null){if(m.as==null||m.as==="script"){var b=E(m.as,m.crossOrigin);c.d.M(T,{crossOrigin:b,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0})}}else m==null&&c.d.M(T)},Jt.preload=function(T,m){if(typeof T=="string"&&typeof m=="object"&&m!==null&&typeof m.as=="string"){var b=m.as,D=E(b,m.crossOrigin);c.d.L(T,b,{crossOrigin:D,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0,type:typeof m.type=="string"?m.type:void 0,fetchPriority:typeof m.fetchPriority=="string"?m.fetchPriority:void 0,referrerPolicy:typeof m.referrerPolicy=="string"?m.referrerPolicy:void 0,imageSrcSet:typeof m.imageSrcSet=="string"?m.imageSrcSet:void 0,imageSizes:typeof m.imageSizes=="string"?m.imageSizes:void 0,media:typeof m.media=="string"?m.media:void 0})}},Jt.preloadModule=function(T,m){if(typeof T=="string")if(m){var b=E(m.as,m.crossOrigin);c.d.m(T,{as:typeof m.as=="string"&&m.as!=="script"?m.as:void 0,crossOrigin:b,integrity:typeof m.integrity=="string"?m.integrity:void 0})}else c.d.m(T)},Jt.requestFormReset=function(T){c.d.r(T)},Jt.unstable_batchedUpdates=function(T,m){return T(m)},Jt.useFormState=function(T,m,b){return y.H.useFormState(T,m,b)},Jt.useFormStatus=function(){return y.H.useHostTransitionStatus()},Jt.version="19.1.0",Jt}var Ud;function g0(){if(Ud)return Pc.exports;Ud=1;function u(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(u)}catch(a){console.error(a)}}return u(),Pc.exports=m0(),Pc.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zd;function v0(){if(zd)return Xa;zd=1;var u=p0(),a=Er(),s=g0();function c(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function f(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function d(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function y(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function E(t){if(d(t)!==t)throw Error(c(188))}function T(t){var e=t.alternate;if(!e){if(e=d(t),e===null)throw Error(c(188));return e!==t?null:t}for(var n=t,l=e;;){var i=n.return;if(i===null)break;var r=i.alternate;if(r===null){if(l=i.return,l!==null){n=l;continue}break}if(i.child===r.child){for(r=i.child;r;){if(r===n)return E(i),t;if(r===l)return E(i),e;r=r.sibling}throw Error(c(188))}if(n.return!==l.return)n=i,l=r;else{for(var o=!1,h=i.child;h;){if(h===n){o=!0,n=i,l=r;break}if(h===l){o=!0,l=i,n=r;break}h=h.sibling}if(!o){for(h=r.child;h;){if(h===n){o=!0,n=r,l=i;break}if(h===l){o=!0,l=r,n=i;break}h=h.sibling}if(!o)throw Error(c(189))}}if(n.alternate!==l)throw Error(c(190))}if(n.tag!==3)throw Error(c(188));return n.stateNode.current===n?t:e}function m(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=m(t),e!==null)return e;t=t.sibling}return null}var b=Object.assign,D=Symbol.for("react.element"),z=Symbol.for("react.transitional.element"),L=Symbol.for("react.portal"),B=Symbol.for("react.fragment"),j=Symbol.for("react.strict_mode"),q=Symbol.for("react.profiler"),Q=Symbol.for("react.provider"),P=Symbol.for("react.consumer"),et=Symbol.for("react.context"),Rt=Symbol.for("react.forward_ref"),F=Symbol.for("react.suspense"),At=Symbol.for("react.suspense_list"),Nt=Symbol.for("react.memo"),qt=Symbol.for("react.lazy"),ie=Symbol.for("react.activity"),Pn=Symbol.for("react.memo_cache_sentinel"),Ye=Symbol.iterator;function Qt(t){return t===null||typeof t!="object"?null:(t=Ye&&t[Ye]||t["@@iterator"],typeof t=="function"?t:null)}var wn=Symbol.for("react.client.reference");function xn(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===wn?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case B:return"Fragment";case q:return"Profiler";case j:return"StrictMode";case F:return"Suspense";case At:return"SuspenseList";case ie:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case L:return"Portal";case et:return(t.displayName||"Context")+".Provider";case P:return(t._context.displayName||"Context")+".Consumer";case Rt:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case Nt:return e=t.displayName||null,e!==null?e:xn(t.type)||"Memo";case qt:e=t._payload,t=t._init;try{return xn(t(e))}catch{}}return null}var kt=Array.isArray,x=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Y=s.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,J={pending:!1,data:null,method:null,action:null},gt=[],g=-1;function M(t){return{current:t}}function X(t){0>g||(t.current=gt[g],gt[g]=null,g--)}function H(t,e){g++,gt[g]=t.current,t.current=e}var k=M(null),rt=M(null),$=M(null),ue=M(null);function St(t,e){switch(H($,e),H(rt,t),H(k,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?Ph(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=Ph(e),t=Ih(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}X(k),H(k,t)}function nn(){X(k),X(rt),X($)}function Uu(t){t.memoizedState!==null&&H(ue,t);var e=k.current,n=Ih(e,t.type);e!==n&&(H(rt,t),H(k,n))}function Wa(t){rt.current===t&&(X(k),X(rt)),ue.current===t&&(X(ue),Ba._currentValue=J)}var zu=Object.prototype.hasOwnProperty,Mu=u.unstable_scheduleCallback,Bu=u.unstable_cancelCallback,Gy=u.unstable_shouldYield,Vy=u.unstable_requestPaint,Ce=u.unstable_now,Qy=u.unstable_getCurrentPriorityLevel,xr=u.unstable_ImmediatePriority,Cr=u.unstable_UserBlockingPriority,Fa=u.unstable_NormalPriority,ky=u.unstable_LowPriority,Ur=u.unstable_IdlePriority,Zy=u.log,Ky=u.unstable_setDisableYieldValue,Vl=null,se=null;function ln(t){if(typeof Zy=="function"&&Ky(t),se&&typeof se.setStrictMode=="function")try{se.setStrictMode(Vl,t)}catch{}}var ce=Math.clz32?Math.clz32:Fy,Jy=Math.log,Wy=Math.LN2;function Fy(t){return t>>>=0,t===0?32:31-(Jy(t)/Wy|0)|0}var $a=256,Pa=4194304;function Cn(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Ia(t,e,n){var l=t.pendingLanes;if(l===0)return 0;var i=0,r=t.suspendedLanes,o=t.pingedLanes;t=t.warmLanes;var h=l&134217727;return h!==0?(l=h&~r,l!==0?i=Cn(l):(o&=h,o!==0?i=Cn(o):n||(n=h&~t,n!==0&&(i=Cn(n))))):(h=l&~r,h!==0?i=Cn(h):o!==0?i=Cn(o):n||(n=l&~t,n!==0&&(i=Cn(n)))),i===0?0:e!==0&&e!==i&&(e&r)===0&&(r=i&-i,n=e&-e,r>=n||r===32&&(n&4194048)!==0)?e:i}function Ql(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function $y(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function zr(){var t=$a;return $a<<=1,($a&4194048)===0&&($a=256),t}function Mr(){var t=Pa;return Pa<<=1,(Pa&62914560)===0&&(Pa=4194304),t}function qu(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function kl(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function Py(t,e,n,l,i,r){var o=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var h=t.entanglements,p=t.expirationTimes,A=t.hiddenUpdates;for(n=o&~n;0<n;){var w=31-ce(n),U=1<<w;h[w]=0,p[w]=-1;var O=A[w];if(O!==null)for(A[w]=null,w=0;w<O.length;w++){var R=O[w];R!==null&&(R.lane&=-536870913)}n&=~U}l!==0&&Br(t,l,0),r!==0&&i===0&&t.tag!==0&&(t.suspendedLanes|=r&~(o&~e))}function Br(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var l=31-ce(e);t.entangledLanes|=e,t.entanglements[l]=t.entanglements[l]|1073741824|n&4194090}function qr(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var l=31-ce(n),i=1<<l;i&e|t[l]&e&&(t[l]|=e),n&=~i}}function ju(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function Lu(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function jr(){var t=Y.p;return t!==0?t:(t=window.event,t===void 0?32:vd(t.type))}function Iy(t,e){var n=Y.p;try{return Y.p=t,e()}finally{Y.p=n}}var an=Math.random().toString(36).slice(2),Zt="__reactFiber$"+an,$t="__reactProps$"+an,In="__reactContainer$"+an,Hu="__reactEvents$"+an,tp="__reactListeners$"+an,ep="__reactHandles$"+an,Lr="__reactResources$"+an,Zl="__reactMarker$"+an;function Yu(t){delete t[Zt],delete t[$t],delete t[Hu],delete t[tp],delete t[ep]}function tl(t){var e=t[Zt];if(e)return e;for(var n=t.parentNode;n;){if(e=n[In]||n[Zt]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=ld(t);t!==null;){if(n=t[Zt])return n;t=ld(t)}return e}t=n,n=t.parentNode}return null}function el(t){if(t=t[Zt]||t[In]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Kl(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(c(33))}function nl(t){var e=t[Lr];return e||(e=t[Lr]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function jt(t){t[Zl]=!0}var Hr=new Set,Yr={};function Un(t,e){ll(t,e),ll(t+"Capture",e)}function ll(t,e){for(Yr[t]=e,t=0;t<e.length;t++)Hr.add(e[t])}var np=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Xr={},Gr={};function lp(t){return zu.call(Gr,t)?!0:zu.call(Xr,t)?!1:np.test(t)?Gr[t]=!0:(Xr[t]=!0,!1)}function ti(t,e,n){if(lp(e))if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var l=e.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+n)}}function ei(t,e,n){if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+n)}}function Xe(t,e,n,l){if(l===null)t.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttributeNS(e,n,""+l)}}var Xu,Vr;function al(t){if(Xu===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);Xu=e&&e[1]||"",Vr=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Xu+t+Vr}var Gu=!1;function Vu(t,e){if(!t||Gu)return"";Gu=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(e){var U=function(){throw Error()};if(Object.defineProperty(U.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(U,[])}catch(R){var O=R}Reflect.construct(t,[],U)}else{try{U.call()}catch(R){O=R}t.call(U.prototype)}}else{try{throw Error()}catch(R){O=R}(U=t())&&typeof U.catch=="function"&&U.catch(function(){})}}catch(R){if(R&&O&&typeof R.stack=="string")return[R.stack,O.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var r=l.DetermineComponentFrameRoot(),o=r[0],h=r[1];if(o&&h){var p=o.split(`
`),A=h.split(`
`);for(i=l=0;l<p.length&&!p[l].includes("DetermineComponentFrameRoot");)l++;for(;i<A.length&&!A[i].includes("DetermineComponentFrameRoot");)i++;if(l===p.length||i===A.length)for(l=p.length-1,i=A.length-1;1<=l&&0<=i&&p[l]!==A[i];)i--;for(;1<=l&&0<=i;l--,i--)if(p[l]!==A[i]){if(l!==1||i!==1)do if(l--,i--,0>i||p[l]!==A[i]){var w=`
`+p[l].replace(" at new "," at ");return t.displayName&&w.includes("<anonymous>")&&(w=w.replace("<anonymous>",t.displayName)),w}while(1<=l&&0<=i);break}}}finally{Gu=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?al(n):""}function ap(t){switch(t.tag){case 26:case 27:case 5:return al(t.type);case 16:return al("Lazy");case 13:return al("Suspense");case 19:return al("SuspenseList");case 0:case 15:return Vu(t.type,!1);case 11:return Vu(t.type.render,!1);case 1:return Vu(t.type,!0);case 31:return al("Activity");default:return""}}function Qr(t){try{var e="";do e+=ap(t),t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function me(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function kr(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function ip(t){var e=kr(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),l=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,r=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return i.call(this)},set:function(o){l=""+o,r.call(this,o)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(o){l=""+o},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function ni(t){t._valueTracker||(t._valueTracker=ip(t))}function Zr(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),l="";return t&&(l=kr(t)?t.checked?"true":"false":t.value),t=l,t!==n?(e.setValue(t),!0):!1}function li(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var up=/[\n"\\]/g;function ge(t){return t.replace(up,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Qu(t,e,n,l,i,r,o,h){t.name="",o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"?t.type=o:t.removeAttribute("type"),e!=null?o==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+me(e)):t.value!==""+me(e)&&(t.value=""+me(e)):o!=="submit"&&o!=="reset"||t.removeAttribute("value"),e!=null?ku(t,o,me(e)):n!=null?ku(t,o,me(n)):l!=null&&t.removeAttribute("value"),i==null&&r!=null&&(t.defaultChecked=!!r),i!=null&&(t.checked=i&&typeof i!="function"&&typeof i!="symbol"),h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?t.name=""+me(h):t.removeAttribute("name")}function Kr(t,e,n,l,i,r,o,h){if(r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"&&(t.type=r),e!=null||n!=null){if(!(r!=="submit"&&r!=="reset"||e!=null))return;n=n!=null?""+me(n):"",e=e!=null?""+me(e):n,h||e===t.value||(t.value=e),t.defaultValue=e}l=l??i,l=typeof l!="function"&&typeof l!="symbol"&&!!l,t.checked=h?t.checked:!!l,t.defaultChecked=!!l,o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"&&(t.name=o)}function ku(t,e,n){e==="number"&&li(t.ownerDocument)===t||t.defaultValue===""+n||(t.defaultValue=""+n)}function il(t,e,n,l){if(t=t.options,e){e={};for(var i=0;i<n.length;i++)e["$"+n[i]]=!0;for(n=0;n<t.length;n++)i=e.hasOwnProperty("$"+t[n].value),t[n].selected!==i&&(t[n].selected=i),i&&l&&(t[n].defaultSelected=!0)}else{for(n=""+me(n),e=null,i=0;i<t.length;i++){if(t[i].value===n){t[i].selected=!0,l&&(t[i].defaultSelected=!0);return}e!==null||t[i].disabled||(e=t[i])}e!==null&&(e.selected=!0)}}function Jr(t,e,n){if(e!=null&&(e=""+me(e),e!==t.value&&(t.value=e),n==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=n!=null?""+me(n):""}function Wr(t,e,n,l){if(e==null){if(l!=null){if(n!=null)throw Error(c(92));if(kt(l)){if(1<l.length)throw Error(c(93));l=l[0]}n=l}n==null&&(n=""),e=n}n=me(e),t.defaultValue=n,l=t.textContent,l===n&&l!==""&&l!==null&&(t.value=l)}function ul(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var sp=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Fr(t,e,n){var l=e.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?l?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":l?t.setProperty(e,n):typeof n!="number"||n===0||sp.has(e)?e==="float"?t.cssFloat=n:t[e]=(""+n).trim():t[e]=n+"px"}function $r(t,e,n){if(e!=null&&typeof e!="object")throw Error(c(62));if(t=t.style,n!=null){for(var l in n)!n.hasOwnProperty(l)||e!=null&&e.hasOwnProperty(l)||(l.indexOf("--")===0?t.setProperty(l,""):l==="float"?t.cssFloat="":t[l]="");for(var i in e)l=e[i],e.hasOwnProperty(i)&&n[i]!==l&&Fr(t,i,l)}else for(var r in e)e.hasOwnProperty(r)&&Fr(t,r,e[r])}function Zu(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var cp=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),rp=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function ai(t){return rp.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Ku=null;function Ju(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var sl=null,cl=null;function Pr(t){var e=el(t);if(e&&(t=e.stateNode)){var n=t[$t]||null;t:switch(t=e.stateNode,e.type){case"input":if(Qu(t,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+ge(""+e)+'"][type="radio"]'),e=0;e<n.length;e++){var l=n[e];if(l!==t&&l.form===t.form){var i=l[$t]||null;if(!i)throw Error(c(90));Qu(l,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(e=0;e<n.length;e++)l=n[e],l.form===t.form&&Zr(l)}break t;case"textarea":Jr(t,n.value,n.defaultValue);break t;case"select":e=n.value,e!=null&&il(t,!!n.multiple,e,!1)}}}var Wu=!1;function Ir(t,e,n){if(Wu)return t(e,n);Wu=!0;try{var l=t(e);return l}finally{if(Wu=!1,(sl!==null||cl!==null)&&(Vi(),sl&&(e=sl,t=cl,cl=sl=null,Pr(e),t)))for(e=0;e<t.length;e++)Pr(t[e])}}function Jl(t,e){var n=t.stateNode;if(n===null)return null;var l=n[$t]||null;if(l===null)return null;n=l[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(t=t.type,l=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!l;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(c(231,e,typeof n));return n}var Ge=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Fu=!1;if(Ge)try{var Wl={};Object.defineProperty(Wl,"passive",{get:function(){Fu=!0}}),window.addEventListener("test",Wl,Wl),window.removeEventListener("test",Wl,Wl)}catch{Fu=!1}var un=null,$u=null,ii=null;function to(){if(ii)return ii;var t,e=$u,n=e.length,l,i="value"in un?un.value:un.textContent,r=i.length;for(t=0;t<n&&e[t]===i[t];t++);var o=n-t;for(l=1;l<=o&&e[n-l]===i[r-l];l++);return ii=i.slice(t,1<l?1-l:void 0)}function ui(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function si(){return!0}function eo(){return!1}function Pt(t){function e(n,l,i,r,o){this._reactName=n,this._targetInst=i,this.type=l,this.nativeEvent=r,this.target=o,this.currentTarget=null;for(var h in t)t.hasOwnProperty(h)&&(n=t[h],this[h]=n?n(r):r[h]);return this.isDefaultPrevented=(r.defaultPrevented!=null?r.defaultPrevented:r.returnValue===!1)?si:eo,this.isPropagationStopped=eo,this}return b(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=si)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=si)},persist:function(){},isPersistent:si}),e}var zn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ci=Pt(zn),Fl=b({},zn,{view:0,detail:0}),op=Pt(Fl),Pu,Iu,$l,ri=b({},Fl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:es,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==$l&&($l&&t.type==="mousemove"?(Pu=t.screenX-$l.screenX,Iu=t.screenY-$l.screenY):Iu=Pu=0,$l=t),Pu)},movementY:function(t){return"movementY"in t?t.movementY:Iu}}),no=Pt(ri),fp=b({},ri,{dataTransfer:0}),hp=Pt(fp),dp=b({},Fl,{relatedTarget:0}),ts=Pt(dp),yp=b({},zn,{animationName:0,elapsedTime:0,pseudoElement:0}),pp=Pt(yp),mp=b({},zn,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),gp=Pt(mp),vp=b({},zn,{data:0}),lo=Pt(vp),bp={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Sp={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ep={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function _p(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Ep[t])?!!e[t]:!1}function es(){return _p}var Ap=b({},Fl,{key:function(t){if(t.key){var e=bp[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=ui(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Sp[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:es,charCode:function(t){return t.type==="keypress"?ui(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?ui(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Tp=Pt(Ap),Op=b({},ri,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),ao=Pt(Op),Rp=b({},Fl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:es}),Np=Pt(Rp),Dp=b({},zn,{propertyName:0,elapsedTime:0,pseudoElement:0}),wp=Pt(Dp),xp=b({},ri,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Cp=Pt(xp),Up=b({},zn,{newState:0,oldState:0}),zp=Pt(Up),Mp=[9,13,27,32],ns=Ge&&"CompositionEvent"in window,Pl=null;Ge&&"documentMode"in document&&(Pl=document.documentMode);var Bp=Ge&&"TextEvent"in window&&!Pl,io=Ge&&(!ns||Pl&&8<Pl&&11>=Pl),uo=" ",so=!1;function co(t,e){switch(t){case"keyup":return Mp.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ro(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var rl=!1;function qp(t,e){switch(t){case"compositionend":return ro(e);case"keypress":return e.which!==32?null:(so=!0,uo);case"textInput":return t=e.data,t===uo&&so?null:t;default:return null}}function jp(t,e){if(rl)return t==="compositionend"||!ns&&co(t,e)?(t=to(),ii=$u=un=null,rl=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return io&&e.locale!=="ko"?null:e.data;default:return null}}var Lp={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function oo(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Lp[t.type]:e==="textarea"}function fo(t,e,n,l){sl?cl?cl.push(l):cl=[l]:sl=l,e=Wi(e,"onChange"),0<e.length&&(n=new ci("onChange","change",null,n,l),t.push({event:n,listeners:e}))}var Il=null,ta=null;function Hp(t){Kh(t,0)}function oi(t){var e=Kl(t);if(Zr(e))return t}function ho(t,e){if(t==="change")return e}var yo=!1;if(Ge){var ls;if(Ge){var as="oninput"in document;if(!as){var po=document.createElement("div");po.setAttribute("oninput","return;"),as=typeof po.oninput=="function"}ls=as}else ls=!1;yo=ls&&(!document.documentMode||9<document.documentMode)}function mo(){Il&&(Il.detachEvent("onpropertychange",go),ta=Il=null)}function go(t){if(t.propertyName==="value"&&oi(ta)){var e=[];fo(e,ta,t,Ju(t)),Ir(Hp,e)}}function Yp(t,e,n){t==="focusin"?(mo(),Il=e,ta=n,Il.attachEvent("onpropertychange",go)):t==="focusout"&&mo()}function Xp(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return oi(ta)}function Gp(t,e){if(t==="click")return oi(e)}function Vp(t,e){if(t==="input"||t==="change")return oi(e)}function Qp(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var re=typeof Object.is=="function"?Object.is:Qp;function ea(t,e){if(re(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),l=Object.keys(e);if(n.length!==l.length)return!1;for(l=0;l<n.length;l++){var i=n[l];if(!zu.call(e,i)||!re(t[i],e[i]))return!1}return!0}function vo(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function bo(t,e){var n=vo(t);t=0;for(var l;n;){if(n.nodeType===3){if(l=t+n.textContent.length,t<=e&&l>=e)return{node:n,offset:e-t};t=l}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=vo(n)}}function So(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?So(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Eo(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=li(t.document);e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=li(t.document)}return e}function is(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var kp=Ge&&"documentMode"in document&&11>=document.documentMode,ol=null,us=null,na=null,ss=!1;function _o(t,e,n){var l=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ss||ol==null||ol!==li(l)||(l=ol,"selectionStart"in l&&is(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),na&&ea(na,l)||(na=l,l=Wi(us,"onSelect"),0<l.length&&(e=new ci("onSelect","select",null,e,n),t.push({event:e,listeners:l}),e.target=ol)))}function Mn(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var fl={animationend:Mn("Animation","AnimationEnd"),animationiteration:Mn("Animation","AnimationIteration"),animationstart:Mn("Animation","AnimationStart"),transitionrun:Mn("Transition","TransitionRun"),transitionstart:Mn("Transition","TransitionStart"),transitioncancel:Mn("Transition","TransitionCancel"),transitionend:Mn("Transition","TransitionEnd")},cs={},Ao={};Ge&&(Ao=document.createElement("div").style,"AnimationEvent"in window||(delete fl.animationend.animation,delete fl.animationiteration.animation,delete fl.animationstart.animation),"TransitionEvent"in window||delete fl.transitionend.transition);function Bn(t){if(cs[t])return cs[t];if(!fl[t])return t;var e=fl[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in Ao)return cs[t]=e[n];return t}var To=Bn("animationend"),Oo=Bn("animationiteration"),Ro=Bn("animationstart"),Zp=Bn("transitionrun"),Kp=Bn("transitionstart"),Jp=Bn("transitioncancel"),No=Bn("transitionend"),Do=new Map,rs="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");rs.push("scrollEnd");function Re(t,e){Do.set(t,e),Un(e,[t])}var wo=new WeakMap;function ve(t,e){if(typeof t=="object"&&t!==null){var n=wo.get(t);return n!==void 0?n:(e={value:t,source:e,stack:Qr(e)},wo.set(t,e),e)}return{value:t,source:e,stack:Qr(e)}}var be=[],hl=0,os=0;function fi(){for(var t=hl,e=os=hl=0;e<t;){var n=be[e];be[e++]=null;var l=be[e];be[e++]=null;var i=be[e];be[e++]=null;var r=be[e];if(be[e++]=null,l!==null&&i!==null){var o=l.pending;o===null?i.next=i:(i.next=o.next,o.next=i),l.pending=i}r!==0&&xo(n,i,r)}}function hi(t,e,n,l){be[hl++]=t,be[hl++]=e,be[hl++]=n,be[hl++]=l,os|=l,t.lanes|=l,t=t.alternate,t!==null&&(t.lanes|=l)}function fs(t,e,n,l){return hi(t,e,n,l),di(t)}function dl(t,e){return hi(t,null,null,e),di(t)}function xo(t,e,n){t.lanes|=n;var l=t.alternate;l!==null&&(l.lanes|=n);for(var i=!1,r=t.return;r!==null;)r.childLanes|=n,l=r.alternate,l!==null&&(l.childLanes|=n),r.tag===22&&(t=r.stateNode,t===null||t._visibility&1||(i=!0)),t=r,r=r.return;return t.tag===3?(r=t.stateNode,i&&e!==null&&(i=31-ce(n),t=r.hiddenUpdates,l=t[i],l===null?t[i]=[e]:l.push(e),e.lane=n|536870912),r):null}function di(t){if(50<Na)throw Na=0,gc=null,Error(c(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var yl={};function Wp(t,e,n,l){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function oe(t,e,n,l){return new Wp(t,e,n,l)}function hs(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Ve(t,e){var n=t.alternate;return n===null?(n=oe(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&65011712,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n.refCleanup=t.refCleanup,n}function Co(t,e){t.flags&=65011714;var n=t.alternate;return n===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function yi(t,e,n,l,i,r){var o=0;if(l=t,typeof t=="function")hs(t)&&(o=1);else if(typeof t=="string")o=$m(t,n,k.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case ie:return t=oe(31,n,e,i),t.elementType=ie,t.lanes=r,t;case B:return qn(n.children,i,r,e);case j:o=8,i|=24;break;case q:return t=oe(12,n,e,i|2),t.elementType=q,t.lanes=r,t;case F:return t=oe(13,n,e,i),t.elementType=F,t.lanes=r,t;case At:return t=oe(19,n,e,i),t.elementType=At,t.lanes=r,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case Q:case et:o=10;break t;case P:o=9;break t;case Rt:o=11;break t;case Nt:o=14;break t;case qt:o=16,l=null;break t}o=29,n=Error(c(130,t===null?"null":typeof t,"")),l=null}return e=oe(o,n,e,i),e.elementType=t,e.type=l,e.lanes=r,e}function qn(t,e,n,l){return t=oe(7,t,l,e),t.lanes=n,t}function ds(t,e,n){return t=oe(6,t,null,e),t.lanes=n,t}function ys(t,e,n){return e=oe(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var pl=[],ml=0,pi=null,mi=0,Se=[],Ee=0,jn=null,Qe=1,ke="";function Ln(t,e){pl[ml++]=mi,pl[ml++]=pi,pi=t,mi=e}function Uo(t,e,n){Se[Ee++]=Qe,Se[Ee++]=ke,Se[Ee++]=jn,jn=t;var l=Qe;t=ke;var i=32-ce(l)-1;l&=~(1<<i),n+=1;var r=32-ce(e)+i;if(30<r){var o=i-i%5;r=(l&(1<<o)-1).toString(32),l>>=o,i-=o,Qe=1<<32-ce(e)+i|n<<i|l,ke=r+t}else Qe=1<<r|n<<i|l,ke=t}function ps(t){t.return!==null&&(Ln(t,1),Uo(t,1,0))}function ms(t){for(;t===pi;)pi=pl[--ml],pl[ml]=null,mi=pl[--ml],pl[ml]=null;for(;t===jn;)jn=Se[--Ee],Se[Ee]=null,ke=Se[--Ee],Se[Ee]=null,Qe=Se[--Ee],Se[Ee]=null}var Ft=null,Tt=null,ft=!1,Hn=null,Ue=!1,gs=Error(c(519));function Yn(t){var e=Error(c(418,""));throw ia(ve(e,t)),gs}function zo(t){var e=t.stateNode,n=t.type,l=t.memoizedProps;switch(e[Zt]=t,e[$t]=l,n){case"dialog":it("cancel",e),it("close",e);break;case"iframe":case"object":case"embed":it("load",e);break;case"video":case"audio":for(n=0;n<wa.length;n++)it(wa[n],e);break;case"source":it("error",e);break;case"img":case"image":case"link":it("error",e),it("load",e);break;case"details":it("toggle",e);break;case"input":it("invalid",e),Kr(e,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),ni(e);break;case"select":it("invalid",e);break;case"textarea":it("invalid",e),Wr(e,l.value,l.defaultValue,l.children),ni(e)}n=l.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||e.textContent===""+n||l.suppressHydrationWarning===!0||$h(e.textContent,n)?(l.popover!=null&&(it("beforetoggle",e),it("toggle",e)),l.onScroll!=null&&it("scroll",e),l.onScrollEnd!=null&&it("scrollend",e),l.onClick!=null&&(e.onclick=Fi),e=!0):e=!1,e||Yn(t)}function Mo(t){for(Ft=t.return;Ft;)switch(Ft.tag){case 5:case 13:Ue=!1;return;case 27:case 3:Ue=!0;return;default:Ft=Ft.return}}function la(t){if(t!==Ft)return!1;if(!ft)return Mo(t),ft=!0,!1;var e=t.tag,n;if((n=e!==3&&e!==27)&&((n=e===5)&&(n=t.type,n=!(n!=="form"&&n!=="button")||zc(t.type,t.memoizedProps)),n=!n),n&&Tt&&Yn(t),Mo(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(c(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(n=t.data,n==="/$"){if(e===0){Tt=De(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++;t=t.nextSibling}Tt=null}}else e===27?(e=Tt,_n(t.type)?(t=jc,jc=null,Tt=t):Tt=e):Tt=Ft?De(t.stateNode.nextSibling):null;return!0}function aa(){Tt=Ft=null,ft=!1}function Bo(){var t=Hn;return t!==null&&(ee===null?ee=t:ee.push.apply(ee,t),Hn=null),t}function ia(t){Hn===null?Hn=[t]:Hn.push(t)}var vs=M(null),Xn=null,Ze=null;function sn(t,e,n){H(vs,e._currentValue),e._currentValue=n}function Ke(t){t._currentValue=vs.current,X(vs)}function bs(t,e,n){for(;t!==null;){var l=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,l!==null&&(l.childLanes|=e)):l!==null&&(l.childLanes&e)!==e&&(l.childLanes|=e),t===n)break;t=t.return}}function Ss(t,e,n,l){var i=t.child;for(i!==null&&(i.return=t);i!==null;){var r=i.dependencies;if(r!==null){var o=i.child;r=r.firstContext;t:for(;r!==null;){var h=r;r=i;for(var p=0;p<e.length;p++)if(h.context===e[p]){r.lanes|=n,h=r.alternate,h!==null&&(h.lanes|=n),bs(r.return,n,t),l||(o=null);break t}r=h.next}}else if(i.tag===18){if(o=i.return,o===null)throw Error(c(341));o.lanes|=n,r=o.alternate,r!==null&&(r.lanes|=n),bs(o,n,t),o=null}else o=i.child;if(o!==null)o.return=i;else for(o=i;o!==null;){if(o===t){o=null;break}if(i=o.sibling,i!==null){i.return=o.return,o=i;break}o=o.return}i=o}}function ua(t,e,n,l){t=null;for(var i=e,r=!1;i!==null;){if(!r){if((i.flags&524288)!==0)r=!0;else if((i.flags&262144)!==0)break}if(i.tag===10){var o=i.alternate;if(o===null)throw Error(c(387));if(o=o.memoizedProps,o!==null){var h=i.type;re(i.pendingProps.value,o.value)||(t!==null?t.push(h):t=[h])}}else if(i===ue.current){if(o=i.alternate,o===null)throw Error(c(387));o.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(t!==null?t.push(Ba):t=[Ba])}i=i.return}t!==null&&Ss(e,t,n,l),e.flags|=262144}function gi(t){for(t=t.firstContext;t!==null;){if(!re(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Gn(t){Xn=t,Ze=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function Kt(t){return qo(Xn,t)}function vi(t,e){return Xn===null&&Gn(t),qo(t,e)}function qo(t,e){var n=e._currentValue;if(e={context:e,memoizedValue:n,next:null},Ze===null){if(t===null)throw Error(c(308));Ze=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Ze=Ze.next=e;return n}var Fp=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(n,l){t.push(l)}};this.abort=function(){e.aborted=!0,t.forEach(function(n){return n()})}},$p=u.unstable_scheduleCallback,Pp=u.unstable_NormalPriority,Mt={$$typeof:et,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Es(){return{controller:new Fp,data:new Map,refCount:0}}function sa(t){t.refCount--,t.refCount===0&&$p(Pp,function(){t.controller.abort()})}var ca=null,_s=0,gl=0,vl=null;function Ip(t,e){if(ca===null){var n=ca=[];_s=0,gl=Tc(),vl={status:"pending",value:void 0,then:function(l){n.push(l)}}}return _s++,e.then(jo,jo),e}function jo(){if(--_s===0&&ca!==null){vl!==null&&(vl.status="fulfilled");var t=ca;ca=null,gl=0,vl=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function tm(t,e){var n=[],l={status:"pending",value:null,reason:null,then:function(i){n.push(i)}};return t.then(function(){l.status="fulfilled",l.value=e;for(var i=0;i<n.length;i++)(0,n[i])(e)},function(i){for(l.status="rejected",l.reason=i,i=0;i<n.length;i++)(0,n[i])(void 0)}),l}var Lo=x.S;x.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&Ip(t,e),Lo!==null&&Lo(t,e)};var Vn=M(null);function As(){var t=Vn.current;return t!==null?t:bt.pooledCache}function bi(t,e){e===null?H(Vn,Vn.current):H(Vn,e.pool)}function Ho(){var t=As();return t===null?null:{parent:Mt._currentValue,pool:t}}var ra=Error(c(460)),Yo=Error(c(474)),Si=Error(c(542)),Ts={then:function(){}};function Xo(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Ei(){}function Go(t,e,n){switch(n=t[n],n===void 0?t.push(e):n!==e&&(e.then(Ei,Ei),e=n),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Qo(t),t;default:if(typeof e.status=="string")e.then(Ei,Ei);else{if(t=bt,t!==null&&100<t.shellSuspendCounter)throw Error(c(482));t=e,t.status="pending",t.then(function(l){if(e.status==="pending"){var i=e;i.status="fulfilled",i.value=l}},function(l){if(e.status==="pending"){var i=e;i.status="rejected",i.reason=l}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Qo(t),t}throw oa=e,ra}}var oa=null;function Vo(){if(oa===null)throw Error(c(459));var t=oa;return oa=null,t}function Qo(t){if(t===ra||t===Si)throw Error(c(483))}var cn=!1;function Os(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Rs(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function rn(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function on(t,e,n){var l=t.updateQueue;if(l===null)return null;if(l=l.shared,(ht&2)!==0){var i=l.pending;return i===null?e.next=e:(e.next=i.next,i.next=e),l.pending=e,e=di(t),xo(t,null,n),e}return hi(t,l,e,n),di(t)}function fa(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194048)!==0)){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,qr(t,n)}}function Ns(t,e){var n=t.updateQueue,l=t.alternate;if(l!==null&&(l=l.updateQueue,n===l)){var i=null,r=null;if(n=n.firstBaseUpdate,n!==null){do{var o={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};r===null?i=r=o:r=r.next=o,n=n.next}while(n!==null);r===null?i=r=e:r=r.next=e}else i=r=e;n={baseState:l.baseState,firstBaseUpdate:i,lastBaseUpdate:r,shared:l.shared,callbacks:l.callbacks},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}var Ds=!1;function ha(){if(Ds){var t=vl;if(t!==null)throw t}}function da(t,e,n,l){Ds=!1;var i=t.updateQueue;cn=!1;var r=i.firstBaseUpdate,o=i.lastBaseUpdate,h=i.shared.pending;if(h!==null){i.shared.pending=null;var p=h,A=p.next;p.next=null,o===null?r=A:o.next=A,o=p;var w=t.alternate;w!==null&&(w=w.updateQueue,h=w.lastBaseUpdate,h!==o&&(h===null?w.firstBaseUpdate=A:h.next=A,w.lastBaseUpdate=p))}if(r!==null){var U=i.baseState;o=0,w=A=p=null,h=r;do{var O=h.lane&-536870913,R=O!==h.lane;if(R?(ct&O)===O:(l&O)===O){O!==0&&O===gl&&(Ds=!0),w!==null&&(w=w.next={lane:0,tag:h.tag,payload:h.payload,callback:null,next:null});t:{var W=t,Z=h;O=e;var mt=n;switch(Z.tag){case 1:if(W=Z.payload,typeof W=="function"){U=W.call(mt,U,O);break t}U=W;break t;case 3:W.flags=W.flags&-65537|128;case 0:if(W=Z.payload,O=typeof W=="function"?W.call(mt,U,O):W,O==null)break t;U=b({},U,O);break t;case 2:cn=!0}}O=h.callback,O!==null&&(t.flags|=64,R&&(t.flags|=8192),R=i.callbacks,R===null?i.callbacks=[O]:R.push(O))}else R={lane:O,tag:h.tag,payload:h.payload,callback:h.callback,next:null},w===null?(A=w=R,p=U):w=w.next=R,o|=O;if(h=h.next,h===null){if(h=i.shared.pending,h===null)break;R=h,h=R.next,R.next=null,i.lastBaseUpdate=R,i.shared.pending=null}}while(!0);w===null&&(p=U),i.baseState=p,i.firstBaseUpdate=A,i.lastBaseUpdate=w,r===null&&(i.shared.lanes=0),vn|=o,t.lanes=o,t.memoizedState=U}}function ko(t,e){if(typeof t!="function")throw Error(c(191,t));t.call(e)}function Zo(t,e){var n=t.callbacks;if(n!==null)for(t.callbacks=null,t=0;t<n.length;t++)ko(n[t],e)}var bl=M(null),_i=M(0);function Ko(t,e){t=tn,H(_i,t),H(bl,e),tn=t|e.baseLanes}function ws(){H(_i,tn),H(bl,bl.current)}function xs(){tn=_i.current,X(bl),X(_i)}var fn=0,nt=null,yt=null,Ct=null,Ai=!1,Sl=!1,Qn=!1,Ti=0,ya=0,El=null,em=0;function Dt(){throw Error(c(321))}function Cs(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!re(t[n],e[n]))return!1;return!0}function Us(t,e,n,l,i,r){return fn=r,nt=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,x.H=t===null||t.memoizedState===null?Cf:Uf,Qn=!1,r=n(l,i),Qn=!1,Sl&&(r=Wo(e,n,l,i)),Jo(t),r}function Jo(t){x.H=xi;var e=yt!==null&&yt.next!==null;if(fn=0,Ct=yt=nt=null,Ai=!1,ya=0,El=null,e)throw Error(c(300));t===null||Lt||(t=t.dependencies,t!==null&&gi(t)&&(Lt=!0))}function Wo(t,e,n,l){nt=t;var i=0;do{if(Sl&&(El=null),ya=0,Sl=!1,25<=i)throw Error(c(301));if(i+=1,Ct=yt=null,t.updateQueue!=null){var r=t.updateQueue;r.lastEffect=null,r.events=null,r.stores=null,r.memoCache!=null&&(r.memoCache.index=0)}x.H=cm,r=e(n,l)}while(Sl);return r}function nm(){var t=x.H,e=t.useState()[0];return e=typeof e.then=="function"?pa(e):e,t=t.useState()[0],(yt!==null?yt.memoizedState:null)!==t&&(nt.flags|=1024),e}function zs(){var t=Ti!==0;return Ti=0,t}function Ms(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function Bs(t){if(Ai){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}Ai=!1}fn=0,Ct=yt=nt=null,Sl=!1,ya=Ti=0,El=null}function It(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ct===null?nt.memoizedState=Ct=t:Ct=Ct.next=t,Ct}function Ut(){if(yt===null){var t=nt.alternate;t=t!==null?t.memoizedState:null}else t=yt.next;var e=Ct===null?nt.memoizedState:Ct.next;if(e!==null)Ct=e,yt=t;else{if(t===null)throw nt.alternate===null?Error(c(467)):Error(c(310));yt=t,t={memoizedState:yt.memoizedState,baseState:yt.baseState,baseQueue:yt.baseQueue,queue:yt.queue,next:null},Ct===null?nt.memoizedState=Ct=t:Ct=Ct.next=t}return Ct}function qs(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function pa(t){var e=ya;return ya+=1,El===null&&(El=[]),t=Go(El,t,e),e=nt,(Ct===null?e.memoizedState:Ct.next)===null&&(e=e.alternate,x.H=e===null||e.memoizedState===null?Cf:Uf),t}function Oi(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return pa(t);if(t.$$typeof===et)return Kt(t)}throw Error(c(438,String(t)))}function js(t){var e=null,n=nt.updateQueue;if(n!==null&&(e=n.memoCache),e==null){var l=nt.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(e={data:l.data.map(function(i){return i.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),n===null&&(n=qs(),nt.updateQueue=n),n.memoCache=e,n=e.data[e.index],n===void 0)for(n=e.data[e.index]=Array(t),l=0;l<t;l++)n[l]=Pn;return e.index++,n}function Je(t,e){return typeof e=="function"?e(t):e}function Ri(t){var e=Ut();return Ls(e,yt,t)}function Ls(t,e,n){var l=t.queue;if(l===null)throw Error(c(311));l.lastRenderedReducer=n;var i=t.baseQueue,r=l.pending;if(r!==null){if(i!==null){var o=i.next;i.next=r.next,r.next=o}e.baseQueue=i=r,l.pending=null}if(r=t.baseState,i===null)t.memoizedState=r;else{e=i.next;var h=o=null,p=null,A=e,w=!1;do{var U=A.lane&-536870913;if(U!==A.lane?(ct&U)===U:(fn&U)===U){var O=A.revertLane;if(O===0)p!==null&&(p=p.next={lane:0,revertLane:0,action:A.action,hasEagerState:A.hasEagerState,eagerState:A.eagerState,next:null}),U===gl&&(w=!0);else if((fn&O)===O){A=A.next,O===gl&&(w=!0);continue}else U={lane:0,revertLane:A.revertLane,action:A.action,hasEagerState:A.hasEagerState,eagerState:A.eagerState,next:null},p===null?(h=p=U,o=r):p=p.next=U,nt.lanes|=O,vn|=O;U=A.action,Qn&&n(r,U),r=A.hasEagerState?A.eagerState:n(r,U)}else O={lane:U,revertLane:A.revertLane,action:A.action,hasEagerState:A.hasEagerState,eagerState:A.eagerState,next:null},p===null?(h=p=O,o=r):p=p.next=O,nt.lanes|=U,vn|=U;A=A.next}while(A!==null&&A!==e);if(p===null?o=r:p.next=h,!re(r,t.memoizedState)&&(Lt=!0,w&&(n=vl,n!==null)))throw n;t.memoizedState=r,t.baseState=o,t.baseQueue=p,l.lastRenderedState=r}return i===null&&(l.lanes=0),[t.memoizedState,l.dispatch]}function Hs(t){var e=Ut(),n=e.queue;if(n===null)throw Error(c(311));n.lastRenderedReducer=t;var l=n.dispatch,i=n.pending,r=e.memoizedState;if(i!==null){n.pending=null;var o=i=i.next;do r=t(r,o.action),o=o.next;while(o!==i);re(r,e.memoizedState)||(Lt=!0),e.memoizedState=r,e.baseQueue===null&&(e.baseState=r),n.lastRenderedState=r}return[r,l]}function Fo(t,e,n){var l=nt,i=Ut(),r=ft;if(r){if(n===void 0)throw Error(c(407));n=n()}else n=e();var o=!re((yt||i).memoizedState,n);o&&(i.memoizedState=n,Lt=!0),i=i.queue;var h=Io.bind(null,l,i,t);if(ma(2048,8,h,[t]),i.getSnapshot!==e||o||Ct!==null&&Ct.memoizedState.tag&1){if(l.flags|=2048,_l(9,Ni(),Po.bind(null,l,i,n,e),null),bt===null)throw Error(c(349));r||(fn&124)!==0||$o(l,e,n)}return n}function $o(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=nt.updateQueue,e===null?(e=qs(),nt.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function Po(t,e,n,l){e.value=n,e.getSnapshot=l,tf(e)&&ef(t)}function Io(t,e,n){return n(function(){tf(e)&&ef(t)})}function tf(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!re(t,n)}catch{return!0}}function ef(t){var e=dl(t,2);e!==null&&pe(e,t,2)}function Ys(t){var e=It();if(typeof t=="function"){var n=t;if(t=n(),Qn){ln(!0);try{n()}finally{ln(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Je,lastRenderedState:t},e}function nf(t,e,n,l){return t.baseState=n,Ls(t,yt,typeof l=="function"?l:Je)}function lm(t,e,n,l,i){if(wi(t))throw Error(c(485));if(t=e.action,t!==null){var r={payload:i,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(o){r.listeners.push(o)}};x.T!==null?n(!0):r.isTransition=!1,l(r),n=e.pending,n===null?(r.next=e.pending=r,lf(e,r)):(r.next=n.next,e.pending=n.next=r)}}function lf(t,e){var n=e.action,l=e.payload,i=t.state;if(e.isTransition){var r=x.T,o={};x.T=o;try{var h=n(i,l),p=x.S;p!==null&&p(o,h),af(t,e,h)}catch(A){Xs(t,e,A)}finally{x.T=r}}else try{r=n(i,l),af(t,e,r)}catch(A){Xs(t,e,A)}}function af(t,e,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(l){uf(t,e,l)},function(l){return Xs(t,e,l)}):uf(t,e,n)}function uf(t,e,n){e.status="fulfilled",e.value=n,sf(e),t.state=n,e=t.pending,e!==null&&(n=e.next,n===e?t.pending=null:(n=n.next,e.next=n,lf(t,n)))}function Xs(t,e,n){var l=t.pending;if(t.pending=null,l!==null){l=l.next;do e.status="rejected",e.reason=n,sf(e),e=e.next;while(e!==l)}t.action=null}function sf(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function cf(t,e){return e}function rf(t,e){if(ft){var n=bt.formState;if(n!==null){t:{var l=nt;if(ft){if(Tt){e:{for(var i=Tt,r=Ue;i.nodeType!==8;){if(!r){i=null;break e}if(i=De(i.nextSibling),i===null){i=null;break e}}r=i.data,i=r==="F!"||r==="F"?i:null}if(i){Tt=De(i.nextSibling),l=i.data==="F!";break t}}Yn(l)}l=!1}l&&(e=n[0])}}return n=It(),n.memoizedState=n.baseState=e,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:cf,lastRenderedState:e},n.queue=l,n=Df.bind(null,nt,l),l.dispatch=n,l=Ys(!1),r=Zs.bind(null,nt,!1,l.queue),l=It(),i={state:e,dispatch:null,action:t,pending:null},l.queue=i,n=lm.bind(null,nt,i,r,n),i.dispatch=n,l.memoizedState=t,[e,n,!1]}function of(t){var e=Ut();return ff(e,yt,t)}function ff(t,e,n){if(e=Ls(t,e,cf)[0],t=Ri(Je)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var l=pa(e)}catch(o){throw o===ra?Si:o}else l=e;e=Ut();var i=e.queue,r=i.dispatch;return n!==e.memoizedState&&(nt.flags|=2048,_l(9,Ni(),am.bind(null,i,n),null)),[l,r,t]}function am(t,e){t.action=e}function hf(t){var e=Ut(),n=yt;if(n!==null)return ff(e,n,t);Ut(),e=e.memoizedState,n=Ut();var l=n.queue.dispatch;return n.memoizedState=t,[e,l,!1]}function _l(t,e,n,l){return t={tag:t,create:n,deps:l,inst:e,next:null},e=nt.updateQueue,e===null&&(e=qs(),nt.updateQueue=e),n=e.lastEffect,n===null?e.lastEffect=t.next=t:(l=n.next,n.next=t,t.next=l,e.lastEffect=t),t}function Ni(){return{destroy:void 0,resource:void 0}}function df(){return Ut().memoizedState}function Di(t,e,n,l){var i=It();l=l===void 0?null:l,nt.flags|=t,i.memoizedState=_l(1|e,Ni(),n,l)}function ma(t,e,n,l){var i=Ut();l=l===void 0?null:l;var r=i.memoizedState.inst;yt!==null&&l!==null&&Cs(l,yt.memoizedState.deps)?i.memoizedState=_l(e,r,n,l):(nt.flags|=t,i.memoizedState=_l(1|e,r,n,l))}function yf(t,e){Di(8390656,8,t,e)}function pf(t,e){ma(2048,8,t,e)}function mf(t,e){return ma(4,2,t,e)}function gf(t,e){return ma(4,4,t,e)}function vf(t,e){if(typeof e=="function"){t=t();var n=e(t);return function(){typeof n=="function"?n():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function bf(t,e,n){n=n!=null?n.concat([t]):null,ma(4,4,vf.bind(null,e,t),n)}function Gs(){}function Sf(t,e){var n=Ut();e=e===void 0?null:e;var l=n.memoizedState;return e!==null&&Cs(e,l[1])?l[0]:(n.memoizedState=[t,e],t)}function Ef(t,e){var n=Ut();e=e===void 0?null:e;var l=n.memoizedState;if(e!==null&&Cs(e,l[1]))return l[0];if(l=t(),Qn){ln(!0);try{t()}finally{ln(!1)}}return n.memoizedState=[l,e],l}function Vs(t,e,n){return n===void 0||(fn&1073741824)!==0?t.memoizedState=e:(t.memoizedState=n,t=Th(),nt.lanes|=t,vn|=t,n)}function _f(t,e,n,l){return re(n,e)?n:bl.current!==null?(t=Vs(t,n,l),re(t,e)||(Lt=!0),t):(fn&42)===0?(Lt=!0,t.memoizedState=n):(t=Th(),nt.lanes|=t,vn|=t,e)}function Af(t,e,n,l,i){var r=Y.p;Y.p=r!==0&&8>r?r:8;var o=x.T,h={};x.T=h,Zs(t,!1,e,n);try{var p=i(),A=x.S;if(A!==null&&A(h,p),p!==null&&typeof p=="object"&&typeof p.then=="function"){var w=tm(p,l);ga(t,e,w,ye(t))}else ga(t,e,l,ye(t))}catch(U){ga(t,e,{then:function(){},status:"rejected",reason:U},ye())}finally{Y.p=r,x.T=o}}function im(){}function Qs(t,e,n,l){if(t.tag!==5)throw Error(c(476));var i=Tf(t).queue;Af(t,i,e,J,n===null?im:function(){return Of(t),n(l)})}function Tf(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:J,baseState:J,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Je,lastRenderedState:J},next:null};var n={};return e.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Je,lastRenderedState:n},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function Of(t){var e=Tf(t).next.queue;ga(t,e,{},ye())}function ks(){return Kt(Ba)}function Rf(){return Ut().memoizedState}function Nf(){return Ut().memoizedState}function um(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var n=ye();t=rn(n);var l=on(e,t,n);l!==null&&(pe(l,e,n),fa(l,e,n)),e={cache:Es()},t.payload=e;return}e=e.return}}function sm(t,e,n){var l=ye();n={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},wi(t)?wf(e,n):(n=fs(t,e,n,l),n!==null&&(pe(n,t,l),xf(n,e,l)))}function Df(t,e,n){var l=ye();ga(t,e,n,l)}function ga(t,e,n,l){var i={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(wi(t))wf(e,i);else{var r=t.alternate;if(t.lanes===0&&(r===null||r.lanes===0)&&(r=e.lastRenderedReducer,r!==null))try{var o=e.lastRenderedState,h=r(o,n);if(i.hasEagerState=!0,i.eagerState=h,re(h,o))return hi(t,e,i,0),bt===null&&fi(),!1}catch{}finally{}if(n=fs(t,e,i,l),n!==null)return pe(n,t,l),xf(n,e,l),!0}return!1}function Zs(t,e,n,l){if(l={lane:2,revertLane:Tc(),action:l,hasEagerState:!1,eagerState:null,next:null},wi(t)){if(e)throw Error(c(479))}else e=fs(t,n,l,2),e!==null&&pe(e,t,2)}function wi(t){var e=t.alternate;return t===nt||e!==null&&e===nt}function wf(t,e){Sl=Ai=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function xf(t,e,n){if((n&4194048)!==0){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,qr(t,n)}}var xi={readContext:Kt,use:Oi,useCallback:Dt,useContext:Dt,useEffect:Dt,useImperativeHandle:Dt,useLayoutEffect:Dt,useInsertionEffect:Dt,useMemo:Dt,useReducer:Dt,useRef:Dt,useState:Dt,useDebugValue:Dt,useDeferredValue:Dt,useTransition:Dt,useSyncExternalStore:Dt,useId:Dt,useHostTransitionStatus:Dt,useFormState:Dt,useActionState:Dt,useOptimistic:Dt,useMemoCache:Dt,useCacheRefresh:Dt},Cf={readContext:Kt,use:Oi,useCallback:function(t,e){return It().memoizedState=[t,e===void 0?null:e],t},useContext:Kt,useEffect:yf,useImperativeHandle:function(t,e,n){n=n!=null?n.concat([t]):null,Di(4194308,4,vf.bind(null,e,t),n)},useLayoutEffect:function(t,e){return Di(4194308,4,t,e)},useInsertionEffect:function(t,e){Di(4,2,t,e)},useMemo:function(t,e){var n=It();e=e===void 0?null:e;var l=t();if(Qn){ln(!0);try{t()}finally{ln(!1)}}return n.memoizedState=[l,e],l},useReducer:function(t,e,n){var l=It();if(n!==void 0){var i=n(e);if(Qn){ln(!0);try{n(e)}finally{ln(!1)}}}else i=e;return l.memoizedState=l.baseState=i,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:i},l.queue=t,t=t.dispatch=sm.bind(null,nt,t),[l.memoizedState,t]},useRef:function(t){var e=It();return t={current:t},e.memoizedState=t},useState:function(t){t=Ys(t);var e=t.queue,n=Df.bind(null,nt,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:Gs,useDeferredValue:function(t,e){var n=It();return Vs(n,t,e)},useTransition:function(){var t=Ys(!1);return t=Af.bind(null,nt,t.queue,!0,!1),It().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var l=nt,i=It();if(ft){if(n===void 0)throw Error(c(407));n=n()}else{if(n=e(),bt===null)throw Error(c(349));(ct&124)!==0||$o(l,e,n)}i.memoizedState=n;var r={value:n,getSnapshot:e};return i.queue=r,yf(Io.bind(null,l,r,t),[t]),l.flags|=2048,_l(9,Ni(),Po.bind(null,l,r,n,e),null),n},useId:function(){var t=It(),e=bt.identifierPrefix;if(ft){var n=ke,l=Qe;n=(l&~(1<<32-ce(l)-1)).toString(32)+n,e="«"+e+"R"+n,n=Ti++,0<n&&(e+="H"+n.toString(32)),e+="»"}else n=em++,e="«"+e+"r"+n.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:ks,useFormState:rf,useActionState:rf,useOptimistic:function(t){var e=It();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=Zs.bind(null,nt,!0,n),n.dispatch=e,[t,e]},useMemoCache:js,useCacheRefresh:function(){return It().memoizedState=um.bind(null,nt)}},Uf={readContext:Kt,use:Oi,useCallback:Sf,useContext:Kt,useEffect:pf,useImperativeHandle:bf,useInsertionEffect:mf,useLayoutEffect:gf,useMemo:Ef,useReducer:Ri,useRef:df,useState:function(){return Ri(Je)},useDebugValue:Gs,useDeferredValue:function(t,e){var n=Ut();return _f(n,yt.memoizedState,t,e)},useTransition:function(){var t=Ri(Je)[0],e=Ut().memoizedState;return[typeof t=="boolean"?t:pa(t),e]},useSyncExternalStore:Fo,useId:Rf,useHostTransitionStatus:ks,useFormState:of,useActionState:of,useOptimistic:function(t,e){var n=Ut();return nf(n,yt,t,e)},useMemoCache:js,useCacheRefresh:Nf},cm={readContext:Kt,use:Oi,useCallback:Sf,useContext:Kt,useEffect:pf,useImperativeHandle:bf,useInsertionEffect:mf,useLayoutEffect:gf,useMemo:Ef,useReducer:Hs,useRef:df,useState:function(){return Hs(Je)},useDebugValue:Gs,useDeferredValue:function(t,e){var n=Ut();return yt===null?Vs(n,t,e):_f(n,yt.memoizedState,t,e)},useTransition:function(){var t=Hs(Je)[0],e=Ut().memoizedState;return[typeof t=="boolean"?t:pa(t),e]},useSyncExternalStore:Fo,useId:Rf,useHostTransitionStatus:ks,useFormState:hf,useActionState:hf,useOptimistic:function(t,e){var n=Ut();return yt!==null?nf(n,yt,t,e):(n.baseState=t,[t,n.queue.dispatch])},useMemoCache:js,useCacheRefresh:Nf},Al=null,va=0;function Ci(t){var e=va;return va+=1,Al===null&&(Al=[]),Go(Al,t,e)}function ba(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function Ui(t,e){throw e.$$typeof===D?Error(c(525)):(t=Object.prototype.toString.call(e),Error(c(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function zf(t){var e=t._init;return e(t._payload)}function Mf(t){function e(S,v){if(t){var _=S.deletions;_===null?(S.deletions=[v],S.flags|=16):_.push(v)}}function n(S,v){if(!t)return null;for(;v!==null;)e(S,v),v=v.sibling;return null}function l(S){for(var v=new Map;S!==null;)S.key!==null?v.set(S.key,S):v.set(S.index,S),S=S.sibling;return v}function i(S,v){return S=Ve(S,v),S.index=0,S.sibling=null,S}function r(S,v,_){return S.index=_,t?(_=S.alternate,_!==null?(_=_.index,_<v?(S.flags|=67108866,v):_):(S.flags|=67108866,v)):(S.flags|=1048576,v)}function o(S){return t&&S.alternate===null&&(S.flags|=67108866),S}function h(S,v,_,C){return v===null||v.tag!==6?(v=ds(_,S.mode,C),v.return=S,v):(v=i(v,_),v.return=S,v)}function p(S,v,_,C){var G=_.type;return G===B?w(S,v,_.props.children,C,_.key):v!==null&&(v.elementType===G||typeof G=="object"&&G!==null&&G.$$typeof===qt&&zf(G)===v.type)?(v=i(v,_.props),ba(v,_),v.return=S,v):(v=yi(_.type,_.key,_.props,null,S.mode,C),ba(v,_),v.return=S,v)}function A(S,v,_,C){return v===null||v.tag!==4||v.stateNode.containerInfo!==_.containerInfo||v.stateNode.implementation!==_.implementation?(v=ys(_,S.mode,C),v.return=S,v):(v=i(v,_.children||[]),v.return=S,v)}function w(S,v,_,C,G){return v===null||v.tag!==7?(v=qn(_,S.mode,C,G),v.return=S,v):(v=i(v,_),v.return=S,v)}function U(S,v,_){if(typeof v=="string"&&v!==""||typeof v=="number"||typeof v=="bigint")return v=ds(""+v,S.mode,_),v.return=S,v;if(typeof v=="object"&&v!==null){switch(v.$$typeof){case z:return _=yi(v.type,v.key,v.props,null,S.mode,_),ba(_,v),_.return=S,_;case L:return v=ys(v,S.mode,_),v.return=S,v;case qt:var C=v._init;return v=C(v._payload),U(S,v,_)}if(kt(v)||Qt(v))return v=qn(v,S.mode,_,null),v.return=S,v;if(typeof v.then=="function")return U(S,Ci(v),_);if(v.$$typeof===et)return U(S,vi(S,v),_);Ui(S,v)}return null}function O(S,v,_,C){var G=v!==null?v.key:null;if(typeof _=="string"&&_!==""||typeof _=="number"||typeof _=="bigint")return G!==null?null:h(S,v,""+_,C);if(typeof _=="object"&&_!==null){switch(_.$$typeof){case z:return _.key===G?p(S,v,_,C):null;case L:return _.key===G?A(S,v,_,C):null;case qt:return G=_._init,_=G(_._payload),O(S,v,_,C)}if(kt(_)||Qt(_))return G!==null?null:w(S,v,_,C,null);if(typeof _.then=="function")return O(S,v,Ci(_),C);if(_.$$typeof===et)return O(S,v,vi(S,_),C);Ui(S,_)}return null}function R(S,v,_,C,G){if(typeof C=="string"&&C!==""||typeof C=="number"||typeof C=="bigint")return S=S.get(_)||null,h(v,S,""+C,G);if(typeof C=="object"&&C!==null){switch(C.$$typeof){case z:return S=S.get(C.key===null?_:C.key)||null,p(v,S,C,G);case L:return S=S.get(C.key===null?_:C.key)||null,A(v,S,C,G);case qt:var lt=C._init;return C=lt(C._payload),R(S,v,_,C,G)}if(kt(C)||Qt(C))return S=S.get(_)||null,w(v,S,C,G,null);if(typeof C.then=="function")return R(S,v,_,Ci(C),G);if(C.$$typeof===et)return R(S,v,_,vi(v,C),G);Ui(v,C)}return null}function W(S,v,_,C){for(var G=null,lt=null,V=v,K=v=0,Yt=null;V!==null&&K<_.length;K++){V.index>K?(Yt=V,V=null):Yt=V.sibling;var ot=O(S,V,_[K],C);if(ot===null){V===null&&(V=Yt);break}t&&V&&ot.alternate===null&&e(S,V),v=r(ot,v,K),lt===null?G=ot:lt.sibling=ot,lt=ot,V=Yt}if(K===_.length)return n(S,V),ft&&Ln(S,K),G;if(V===null){for(;K<_.length;K++)V=U(S,_[K],C),V!==null&&(v=r(V,v,K),lt===null?G=V:lt.sibling=V,lt=V);return ft&&Ln(S,K),G}for(V=l(V);K<_.length;K++)Yt=R(V,S,K,_[K],C),Yt!==null&&(t&&Yt.alternate!==null&&V.delete(Yt.key===null?K:Yt.key),v=r(Yt,v,K),lt===null?G=Yt:lt.sibling=Yt,lt=Yt);return t&&V.forEach(function(Nn){return e(S,Nn)}),ft&&Ln(S,K),G}function Z(S,v,_,C){if(_==null)throw Error(c(151));for(var G=null,lt=null,V=v,K=v=0,Yt=null,ot=_.next();V!==null&&!ot.done;K++,ot=_.next()){V.index>K?(Yt=V,V=null):Yt=V.sibling;var Nn=O(S,V,ot.value,C);if(Nn===null){V===null&&(V=Yt);break}t&&V&&Nn.alternate===null&&e(S,V),v=r(Nn,v,K),lt===null?G=Nn:lt.sibling=Nn,lt=Nn,V=Yt}if(ot.done)return n(S,V),ft&&Ln(S,K),G;if(V===null){for(;!ot.done;K++,ot=_.next())ot=U(S,ot.value,C),ot!==null&&(v=r(ot,v,K),lt===null?G=ot:lt.sibling=ot,lt=ot);return ft&&Ln(S,K),G}for(V=l(V);!ot.done;K++,ot=_.next())ot=R(V,S,K,ot.value,C),ot!==null&&(t&&ot.alternate!==null&&V.delete(ot.key===null?K:ot.key),v=r(ot,v,K),lt===null?G=ot:lt.sibling=ot,lt=ot);return t&&V.forEach(function(r0){return e(S,r0)}),ft&&Ln(S,K),G}function mt(S,v,_,C){if(typeof _=="object"&&_!==null&&_.type===B&&_.key===null&&(_=_.props.children),typeof _=="object"&&_!==null){switch(_.$$typeof){case z:t:{for(var G=_.key;v!==null;){if(v.key===G){if(G=_.type,G===B){if(v.tag===7){n(S,v.sibling),C=i(v,_.props.children),C.return=S,S=C;break t}}else if(v.elementType===G||typeof G=="object"&&G!==null&&G.$$typeof===qt&&zf(G)===v.type){n(S,v.sibling),C=i(v,_.props),ba(C,_),C.return=S,S=C;break t}n(S,v);break}else e(S,v);v=v.sibling}_.type===B?(C=qn(_.props.children,S.mode,C,_.key),C.return=S,S=C):(C=yi(_.type,_.key,_.props,null,S.mode,C),ba(C,_),C.return=S,S=C)}return o(S);case L:t:{for(G=_.key;v!==null;){if(v.key===G)if(v.tag===4&&v.stateNode.containerInfo===_.containerInfo&&v.stateNode.implementation===_.implementation){n(S,v.sibling),C=i(v,_.children||[]),C.return=S,S=C;break t}else{n(S,v);break}else e(S,v);v=v.sibling}C=ys(_,S.mode,C),C.return=S,S=C}return o(S);case qt:return G=_._init,_=G(_._payload),mt(S,v,_,C)}if(kt(_))return W(S,v,_,C);if(Qt(_)){if(G=Qt(_),typeof G!="function")throw Error(c(150));return _=G.call(_),Z(S,v,_,C)}if(typeof _.then=="function")return mt(S,v,Ci(_),C);if(_.$$typeof===et)return mt(S,v,vi(S,_),C);Ui(S,_)}return typeof _=="string"&&_!==""||typeof _=="number"||typeof _=="bigint"?(_=""+_,v!==null&&v.tag===6?(n(S,v.sibling),C=i(v,_),C.return=S,S=C):(n(S,v),C=ds(_,S.mode,C),C.return=S,S=C),o(S)):n(S,v)}return function(S,v,_,C){try{va=0;var G=mt(S,v,_,C);return Al=null,G}catch(V){if(V===ra||V===Si)throw V;var lt=oe(29,V,null,S.mode);return lt.lanes=C,lt.return=S,lt}finally{}}}var Tl=Mf(!0),Bf=Mf(!1),_e=M(null),ze=null;function hn(t){var e=t.alternate;H(Bt,Bt.current&1),H(_e,t),ze===null&&(e===null||bl.current!==null||e.memoizedState!==null)&&(ze=t)}function qf(t){if(t.tag===22){if(H(Bt,Bt.current),H(_e,t),ze===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(ze=t)}}else dn()}function dn(){H(Bt,Bt.current),H(_e,_e.current)}function We(t){X(_e),ze===t&&(ze=null),X(Bt)}var Bt=M(0);function zi(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||qc(n)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function Ks(t,e,n,l){e=t.memoizedState,n=n(l,e),n=n==null?e:b({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var Js={enqueueSetState:function(t,e,n){t=t._reactInternals;var l=ye(),i=rn(l);i.payload=e,n!=null&&(i.callback=n),e=on(t,i,l),e!==null&&(pe(e,t,l),fa(e,t,l))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var l=ye(),i=rn(l);i.tag=1,i.payload=e,n!=null&&(i.callback=n),e=on(t,i,l),e!==null&&(pe(e,t,l),fa(e,t,l))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=ye(),l=rn(n);l.tag=2,e!=null&&(l.callback=e),e=on(t,l,n),e!==null&&(pe(e,t,n),fa(e,t,n))}};function jf(t,e,n,l,i,r,o){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(l,r,o):e.prototype&&e.prototype.isPureReactComponent?!ea(n,l)||!ea(i,r):!0}function Lf(t,e,n,l){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,l),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,l),e.state!==t&&Js.enqueueReplaceState(e,e.state,null)}function kn(t,e){var n=e;if("ref"in e){n={};for(var l in e)l!=="ref"&&(n[l]=e[l])}if(t=t.defaultProps){n===e&&(n=b({},n));for(var i in t)n[i]===void 0&&(n[i]=t[i])}return n}var Mi=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Hf(t){Mi(t)}function Yf(t){console.error(t)}function Xf(t){Mi(t)}function Bi(t,e){try{var n=t.onUncaughtError;n(e.value,{componentStack:e.stack})}catch(l){setTimeout(function(){throw l})}}function Gf(t,e,n){try{var l=t.onCaughtError;l(n.value,{componentStack:n.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(i){setTimeout(function(){throw i})}}function Ws(t,e,n){return n=rn(n),n.tag=3,n.payload={element:null},n.callback=function(){Bi(t,e)},n}function Vf(t){return t=rn(t),t.tag=3,t}function Qf(t,e,n,l){var i=n.type.getDerivedStateFromError;if(typeof i=="function"){var r=l.value;t.payload=function(){return i(r)},t.callback=function(){Gf(e,n,l)}}var o=n.stateNode;o!==null&&typeof o.componentDidCatch=="function"&&(t.callback=function(){Gf(e,n,l),typeof i!="function"&&(bn===null?bn=new Set([this]):bn.add(this));var h=l.stack;this.componentDidCatch(l.value,{componentStack:h!==null?h:""})})}function rm(t,e,n,l,i){if(n.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(e=n.alternate,e!==null&&ua(e,n,i,!0),n=_e.current,n!==null){switch(n.tag){case 13:return ze===null?bc():n.alternate===null&&Ot===0&&(Ot=3),n.flags&=-257,n.flags|=65536,n.lanes=i,l===Ts?n.flags|=16384:(e=n.updateQueue,e===null?n.updateQueue=new Set([l]):e.add(l),Ec(t,l,i)),!1;case 22:return n.flags|=65536,l===Ts?n.flags|=16384:(e=n.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([l])},n.updateQueue=e):(n=e.retryQueue,n===null?e.retryQueue=new Set([l]):n.add(l)),Ec(t,l,i)),!1}throw Error(c(435,n.tag))}return Ec(t,l,i),bc(),!1}if(ft)return e=_e.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=i,l!==gs&&(t=Error(c(422),{cause:l}),ia(ve(t,n)))):(l!==gs&&(e=Error(c(423),{cause:l}),ia(ve(e,n))),t=t.current.alternate,t.flags|=65536,i&=-i,t.lanes|=i,l=ve(l,n),i=Ws(t.stateNode,l,i),Ns(t,i),Ot!==4&&(Ot=2)),!1;var r=Error(c(520),{cause:l});if(r=ve(r,n),Ra===null?Ra=[r]:Ra.push(r),Ot!==4&&(Ot=2),e===null)return!0;l=ve(l,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=i&-i,n.lanes|=t,t=Ws(n.stateNode,l,t),Ns(n,t),!1;case 1:if(e=n.type,r=n.stateNode,(n.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||r!==null&&typeof r.componentDidCatch=="function"&&(bn===null||!bn.has(r))))return n.flags|=65536,i&=-i,n.lanes|=i,i=Vf(i),Qf(i,t,n,l),Ns(n,i),!1}n=n.return}while(n!==null);return!1}var kf=Error(c(461)),Lt=!1;function Xt(t,e,n,l){e.child=t===null?Bf(e,null,n,l):Tl(e,t.child,n,l)}function Zf(t,e,n,l,i){n=n.render;var r=e.ref;if("ref"in l){var o={};for(var h in l)h!=="ref"&&(o[h]=l[h])}else o=l;return Gn(e),l=Us(t,e,n,o,r,i),h=zs(),t!==null&&!Lt?(Ms(t,e,i),Fe(t,e,i)):(ft&&h&&ps(e),e.flags|=1,Xt(t,e,l,i),e.child)}function Kf(t,e,n,l,i){if(t===null){var r=n.type;return typeof r=="function"&&!hs(r)&&r.defaultProps===void 0&&n.compare===null?(e.tag=15,e.type=r,Jf(t,e,r,l,i)):(t=yi(n.type,null,l,e,e.mode,i),t.ref=e.ref,t.return=e,e.child=t)}if(r=t.child,!lc(t,i)){var o=r.memoizedProps;if(n=n.compare,n=n!==null?n:ea,n(o,l)&&t.ref===e.ref)return Fe(t,e,i)}return e.flags|=1,t=Ve(r,l),t.ref=e.ref,t.return=e,e.child=t}function Jf(t,e,n,l,i){if(t!==null){var r=t.memoizedProps;if(ea(r,l)&&t.ref===e.ref)if(Lt=!1,e.pendingProps=l=r,lc(t,i))(t.flags&131072)!==0&&(Lt=!0);else return e.lanes=t.lanes,Fe(t,e,i)}return Fs(t,e,n,l,i)}function Wf(t,e,n){var l=e.pendingProps,i=l.children,r=t!==null?t.memoizedState:null;if(l.mode==="hidden"){if((e.flags&128)!==0){if(l=r!==null?r.baseLanes|n:n,t!==null){for(i=e.child=t.child,r=0;i!==null;)r=r|i.lanes|i.childLanes,i=i.sibling;e.childLanes=r&~l}else e.childLanes=0,e.child=null;return Ff(t,e,l,n)}if((n&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&bi(e,r!==null?r.cachePool:null),r!==null?Ko(e,r):ws(),qf(e);else return e.lanes=e.childLanes=536870912,Ff(t,e,r!==null?r.baseLanes|n:n,n)}else r!==null?(bi(e,r.cachePool),Ko(e,r),dn(),e.memoizedState=null):(t!==null&&bi(e,null),ws(),dn());return Xt(t,e,i,n),e.child}function Ff(t,e,n,l){var i=As();return i=i===null?null:{parent:Mt._currentValue,pool:i},e.memoizedState={baseLanes:n,cachePool:i},t!==null&&bi(e,null),ws(),qf(e),t!==null&&ua(t,e,l,!0),null}function qi(t,e){var n=e.ref;if(n===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(c(284));(t===null||t.ref!==n)&&(e.flags|=4194816)}}function Fs(t,e,n,l,i){return Gn(e),n=Us(t,e,n,l,void 0,i),l=zs(),t!==null&&!Lt?(Ms(t,e,i),Fe(t,e,i)):(ft&&l&&ps(e),e.flags|=1,Xt(t,e,n,i),e.child)}function $f(t,e,n,l,i,r){return Gn(e),e.updateQueue=null,n=Wo(e,l,n,i),Jo(t),l=zs(),t!==null&&!Lt?(Ms(t,e,r),Fe(t,e,r)):(ft&&l&&ps(e),e.flags|=1,Xt(t,e,n,r),e.child)}function Pf(t,e,n,l,i){if(Gn(e),e.stateNode===null){var r=yl,o=n.contextType;typeof o=="object"&&o!==null&&(r=Kt(o)),r=new n(l,r),e.memoizedState=r.state!==null&&r.state!==void 0?r.state:null,r.updater=Js,e.stateNode=r,r._reactInternals=e,r=e.stateNode,r.props=l,r.state=e.memoizedState,r.refs={},Os(e),o=n.contextType,r.context=typeof o=="object"&&o!==null?Kt(o):yl,r.state=e.memoizedState,o=n.getDerivedStateFromProps,typeof o=="function"&&(Ks(e,n,o,l),r.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof r.getSnapshotBeforeUpdate=="function"||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(o=r.state,typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount(),o!==r.state&&Js.enqueueReplaceState(r,r.state,null),da(e,l,r,i),ha(),r.state=e.memoizedState),typeof r.componentDidMount=="function"&&(e.flags|=4194308),l=!0}else if(t===null){r=e.stateNode;var h=e.memoizedProps,p=kn(n,h);r.props=p;var A=r.context,w=n.contextType;o=yl,typeof w=="object"&&w!==null&&(o=Kt(w));var U=n.getDerivedStateFromProps;w=typeof U=="function"||typeof r.getSnapshotBeforeUpdate=="function",h=e.pendingProps!==h,w||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(h||A!==o)&&Lf(e,r,l,o),cn=!1;var O=e.memoizedState;r.state=O,da(e,l,r,i),ha(),A=e.memoizedState,h||O!==A||cn?(typeof U=="function"&&(Ks(e,n,U,l),A=e.memoizedState),(p=cn||jf(e,n,p,l,O,A,o))?(w||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount()),typeof r.componentDidMount=="function"&&(e.flags|=4194308)):(typeof r.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=l,e.memoizedState=A),r.props=l,r.state=A,r.context=o,l=p):(typeof r.componentDidMount=="function"&&(e.flags|=4194308),l=!1)}else{r=e.stateNode,Rs(t,e),o=e.memoizedProps,w=kn(n,o),r.props=w,U=e.pendingProps,O=r.context,A=n.contextType,p=yl,typeof A=="object"&&A!==null&&(p=Kt(A)),h=n.getDerivedStateFromProps,(A=typeof h=="function"||typeof r.getSnapshotBeforeUpdate=="function")||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(o!==U||O!==p)&&Lf(e,r,l,p),cn=!1,O=e.memoizedState,r.state=O,da(e,l,r,i),ha();var R=e.memoizedState;o!==U||O!==R||cn||t!==null&&t.dependencies!==null&&gi(t.dependencies)?(typeof h=="function"&&(Ks(e,n,h,l),R=e.memoizedState),(w=cn||jf(e,n,w,l,O,R,p)||t!==null&&t.dependencies!==null&&gi(t.dependencies))?(A||typeof r.UNSAFE_componentWillUpdate!="function"&&typeof r.componentWillUpdate!="function"||(typeof r.componentWillUpdate=="function"&&r.componentWillUpdate(l,R,p),typeof r.UNSAFE_componentWillUpdate=="function"&&r.UNSAFE_componentWillUpdate(l,R,p)),typeof r.componentDidUpdate=="function"&&(e.flags|=4),typeof r.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof r.componentDidUpdate!="function"||o===t.memoizedProps&&O===t.memoizedState||(e.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||o===t.memoizedProps&&O===t.memoizedState||(e.flags|=1024),e.memoizedProps=l,e.memoizedState=R),r.props=l,r.state=R,r.context=p,l=w):(typeof r.componentDidUpdate!="function"||o===t.memoizedProps&&O===t.memoizedState||(e.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||o===t.memoizedProps&&O===t.memoizedState||(e.flags|=1024),l=!1)}return r=l,qi(t,e),l=(e.flags&128)!==0,r||l?(r=e.stateNode,n=l&&typeof n.getDerivedStateFromError!="function"?null:r.render(),e.flags|=1,t!==null&&l?(e.child=Tl(e,t.child,null,i),e.child=Tl(e,null,n,i)):Xt(t,e,n,i),e.memoizedState=r.state,t=e.child):t=Fe(t,e,i),t}function If(t,e,n,l){return aa(),e.flags|=256,Xt(t,e,n,l),e.child}var $s={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Ps(t){return{baseLanes:t,cachePool:Ho()}}function Is(t,e,n){return t=t!==null?t.childLanes&~n:0,e&&(t|=Ae),t}function th(t,e,n){var l=e.pendingProps,i=!1,r=(e.flags&128)!==0,o;if((o=r)||(o=t!==null&&t.memoizedState===null?!1:(Bt.current&2)!==0),o&&(i=!0,e.flags&=-129),o=(e.flags&32)!==0,e.flags&=-33,t===null){if(ft){if(i?hn(e):dn(),ft){var h=Tt,p;if(p=h){t:{for(p=h,h=Ue;p.nodeType!==8;){if(!h){h=null;break t}if(p=De(p.nextSibling),p===null){h=null;break t}}h=p}h!==null?(e.memoizedState={dehydrated:h,treeContext:jn!==null?{id:Qe,overflow:ke}:null,retryLane:536870912,hydrationErrors:null},p=oe(18,null,null,0),p.stateNode=h,p.return=e,e.child=p,Ft=e,Tt=null,p=!0):p=!1}p||Yn(e)}if(h=e.memoizedState,h!==null&&(h=h.dehydrated,h!==null))return qc(h)?e.lanes=32:e.lanes=536870912,null;We(e)}return h=l.children,l=l.fallback,i?(dn(),i=e.mode,h=ji({mode:"hidden",children:h},i),l=qn(l,i,n,null),h.return=e,l.return=e,h.sibling=l,e.child=h,i=e.child,i.memoizedState=Ps(n),i.childLanes=Is(t,o,n),e.memoizedState=$s,l):(hn(e),tc(e,h))}if(p=t.memoizedState,p!==null&&(h=p.dehydrated,h!==null)){if(r)e.flags&256?(hn(e),e.flags&=-257,e=ec(t,e,n)):e.memoizedState!==null?(dn(),e.child=t.child,e.flags|=128,e=null):(dn(),i=l.fallback,h=e.mode,l=ji({mode:"visible",children:l.children},h),i=qn(i,h,n,null),i.flags|=2,l.return=e,i.return=e,l.sibling=i,e.child=l,Tl(e,t.child,null,n),l=e.child,l.memoizedState=Ps(n),l.childLanes=Is(t,o,n),e.memoizedState=$s,e=i);else if(hn(e),qc(h)){if(o=h.nextSibling&&h.nextSibling.dataset,o)var A=o.dgst;o=A,l=Error(c(419)),l.stack="",l.digest=o,ia({value:l,source:null,stack:null}),e=ec(t,e,n)}else if(Lt||ua(t,e,n,!1),o=(n&t.childLanes)!==0,Lt||o){if(o=bt,o!==null&&(l=n&-n,l=(l&42)!==0?1:ju(l),l=(l&(o.suspendedLanes|n))!==0?0:l,l!==0&&l!==p.retryLane))throw p.retryLane=l,dl(t,l),pe(o,t,l),kf;h.data==="$?"||bc(),e=ec(t,e,n)}else h.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=p.treeContext,Tt=De(h.nextSibling),Ft=e,ft=!0,Hn=null,Ue=!1,t!==null&&(Se[Ee++]=Qe,Se[Ee++]=ke,Se[Ee++]=jn,Qe=t.id,ke=t.overflow,jn=e),e=tc(e,l.children),e.flags|=4096);return e}return i?(dn(),i=l.fallback,h=e.mode,p=t.child,A=p.sibling,l=Ve(p,{mode:"hidden",children:l.children}),l.subtreeFlags=p.subtreeFlags&65011712,A!==null?i=Ve(A,i):(i=qn(i,h,n,null),i.flags|=2),i.return=e,l.return=e,l.sibling=i,e.child=l,l=i,i=e.child,h=t.child.memoizedState,h===null?h=Ps(n):(p=h.cachePool,p!==null?(A=Mt._currentValue,p=p.parent!==A?{parent:A,pool:A}:p):p=Ho(),h={baseLanes:h.baseLanes|n,cachePool:p}),i.memoizedState=h,i.childLanes=Is(t,o,n),e.memoizedState=$s,l):(hn(e),n=t.child,t=n.sibling,n=Ve(n,{mode:"visible",children:l.children}),n.return=e,n.sibling=null,t!==null&&(o=e.deletions,o===null?(e.deletions=[t],e.flags|=16):o.push(t)),e.child=n,e.memoizedState=null,n)}function tc(t,e){return e=ji({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function ji(t,e){return t=oe(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function ec(t,e,n){return Tl(e,t.child,null,n),t=tc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function eh(t,e,n){t.lanes|=e;var l=t.alternate;l!==null&&(l.lanes|=e),bs(t.return,e,n)}function nc(t,e,n,l,i){var r=t.memoizedState;r===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:l,tail:n,tailMode:i}:(r.isBackwards=e,r.rendering=null,r.renderingStartTime=0,r.last=l,r.tail=n,r.tailMode=i)}function nh(t,e,n){var l=e.pendingProps,i=l.revealOrder,r=l.tail;if(Xt(t,e,l.children,n),l=Bt.current,(l&2)!==0)l=l&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&eh(t,n,e);else if(t.tag===19)eh(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}l&=1}switch(H(Bt,l),i){case"forwards":for(n=e.child,i=null;n!==null;)t=n.alternate,t!==null&&zi(t)===null&&(i=n),n=n.sibling;n=i,n===null?(i=e.child,e.child=null):(i=n.sibling,n.sibling=null),nc(e,!1,i,n,r);break;case"backwards":for(n=null,i=e.child,e.child=null;i!==null;){if(t=i.alternate,t!==null&&zi(t)===null){e.child=i;break}t=i.sibling,i.sibling=n,n=i,i=t}nc(e,!0,n,null,r);break;case"together":nc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Fe(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),vn|=e.lanes,(n&e.childLanes)===0)if(t!==null){if(ua(t,e,n,!1),(n&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(c(153));if(e.child!==null){for(t=e.child,n=Ve(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=Ve(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function lc(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&gi(t)))}function om(t,e,n){switch(e.tag){case 3:St(e,e.stateNode.containerInfo),sn(e,Mt,t.memoizedState.cache),aa();break;case 27:case 5:Uu(e);break;case 4:St(e,e.stateNode.containerInfo);break;case 10:sn(e,e.type,e.memoizedProps.value);break;case 13:var l=e.memoizedState;if(l!==null)return l.dehydrated!==null?(hn(e),e.flags|=128,null):(n&e.child.childLanes)!==0?th(t,e,n):(hn(e),t=Fe(t,e,n),t!==null?t.sibling:null);hn(e);break;case 19:var i=(t.flags&128)!==0;if(l=(n&e.childLanes)!==0,l||(ua(t,e,n,!1),l=(n&e.childLanes)!==0),i){if(l)return nh(t,e,n);e.flags|=128}if(i=e.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),H(Bt,Bt.current),l)break;return null;case 22:case 23:return e.lanes=0,Wf(t,e,n);case 24:sn(e,Mt,t.memoizedState.cache)}return Fe(t,e,n)}function lh(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps)Lt=!0;else{if(!lc(t,n)&&(e.flags&128)===0)return Lt=!1,om(t,e,n);Lt=(t.flags&131072)!==0}else Lt=!1,ft&&(e.flags&1048576)!==0&&Uo(e,mi,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var l=e.elementType,i=l._init;if(l=i(l._payload),e.type=l,typeof l=="function")hs(l)?(t=kn(l,t),e.tag=1,e=Pf(null,e,l,t,n)):(e.tag=0,e=Fs(null,e,l,t,n));else{if(l!=null){if(i=l.$$typeof,i===Rt){e.tag=11,e=Zf(null,e,l,t,n);break t}else if(i===Nt){e.tag=14,e=Kf(null,e,l,t,n);break t}}throw e=xn(l)||l,Error(c(306,e,""))}}return e;case 0:return Fs(t,e,e.type,e.pendingProps,n);case 1:return l=e.type,i=kn(l,e.pendingProps),Pf(t,e,l,i,n);case 3:t:{if(St(e,e.stateNode.containerInfo),t===null)throw Error(c(387));l=e.pendingProps;var r=e.memoizedState;i=r.element,Rs(t,e),da(e,l,null,n);var o=e.memoizedState;if(l=o.cache,sn(e,Mt,l),l!==r.cache&&Ss(e,[Mt],n,!0),ha(),l=o.element,r.isDehydrated)if(r={element:l,isDehydrated:!1,cache:o.cache},e.updateQueue.baseState=r,e.memoizedState=r,e.flags&256){e=If(t,e,l,n);break t}else if(l!==i){i=ve(Error(c(424)),e),ia(i),e=If(t,e,l,n);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Tt=De(t.firstChild),Ft=e,ft=!0,Hn=null,Ue=!0,n=Bf(e,null,l,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(aa(),l===i){e=Fe(t,e,n);break t}Xt(t,e,l,n)}e=e.child}return e;case 26:return qi(t,e),t===null?(n=sd(e.type,null,e.pendingProps,null))?e.memoizedState=n:ft||(n=e.type,t=e.pendingProps,l=$i($.current).createElement(n),l[Zt]=e,l[$t]=t,Vt(l,n,t),jt(l),e.stateNode=l):e.memoizedState=sd(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return Uu(e),t===null&&ft&&(l=e.stateNode=ad(e.type,e.pendingProps,$.current),Ft=e,Ue=!0,i=Tt,_n(e.type)?(jc=i,Tt=De(l.firstChild)):Tt=i),Xt(t,e,e.pendingProps.children,n),qi(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&ft&&((i=l=Tt)&&(l=Lm(l,e.type,e.pendingProps,Ue),l!==null?(e.stateNode=l,Ft=e,Tt=De(l.firstChild),Ue=!1,i=!0):i=!1),i||Yn(e)),Uu(e),i=e.type,r=e.pendingProps,o=t!==null?t.memoizedProps:null,l=r.children,zc(i,r)?l=null:o!==null&&zc(i,o)&&(e.flags|=32),e.memoizedState!==null&&(i=Us(t,e,nm,null,null,n),Ba._currentValue=i),qi(t,e),Xt(t,e,l,n),e.child;case 6:return t===null&&ft&&((t=n=Tt)&&(n=Hm(n,e.pendingProps,Ue),n!==null?(e.stateNode=n,Ft=e,Tt=null,t=!0):t=!1),t||Yn(e)),null;case 13:return th(t,e,n);case 4:return St(e,e.stateNode.containerInfo),l=e.pendingProps,t===null?e.child=Tl(e,null,l,n):Xt(t,e,l,n),e.child;case 11:return Zf(t,e,e.type,e.pendingProps,n);case 7:return Xt(t,e,e.pendingProps,n),e.child;case 8:return Xt(t,e,e.pendingProps.children,n),e.child;case 12:return Xt(t,e,e.pendingProps.children,n),e.child;case 10:return l=e.pendingProps,sn(e,e.type,l.value),Xt(t,e,l.children,n),e.child;case 9:return i=e.type._context,l=e.pendingProps.children,Gn(e),i=Kt(i),l=l(i),e.flags|=1,Xt(t,e,l,n),e.child;case 14:return Kf(t,e,e.type,e.pendingProps,n);case 15:return Jf(t,e,e.type,e.pendingProps,n);case 19:return nh(t,e,n);case 31:return l=e.pendingProps,n=e.mode,l={mode:l.mode,children:l.children},t===null?(n=ji(l,n),n.ref=e.ref,e.child=n,n.return=e,e=n):(n=Ve(t.child,l),n.ref=e.ref,e.child=n,n.return=e,e=n),e;case 22:return Wf(t,e,n);case 24:return Gn(e),l=Kt(Mt),t===null?(i=As(),i===null&&(i=bt,r=Es(),i.pooledCache=r,r.refCount++,r!==null&&(i.pooledCacheLanes|=n),i=r),e.memoizedState={parent:l,cache:i},Os(e),sn(e,Mt,i)):((t.lanes&n)!==0&&(Rs(t,e),da(e,null,null,n),ha()),i=t.memoizedState,r=e.memoizedState,i.parent!==l?(i={parent:l,cache:l},e.memoizedState=i,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=i),sn(e,Mt,l)):(l=r.cache,sn(e,Mt,l),l!==i.cache&&Ss(e,[Mt],n,!0))),Xt(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(c(156,e.tag))}function $e(t){t.flags|=4}function ah(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!hd(e)){if(e=_e.current,e!==null&&((ct&4194048)===ct?ze!==null:(ct&62914560)!==ct&&(ct&536870912)===0||e!==ze))throw oa=Ts,Yo;t.flags|=8192}}function Li(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Mr():536870912,t.lanes|=e,Dl|=e)}function Sa(t,e){if(!ft)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:l.sibling=null}}function _t(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,l=0;if(e)for(var i=t.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags&65011712,l|=i.flags&65011712,i.return=t,i=i.sibling;else for(i=t.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags,l|=i.flags,i.return=t,i=i.sibling;return t.subtreeFlags|=l,t.childLanes=n,e}function fm(t,e,n){var l=e.pendingProps;switch(ms(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return _t(e),null;case 1:return _t(e),null;case 3:return n=e.stateNode,l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),Ke(Mt),nn(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(t===null||t.child===null)&&(la(e)?$e(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,Bo())),_t(e),null;case 26:return n=e.memoizedState,t===null?($e(e),n!==null?(_t(e),ah(e,n)):(_t(e),e.flags&=-16777217)):n?n!==t.memoizedState?($e(e),_t(e),ah(e,n)):(_t(e),e.flags&=-16777217):(t.memoizedProps!==l&&$e(e),_t(e),e.flags&=-16777217),null;case 27:Wa(e),n=$.current;var i=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==l&&$e(e);else{if(!l){if(e.stateNode===null)throw Error(c(166));return _t(e),null}t=k.current,la(e)?zo(e):(t=ad(i,l,n),e.stateNode=t,$e(e))}return _t(e),null;case 5:if(Wa(e),n=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==l&&$e(e);else{if(!l){if(e.stateNode===null)throw Error(c(166));return _t(e),null}if(t=k.current,la(e))zo(e);else{switch(i=$i($.current),t){case 1:t=i.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:t=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":t=i.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":t=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":t=i.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof l.is=="string"?i.createElement("select",{is:l.is}):i.createElement("select"),l.multiple?t.multiple=!0:l.size&&(t.size=l.size);break;default:t=typeof l.is=="string"?i.createElement(n,{is:l.is}):i.createElement(n)}}t[Zt]=e,t[$t]=l;t:for(i=e.child;i!==null;){if(i.tag===5||i.tag===6)t.appendChild(i.stateNode);else if(i.tag!==4&&i.tag!==27&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===e)break t;for(;i.sibling===null;){if(i.return===null||i.return===e)break t;i=i.return}i.sibling.return=i.return,i=i.sibling}e.stateNode=t;t:switch(Vt(t,n,l),n){case"button":case"input":case"select":case"textarea":t=!!l.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&$e(e)}}return _t(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==l&&$e(e);else{if(typeof l!="string"&&e.stateNode===null)throw Error(c(166));if(t=$.current,la(e)){if(t=e.stateNode,n=e.memoizedProps,l=null,i=Ft,i!==null)switch(i.tag){case 27:case 5:l=i.memoizedProps}t[Zt]=e,t=!!(t.nodeValue===n||l!==null&&l.suppressHydrationWarning===!0||$h(t.nodeValue,n)),t||Yn(e)}else t=$i(t).createTextNode(l),t[Zt]=e,e.stateNode=t}return _t(e),null;case 13:if(l=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(i=la(e),l!==null&&l.dehydrated!==null){if(t===null){if(!i)throw Error(c(318));if(i=e.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(c(317));i[Zt]=e}else aa(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;_t(e),i=!1}else i=Bo(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=i),i=!0;if(!i)return e.flags&256?(We(e),e):(We(e),null)}if(We(e),(e.flags&128)!==0)return e.lanes=n,e;if(n=l!==null,t=t!==null&&t.memoizedState!==null,n){l=e.child,i=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(i=l.alternate.memoizedState.cachePool.pool);var r=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(r=l.memoizedState.cachePool.pool),r!==i&&(l.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),Li(e,e.updateQueue),_t(e),null;case 4:return nn(),t===null&&Dc(e.stateNode.containerInfo),_t(e),null;case 10:return Ke(e.type),_t(e),null;case 19:if(X(Bt),i=e.memoizedState,i===null)return _t(e),null;if(l=(e.flags&128)!==0,r=i.rendering,r===null)if(l)Sa(i,!1);else{if(Ot!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(r=zi(t),r!==null){for(e.flags|=128,Sa(i,!1),t=r.updateQueue,e.updateQueue=t,Li(e,t),e.subtreeFlags=0,t=n,n=e.child;n!==null;)Co(n,t),n=n.sibling;return H(Bt,Bt.current&1|2),e.child}t=t.sibling}i.tail!==null&&Ce()>Xi&&(e.flags|=128,l=!0,Sa(i,!1),e.lanes=4194304)}else{if(!l)if(t=zi(r),t!==null){if(e.flags|=128,l=!0,t=t.updateQueue,e.updateQueue=t,Li(e,t),Sa(i,!0),i.tail===null&&i.tailMode==="hidden"&&!r.alternate&&!ft)return _t(e),null}else 2*Ce()-i.renderingStartTime>Xi&&n!==536870912&&(e.flags|=128,l=!0,Sa(i,!1),e.lanes=4194304);i.isBackwards?(r.sibling=e.child,e.child=r):(t=i.last,t!==null?t.sibling=r:e.child=r,i.last=r)}return i.tail!==null?(e=i.tail,i.rendering=e,i.tail=e.sibling,i.renderingStartTime=Ce(),e.sibling=null,t=Bt.current,H(Bt,l?t&1|2:t&1),e):(_t(e),null);case 22:case 23:return We(e),xs(),l=e.memoizedState!==null,t!==null?t.memoizedState!==null!==l&&(e.flags|=8192):l&&(e.flags|=8192),l?(n&536870912)!==0&&(e.flags&128)===0&&(_t(e),e.subtreeFlags&6&&(e.flags|=8192)):_t(e),n=e.updateQueue,n!==null&&Li(e,n.retryQueue),n=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),l=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),l!==n&&(e.flags|=2048),t!==null&&X(Vn),null;case 24:return n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),Ke(Mt),_t(e),null;case 25:return null;case 30:return null}throw Error(c(156,e.tag))}function hm(t,e){switch(ms(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Ke(Mt),nn(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return Wa(e),null;case 13:if(We(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(c(340));aa()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return X(Bt),null;case 4:return nn(),null;case 10:return Ke(e.type),null;case 22:case 23:return We(e),xs(),t!==null&&X(Vn),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return Ke(Mt),null;case 25:return null;default:return null}}function ih(t,e){switch(ms(e),e.tag){case 3:Ke(Mt),nn();break;case 26:case 27:case 5:Wa(e);break;case 4:nn();break;case 13:We(e);break;case 19:X(Bt);break;case 10:Ke(e.type);break;case 22:case 23:We(e),xs(),t!==null&&X(Vn);break;case 24:Ke(Mt)}}function Ea(t,e){try{var n=e.updateQueue,l=n!==null?n.lastEffect:null;if(l!==null){var i=l.next;n=i;do{if((n.tag&t)===t){l=void 0;var r=n.create,o=n.inst;l=r(),o.destroy=l}n=n.next}while(n!==i)}}catch(h){vt(e,e.return,h)}}function yn(t,e,n){try{var l=e.updateQueue,i=l!==null?l.lastEffect:null;if(i!==null){var r=i.next;l=r;do{if((l.tag&t)===t){var o=l.inst,h=o.destroy;if(h!==void 0){o.destroy=void 0,i=e;var p=n,A=h;try{A()}catch(w){vt(i,p,w)}}}l=l.next}while(l!==r)}}catch(w){vt(e,e.return,w)}}function uh(t){var e=t.updateQueue;if(e!==null){var n=t.stateNode;try{Zo(e,n)}catch(l){vt(t,t.return,l)}}}function sh(t,e,n){n.props=kn(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(l){vt(t,e,l)}}function _a(t,e){try{var n=t.ref;if(n!==null){switch(t.tag){case 26:case 27:case 5:var l=t.stateNode;break;case 30:l=t.stateNode;break;default:l=t.stateNode}typeof n=="function"?t.refCleanup=n(l):n.current=l}}catch(i){vt(t,e,i)}}function Me(t,e){var n=t.ref,l=t.refCleanup;if(n!==null)if(typeof l=="function")try{l()}catch(i){vt(t,e,i)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(i){vt(t,e,i)}else n.current=null}function ch(t){var e=t.type,n=t.memoizedProps,l=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":n.autoFocus&&l.focus();break t;case"img":n.src?l.src=n.src:n.srcSet&&(l.srcset=n.srcSet)}}catch(i){vt(t,t.return,i)}}function ac(t,e,n){try{var l=t.stateNode;zm(l,t.type,n,e),l[$t]=e}catch(i){vt(t,t.return,i)}}function rh(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&_n(t.type)||t.tag===4}function ic(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||rh(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&_n(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function uc(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(t,e):(e=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,e.appendChild(t),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=Fi));else if(l!==4&&(l===27&&_n(t.type)&&(n=t.stateNode,e=null),t=t.child,t!==null))for(uc(t,e,n),t=t.sibling;t!==null;)uc(t,e,n),t=t.sibling}function Hi(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(l!==4&&(l===27&&_n(t.type)&&(n=t.stateNode),t=t.child,t!==null))for(Hi(t,e,n),t=t.sibling;t!==null;)Hi(t,e,n),t=t.sibling}function oh(t){var e=t.stateNode,n=t.memoizedProps;try{for(var l=t.type,i=e.attributes;i.length;)e.removeAttributeNode(i[0]);Vt(e,l,n),e[Zt]=t,e[$t]=n}catch(r){vt(t,t.return,r)}}var Pe=!1,wt=!1,sc=!1,fh=typeof WeakSet=="function"?WeakSet:Set,Ht=null;function dm(t,e){if(t=t.containerInfo,Cc=lu,t=Eo(t),is(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var l=n.getSelection&&n.getSelection();if(l&&l.rangeCount!==0){n=l.anchorNode;var i=l.anchorOffset,r=l.focusNode;l=l.focusOffset;try{n.nodeType,r.nodeType}catch{n=null;break t}var o=0,h=-1,p=-1,A=0,w=0,U=t,O=null;e:for(;;){for(var R;U!==n||i!==0&&U.nodeType!==3||(h=o+i),U!==r||l!==0&&U.nodeType!==3||(p=o+l),U.nodeType===3&&(o+=U.nodeValue.length),(R=U.firstChild)!==null;)O=U,U=R;for(;;){if(U===t)break e;if(O===n&&++A===i&&(h=o),O===r&&++w===l&&(p=o),(R=U.nextSibling)!==null)break;U=O,O=U.parentNode}U=R}n=h===-1||p===-1?null:{start:h,end:p}}else n=null}n=n||{start:0,end:0}}else n=null;for(Uc={focusedElem:t,selectionRange:n},lu=!1,Ht=e;Ht!==null;)if(e=Ht,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Ht=t;else for(;Ht!==null;){switch(e=Ht,r=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&r!==null){t=void 0,n=e,i=r.memoizedProps,r=r.memoizedState,l=n.stateNode;try{var W=kn(n.type,i,n.elementType===n.type);t=l.getSnapshotBeforeUpdate(W,r),l.__reactInternalSnapshotBeforeUpdate=t}catch(Z){vt(n,n.return,Z)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,n=t.nodeType,n===9)Bc(t);else if(n===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":Bc(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(c(163))}if(t=e.sibling,t!==null){t.return=e.return,Ht=t;break}Ht=e.return}}function hh(t,e,n){var l=n.flags;switch(n.tag){case 0:case 11:case 15:pn(t,n),l&4&&Ea(5,n);break;case 1:if(pn(t,n),l&4)if(t=n.stateNode,e===null)try{t.componentDidMount()}catch(o){vt(n,n.return,o)}else{var i=kn(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(i,e,t.__reactInternalSnapshotBeforeUpdate)}catch(o){vt(n,n.return,o)}}l&64&&uh(n),l&512&&_a(n,n.return);break;case 3:if(pn(t,n),l&64&&(t=n.updateQueue,t!==null)){if(e=null,n.child!==null)switch(n.child.tag){case 27:case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}try{Zo(t,e)}catch(o){vt(n,n.return,o)}}break;case 27:e===null&&l&4&&oh(n);case 26:case 5:pn(t,n),e===null&&l&4&&ch(n),l&512&&_a(n,n.return);break;case 12:pn(t,n);break;case 13:pn(t,n),l&4&&ph(t,n),l&64&&(t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(n=_m.bind(null,n),Ym(t,n))));break;case 22:if(l=n.memoizedState!==null||Pe,!l){e=e!==null&&e.memoizedState!==null||wt,i=Pe;var r=wt;Pe=l,(wt=e)&&!r?mn(t,n,(n.subtreeFlags&8772)!==0):pn(t,n),Pe=i,wt=r}break;case 30:break;default:pn(t,n)}}function dh(t){var e=t.alternate;e!==null&&(t.alternate=null,dh(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&Yu(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Et=null,te=!1;function Ie(t,e,n){for(n=n.child;n!==null;)yh(t,e,n),n=n.sibling}function yh(t,e,n){if(se&&typeof se.onCommitFiberUnmount=="function")try{se.onCommitFiberUnmount(Vl,n)}catch{}switch(n.tag){case 26:wt||Me(n,e),Ie(t,e,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:wt||Me(n,e);var l=Et,i=te;_n(n.type)&&(Et=n.stateNode,te=!1),Ie(t,e,n),Ca(n.stateNode),Et=l,te=i;break;case 5:wt||Me(n,e);case 6:if(l=Et,i=te,Et=null,Ie(t,e,n),Et=l,te=i,Et!==null)if(te)try{(Et.nodeType===9?Et.body:Et.nodeName==="HTML"?Et.ownerDocument.body:Et).removeChild(n.stateNode)}catch(r){vt(n,e,r)}else try{Et.removeChild(n.stateNode)}catch(r){vt(n,e,r)}break;case 18:Et!==null&&(te?(t=Et,nd(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,n.stateNode),Ha(t)):nd(Et,n.stateNode));break;case 4:l=Et,i=te,Et=n.stateNode.containerInfo,te=!0,Ie(t,e,n),Et=l,te=i;break;case 0:case 11:case 14:case 15:wt||yn(2,n,e),wt||yn(4,n,e),Ie(t,e,n);break;case 1:wt||(Me(n,e),l=n.stateNode,typeof l.componentWillUnmount=="function"&&sh(n,e,l)),Ie(t,e,n);break;case 21:Ie(t,e,n);break;case 22:wt=(l=wt)||n.memoizedState!==null,Ie(t,e,n),wt=l;break;default:Ie(t,e,n)}}function ph(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{Ha(t)}catch(n){vt(e,e.return,n)}}function ym(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new fh),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new fh),e;default:throw Error(c(435,t.tag))}}function cc(t,e){var n=ym(t);e.forEach(function(l){var i=Am.bind(null,t,l);n.has(l)||(n.add(l),l.then(i,i))})}function fe(t,e){var n=e.deletions;if(n!==null)for(var l=0;l<n.length;l++){var i=n[l],r=t,o=e,h=o;t:for(;h!==null;){switch(h.tag){case 27:if(_n(h.type)){Et=h.stateNode,te=!1;break t}break;case 5:Et=h.stateNode,te=!1;break t;case 3:case 4:Et=h.stateNode.containerInfo,te=!0;break t}h=h.return}if(Et===null)throw Error(c(160));yh(r,o,i),Et=null,te=!1,r=i.alternate,r!==null&&(r.return=null),i.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)mh(e,t),e=e.sibling}var Ne=null;function mh(t,e){var n=t.alternate,l=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:fe(e,t),he(t),l&4&&(yn(3,t,t.return),Ea(3,t),yn(5,t,t.return));break;case 1:fe(e,t),he(t),l&512&&(wt||n===null||Me(n,n.return)),l&64&&Pe&&(t=t.updateQueue,t!==null&&(l=t.callbacks,l!==null&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=n===null?l:n.concat(l))));break;case 26:var i=Ne;if(fe(e,t),he(t),l&512&&(wt||n===null||Me(n,n.return)),l&4){var r=n!==null?n.memoizedState:null;if(l=t.memoizedState,n===null)if(l===null)if(t.stateNode===null){t:{l=t.type,n=t.memoizedProps,i=i.ownerDocument||i;e:switch(l){case"title":r=i.getElementsByTagName("title")[0],(!r||r[Zl]||r[Zt]||r.namespaceURI==="http://www.w3.org/2000/svg"||r.hasAttribute("itemprop"))&&(r=i.createElement(l),i.head.insertBefore(r,i.querySelector("head > title"))),Vt(r,l,n),r[Zt]=t,jt(r),l=r;break t;case"link":var o=od("link","href",i).get(l+(n.href||""));if(o){for(var h=0;h<o.length;h++)if(r=o[h],r.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&r.getAttribute("rel")===(n.rel==null?null:n.rel)&&r.getAttribute("title")===(n.title==null?null:n.title)&&r.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){o.splice(h,1);break e}}r=i.createElement(l),Vt(r,l,n),i.head.appendChild(r);break;case"meta":if(o=od("meta","content",i).get(l+(n.content||""))){for(h=0;h<o.length;h++)if(r=o[h],r.getAttribute("content")===(n.content==null?null:""+n.content)&&r.getAttribute("name")===(n.name==null?null:n.name)&&r.getAttribute("property")===(n.property==null?null:n.property)&&r.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&r.getAttribute("charset")===(n.charSet==null?null:n.charSet)){o.splice(h,1);break e}}r=i.createElement(l),Vt(r,l,n),i.head.appendChild(r);break;default:throw Error(c(468,l))}r[Zt]=t,jt(r),l=r}t.stateNode=l}else fd(i,t.type,t.stateNode);else t.stateNode=rd(i,l,t.memoizedProps);else r!==l?(r===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):r.count--,l===null?fd(i,t.type,t.stateNode):rd(i,l,t.memoizedProps)):l===null&&t.stateNode!==null&&ac(t,t.memoizedProps,n.memoizedProps)}break;case 27:fe(e,t),he(t),l&512&&(wt||n===null||Me(n,n.return)),n!==null&&l&4&&ac(t,t.memoizedProps,n.memoizedProps);break;case 5:if(fe(e,t),he(t),l&512&&(wt||n===null||Me(n,n.return)),t.flags&32){i=t.stateNode;try{ul(i,"")}catch(R){vt(t,t.return,R)}}l&4&&t.stateNode!=null&&(i=t.memoizedProps,ac(t,i,n!==null?n.memoizedProps:i)),l&1024&&(sc=!0);break;case 6:if(fe(e,t),he(t),l&4){if(t.stateNode===null)throw Error(c(162));l=t.memoizedProps,n=t.stateNode;try{n.nodeValue=l}catch(R){vt(t,t.return,R)}}break;case 3:if(tu=null,i=Ne,Ne=Pi(e.containerInfo),fe(e,t),Ne=i,he(t),l&4&&n!==null&&n.memoizedState.isDehydrated)try{Ha(e.containerInfo)}catch(R){vt(t,t.return,R)}sc&&(sc=!1,gh(t));break;case 4:l=Ne,Ne=Pi(t.stateNode.containerInfo),fe(e,t),he(t),Ne=l;break;case 12:fe(e,t),he(t);break;case 13:fe(e,t),he(t),t.child.flags&8192&&t.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(yc=Ce()),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,cc(t,l)));break;case 22:i=t.memoizedState!==null;var p=n!==null&&n.memoizedState!==null,A=Pe,w=wt;if(Pe=A||i,wt=w||p,fe(e,t),wt=w,Pe=A,he(t),l&8192)t:for(e=t.stateNode,e._visibility=i?e._visibility&-2:e._visibility|1,i&&(n===null||p||Pe||wt||Zn(t)),n=null,e=t;;){if(e.tag===5||e.tag===26){if(n===null){p=n=e;try{if(r=p.stateNode,i)o=r.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none";else{h=p.stateNode;var U=p.memoizedProps.style,O=U!=null&&U.hasOwnProperty("display")?U.display:null;h.style.display=O==null||typeof O=="boolean"?"":(""+O).trim()}}catch(R){vt(p,p.return,R)}}}else if(e.tag===6){if(n===null){p=e;try{p.stateNode.nodeValue=i?"":p.memoizedProps}catch(R){vt(p,p.return,R)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}l&4&&(l=t.updateQueue,l!==null&&(n=l.retryQueue,n!==null&&(l.retryQueue=null,cc(t,n))));break;case 19:fe(e,t),he(t),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,cc(t,l)));break;case 30:break;case 21:break;default:fe(e,t),he(t)}}function he(t){var e=t.flags;if(e&2){try{for(var n,l=t.return;l!==null;){if(rh(l)){n=l;break}l=l.return}if(n==null)throw Error(c(160));switch(n.tag){case 27:var i=n.stateNode,r=ic(t);Hi(t,r,i);break;case 5:var o=n.stateNode;n.flags&32&&(ul(o,""),n.flags&=-33);var h=ic(t);Hi(t,h,o);break;case 3:case 4:var p=n.stateNode.containerInfo,A=ic(t);uc(t,A,p);break;default:throw Error(c(161))}}catch(w){vt(t,t.return,w)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function gh(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;gh(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function pn(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)hh(t,e.alternate,e),e=e.sibling}function Zn(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:yn(4,e,e.return),Zn(e);break;case 1:Me(e,e.return);var n=e.stateNode;typeof n.componentWillUnmount=="function"&&sh(e,e.return,n),Zn(e);break;case 27:Ca(e.stateNode);case 26:case 5:Me(e,e.return),Zn(e);break;case 22:e.memoizedState===null&&Zn(e);break;case 30:Zn(e);break;default:Zn(e)}t=t.sibling}}function mn(t,e,n){for(n=n&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var l=e.alternate,i=t,r=e,o=r.flags;switch(r.tag){case 0:case 11:case 15:mn(i,r,n),Ea(4,r);break;case 1:if(mn(i,r,n),l=r,i=l.stateNode,typeof i.componentDidMount=="function")try{i.componentDidMount()}catch(A){vt(l,l.return,A)}if(l=r,i=l.updateQueue,i!==null){var h=l.stateNode;try{var p=i.shared.hiddenCallbacks;if(p!==null)for(i.shared.hiddenCallbacks=null,i=0;i<p.length;i++)ko(p[i],h)}catch(A){vt(l,l.return,A)}}n&&o&64&&uh(r),_a(r,r.return);break;case 27:oh(r);case 26:case 5:mn(i,r,n),n&&l===null&&o&4&&ch(r),_a(r,r.return);break;case 12:mn(i,r,n);break;case 13:mn(i,r,n),n&&o&4&&ph(i,r);break;case 22:r.memoizedState===null&&mn(i,r,n),_a(r,r.return);break;case 30:break;default:mn(i,r,n)}e=e.sibling}}function rc(t,e){var n=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==n&&(t!=null&&t.refCount++,n!=null&&sa(n))}function oc(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&sa(t))}function Be(t,e,n,l){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)vh(t,e,n,l),e=e.sibling}function vh(t,e,n,l){var i=e.flags;switch(e.tag){case 0:case 11:case 15:Be(t,e,n,l),i&2048&&Ea(9,e);break;case 1:Be(t,e,n,l);break;case 3:Be(t,e,n,l),i&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&sa(t)));break;case 12:if(i&2048){Be(t,e,n,l),t=e.stateNode;try{var r=e.memoizedProps,o=r.id,h=r.onPostCommit;typeof h=="function"&&h(o,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(p){vt(e,e.return,p)}}else Be(t,e,n,l);break;case 13:Be(t,e,n,l);break;case 23:break;case 22:r=e.stateNode,o=e.alternate,e.memoizedState!==null?r._visibility&2?Be(t,e,n,l):Aa(t,e):r._visibility&2?Be(t,e,n,l):(r._visibility|=2,Ol(t,e,n,l,(e.subtreeFlags&10256)!==0)),i&2048&&rc(o,e);break;case 24:Be(t,e,n,l),i&2048&&oc(e.alternate,e);break;default:Be(t,e,n,l)}}function Ol(t,e,n,l,i){for(i=i&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var r=t,o=e,h=n,p=l,A=o.flags;switch(o.tag){case 0:case 11:case 15:Ol(r,o,h,p,i),Ea(8,o);break;case 23:break;case 22:var w=o.stateNode;o.memoizedState!==null?w._visibility&2?Ol(r,o,h,p,i):Aa(r,o):(w._visibility|=2,Ol(r,o,h,p,i)),i&&A&2048&&rc(o.alternate,o);break;case 24:Ol(r,o,h,p,i),i&&A&2048&&oc(o.alternate,o);break;default:Ol(r,o,h,p,i)}e=e.sibling}}function Aa(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var n=t,l=e,i=l.flags;switch(l.tag){case 22:Aa(n,l),i&2048&&rc(l.alternate,l);break;case 24:Aa(n,l),i&2048&&oc(l.alternate,l);break;default:Aa(n,l)}e=e.sibling}}var Ta=8192;function Rl(t){if(t.subtreeFlags&Ta)for(t=t.child;t!==null;)bh(t),t=t.sibling}function bh(t){switch(t.tag){case 26:Rl(t),t.flags&Ta&&t.memoizedState!==null&&Im(Ne,t.memoizedState,t.memoizedProps);break;case 5:Rl(t);break;case 3:case 4:var e=Ne;Ne=Pi(t.stateNode.containerInfo),Rl(t),Ne=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Ta,Ta=16777216,Rl(t),Ta=e):Rl(t));break;default:Rl(t)}}function Sh(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Oa(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];Ht=l,_h(l,t)}Sh(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Eh(t),t=t.sibling}function Eh(t){switch(t.tag){case 0:case 11:case 15:Oa(t),t.flags&2048&&yn(9,t,t.return);break;case 3:Oa(t);break;case 12:Oa(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Yi(t)):Oa(t);break;default:Oa(t)}}function Yi(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];Ht=l,_h(l,t)}Sh(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:yn(8,e,e.return),Yi(e);break;case 22:n=e.stateNode,n._visibility&2&&(n._visibility&=-3,Yi(e));break;default:Yi(e)}t=t.sibling}}function _h(t,e){for(;Ht!==null;){var n=Ht;switch(n.tag){case 0:case 11:case 15:yn(8,n,e);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var l=n.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:sa(n.memoizedState.cache)}if(l=n.child,l!==null)l.return=n,Ht=l;else t:for(n=t;Ht!==null;){l=Ht;var i=l.sibling,r=l.return;if(dh(l),l===n){Ht=null;break t}if(i!==null){i.return=r,Ht=i;break t}Ht=r}}}var pm={getCacheForType:function(t){var e=Kt(Mt),n=e.data.get(t);return n===void 0&&(n=t(),e.data.set(t,n)),n}},mm=typeof WeakMap=="function"?WeakMap:Map,ht=0,bt=null,at=null,ct=0,dt=0,de=null,gn=!1,Nl=!1,fc=!1,tn=0,Ot=0,vn=0,Kn=0,hc=0,Ae=0,Dl=0,Ra=null,ee=null,dc=!1,yc=0,Xi=1/0,Gi=null,bn=null,Gt=0,Sn=null,wl=null,xl=0,pc=0,mc=null,Ah=null,Na=0,gc=null;function ye(){if((ht&2)!==0&&ct!==0)return ct&-ct;if(x.T!==null){var t=gl;return t!==0?t:Tc()}return jr()}function Th(){Ae===0&&(Ae=(ct&536870912)===0||ft?zr():536870912);var t=_e.current;return t!==null&&(t.flags|=32),Ae}function pe(t,e,n){(t===bt&&(dt===2||dt===9)||t.cancelPendingCommit!==null)&&(Cl(t,0),En(t,ct,Ae,!1)),kl(t,n),((ht&2)===0||t!==bt)&&(t===bt&&((ht&2)===0&&(Kn|=n),Ot===4&&En(t,ct,Ae,!1)),qe(t))}function Oh(t,e,n){if((ht&6)!==0)throw Error(c(327));var l=!n&&(e&124)===0&&(e&t.expiredLanes)===0||Ql(t,e),i=l?bm(t,e):Sc(t,e,!0),r=l;do{if(i===0){Nl&&!l&&En(t,e,0,!1);break}else{if(n=t.current.alternate,r&&!gm(n)){i=Sc(t,e,!1),r=!1;continue}if(i===2){if(r=e,t.errorRecoveryDisabledLanes&r)var o=0;else o=t.pendingLanes&-536870913,o=o!==0?o:o&536870912?536870912:0;if(o!==0){e=o;t:{var h=t;i=Ra;var p=h.current.memoizedState.isDehydrated;if(p&&(Cl(h,o).flags|=256),o=Sc(h,o,!1),o!==2){if(fc&&!p){h.errorRecoveryDisabledLanes|=r,Kn|=r,i=4;break t}r=ee,ee=i,r!==null&&(ee===null?ee=r:ee.push.apply(ee,r))}i=o}if(r=!1,i!==2)continue}}if(i===1){Cl(t,0),En(t,e,0,!0);break}t:{switch(l=t,r=i,r){case 0:case 1:throw Error(c(345));case 4:if((e&4194048)!==e)break;case 6:En(l,e,Ae,!gn);break t;case 2:ee=null;break;case 3:case 5:break;default:throw Error(c(329))}if((e&62914560)===e&&(i=yc+300-Ce(),10<i)){if(En(l,e,Ae,!gn),Ia(l,0,!0)!==0)break t;l.timeoutHandle=td(Rh.bind(null,l,n,ee,Gi,dc,e,Ae,Kn,Dl,gn,r,2,-0,0),i);break t}Rh(l,n,ee,Gi,dc,e,Ae,Kn,Dl,gn,r,0,-0,0)}}break}while(!0);qe(t)}function Rh(t,e,n,l,i,r,o,h,p,A,w,U,O,R){if(t.timeoutHandle=-1,U=e.subtreeFlags,(U&8192||(U&16785408)===16785408)&&(Ma={stylesheets:null,count:0,unsuspend:Pm},bh(e),U=t0(),U!==null)){t.cancelPendingCommit=U(zh.bind(null,t,e,r,n,l,i,o,h,p,w,1,O,R)),En(t,r,o,!A);return}zh(t,e,r,n,l,i,o,h,p)}function gm(t){for(var e=t;;){var n=e.tag;if((n===0||n===11||n===15)&&e.flags&16384&&(n=e.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var l=0;l<n.length;l++){var i=n[l],r=i.getSnapshot;i=i.value;try{if(!re(r(),i))return!1}catch{return!1}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function En(t,e,n,l){e&=~hc,e&=~Kn,t.suspendedLanes|=e,t.pingedLanes&=~e,l&&(t.warmLanes|=e),l=t.expirationTimes;for(var i=e;0<i;){var r=31-ce(i),o=1<<r;l[r]=-1,i&=~o}n!==0&&Br(t,n,e)}function Vi(){return(ht&6)===0?(Da(0),!1):!0}function vc(){if(at!==null){if(dt===0)var t=at.return;else t=at,Ze=Xn=null,Bs(t),Al=null,va=0,t=at;for(;t!==null;)ih(t.alternate,t),t=t.return;at=null}}function Cl(t,e){var n=t.timeoutHandle;n!==-1&&(t.timeoutHandle=-1,Bm(n)),n=t.cancelPendingCommit,n!==null&&(t.cancelPendingCommit=null,n()),vc(),bt=t,at=n=Ve(t.current,null),ct=e,dt=0,de=null,gn=!1,Nl=Ql(t,e),fc=!1,Dl=Ae=hc=Kn=vn=Ot=0,ee=Ra=null,dc=!1,(e&8)!==0&&(e|=e&32);var l=t.entangledLanes;if(l!==0)for(t=t.entanglements,l&=e;0<l;){var i=31-ce(l),r=1<<i;e|=t[i],l&=~r}return tn=e,fi(),n}function Nh(t,e){nt=null,x.H=xi,e===ra||e===Si?(e=Vo(),dt=3):e===Yo?(e=Vo(),dt=4):dt=e===kf?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,de=e,at===null&&(Ot=1,Bi(t,ve(e,t.current)))}function Dh(){var t=x.H;return x.H=xi,t===null?xi:t}function wh(){var t=x.A;return x.A=pm,t}function bc(){Ot=4,gn||(ct&4194048)!==ct&&_e.current!==null||(Nl=!0),(vn&134217727)===0&&(Kn&134217727)===0||bt===null||En(bt,ct,Ae,!1)}function Sc(t,e,n){var l=ht;ht|=2;var i=Dh(),r=wh();(bt!==t||ct!==e)&&(Gi=null,Cl(t,e)),e=!1;var o=Ot;t:do try{if(dt!==0&&at!==null){var h=at,p=de;switch(dt){case 8:vc(),o=6;break t;case 3:case 2:case 9:case 6:_e.current===null&&(e=!0);var A=dt;if(dt=0,de=null,Ul(t,h,p,A),n&&Nl){o=0;break t}break;default:A=dt,dt=0,de=null,Ul(t,h,p,A)}}vm(),o=Ot;break}catch(w){Nh(t,w)}while(!0);return e&&t.shellSuspendCounter++,Ze=Xn=null,ht=l,x.H=i,x.A=r,at===null&&(bt=null,ct=0,fi()),o}function vm(){for(;at!==null;)xh(at)}function bm(t,e){var n=ht;ht|=2;var l=Dh(),i=wh();bt!==t||ct!==e?(Gi=null,Xi=Ce()+500,Cl(t,e)):Nl=Ql(t,e);t:do try{if(dt!==0&&at!==null){e=at;var r=de;e:switch(dt){case 1:dt=0,de=null,Ul(t,e,r,1);break;case 2:case 9:if(Xo(r)){dt=0,de=null,Ch(e);break}e=function(){dt!==2&&dt!==9||bt!==t||(dt=7),qe(t)},r.then(e,e);break t;case 3:dt=7;break t;case 4:dt=5;break t;case 7:Xo(r)?(dt=0,de=null,Ch(e)):(dt=0,de=null,Ul(t,e,r,7));break;case 5:var o=null;switch(at.tag){case 26:o=at.memoizedState;case 5:case 27:var h=at;if(!o||hd(o)){dt=0,de=null;var p=h.sibling;if(p!==null)at=p;else{var A=h.return;A!==null?(at=A,Qi(A)):at=null}break e}}dt=0,de=null,Ul(t,e,r,5);break;case 6:dt=0,de=null,Ul(t,e,r,6);break;case 8:vc(),Ot=6;break t;default:throw Error(c(462))}}Sm();break}catch(w){Nh(t,w)}while(!0);return Ze=Xn=null,x.H=l,x.A=i,ht=n,at!==null?0:(bt=null,ct=0,fi(),Ot)}function Sm(){for(;at!==null&&!Gy();)xh(at)}function xh(t){var e=lh(t.alternate,t,tn);t.memoizedProps=t.pendingProps,e===null?Qi(t):at=e}function Ch(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=$f(n,e,e.pendingProps,e.type,void 0,ct);break;case 11:e=$f(n,e,e.pendingProps,e.type.render,e.ref,ct);break;case 5:Bs(e);default:ih(n,e),e=at=Co(e,tn),e=lh(n,e,tn)}t.memoizedProps=t.pendingProps,e===null?Qi(t):at=e}function Ul(t,e,n,l){Ze=Xn=null,Bs(e),Al=null,va=0;var i=e.return;try{if(rm(t,i,e,n,ct)){Ot=1,Bi(t,ve(n,t.current)),at=null;return}}catch(r){if(i!==null)throw at=i,r;Ot=1,Bi(t,ve(n,t.current)),at=null;return}e.flags&32768?(ft||l===1?t=!0:Nl||(ct&536870912)!==0?t=!1:(gn=t=!0,(l===2||l===9||l===3||l===6)&&(l=_e.current,l!==null&&l.tag===13&&(l.flags|=16384))),Uh(e,t)):Qi(e)}function Qi(t){var e=t;do{if((e.flags&32768)!==0){Uh(e,gn);return}t=e.return;var n=fm(e.alternate,e,tn);if(n!==null){at=n;return}if(e=e.sibling,e!==null){at=e;return}at=e=t}while(e!==null);Ot===0&&(Ot=5)}function Uh(t,e){do{var n=hm(t.alternate,t);if(n!==null){n.flags&=32767,at=n;return}if(n=t.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&(t=t.sibling,t!==null)){at=t;return}at=t=n}while(t!==null);Ot=6,at=null}function zh(t,e,n,l,i,r,o,h,p){t.cancelPendingCommit=null;do ki();while(Gt!==0);if((ht&6)!==0)throw Error(c(327));if(e!==null){if(e===t.current)throw Error(c(177));if(r=e.lanes|e.childLanes,r|=os,Py(t,n,r,o,h,p),t===bt&&(at=bt=null,ct=0),wl=e,Sn=t,xl=n,pc=r,mc=i,Ah=l,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,Tm(Fa,function(){return Lh(),null})):(t.callbackNode=null,t.callbackPriority=0),l=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||l){l=x.T,x.T=null,i=Y.p,Y.p=2,o=ht,ht|=4;try{dm(t,e,n)}finally{ht=o,Y.p=i,x.T=l}}Gt=1,Mh(),Bh(),qh()}}function Mh(){if(Gt===1){Gt=0;var t=Sn,e=wl,n=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||n){n=x.T,x.T=null;var l=Y.p;Y.p=2;var i=ht;ht|=4;try{mh(e,t);var r=Uc,o=Eo(t.containerInfo),h=r.focusedElem,p=r.selectionRange;if(o!==h&&h&&h.ownerDocument&&So(h.ownerDocument.documentElement,h)){if(p!==null&&is(h)){var A=p.start,w=p.end;if(w===void 0&&(w=A),"selectionStart"in h)h.selectionStart=A,h.selectionEnd=Math.min(w,h.value.length);else{var U=h.ownerDocument||document,O=U&&U.defaultView||window;if(O.getSelection){var R=O.getSelection(),W=h.textContent.length,Z=Math.min(p.start,W),mt=p.end===void 0?Z:Math.min(p.end,W);!R.extend&&Z>mt&&(o=mt,mt=Z,Z=o);var S=bo(h,Z),v=bo(h,mt);if(S&&v&&(R.rangeCount!==1||R.anchorNode!==S.node||R.anchorOffset!==S.offset||R.focusNode!==v.node||R.focusOffset!==v.offset)){var _=U.createRange();_.setStart(S.node,S.offset),R.removeAllRanges(),Z>mt?(R.addRange(_),R.extend(v.node,v.offset)):(_.setEnd(v.node,v.offset),R.addRange(_))}}}}for(U=[],R=h;R=R.parentNode;)R.nodeType===1&&U.push({element:R,left:R.scrollLeft,top:R.scrollTop});for(typeof h.focus=="function"&&h.focus(),h=0;h<U.length;h++){var C=U[h];C.element.scrollLeft=C.left,C.element.scrollTop=C.top}}lu=!!Cc,Uc=Cc=null}finally{ht=i,Y.p=l,x.T=n}}t.current=e,Gt=2}}function Bh(){if(Gt===2){Gt=0;var t=Sn,e=wl,n=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||n){n=x.T,x.T=null;var l=Y.p;Y.p=2;var i=ht;ht|=4;try{hh(t,e.alternate,e)}finally{ht=i,Y.p=l,x.T=n}}Gt=3}}function qh(){if(Gt===4||Gt===3){Gt=0,Vy();var t=Sn,e=wl,n=xl,l=Ah;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Gt=5:(Gt=0,wl=Sn=null,jh(t,t.pendingLanes));var i=t.pendingLanes;if(i===0&&(bn=null),Lu(n),e=e.stateNode,se&&typeof se.onCommitFiberRoot=="function")try{se.onCommitFiberRoot(Vl,e,void 0,(e.current.flags&128)===128)}catch{}if(l!==null){e=x.T,i=Y.p,Y.p=2,x.T=null;try{for(var r=t.onRecoverableError,o=0;o<l.length;o++){var h=l[o];r(h.value,{componentStack:h.stack})}}finally{x.T=e,Y.p=i}}(xl&3)!==0&&ki(),qe(t),i=t.pendingLanes,(n&4194090)!==0&&(i&42)!==0?t===gc?Na++:(Na=0,gc=t):Na=0,Da(0)}}function jh(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,sa(e)))}function ki(t){return Mh(),Bh(),qh(),Lh()}function Lh(){if(Gt!==5)return!1;var t=Sn,e=pc;pc=0;var n=Lu(xl),l=x.T,i=Y.p;try{Y.p=32>n?32:n,x.T=null,n=mc,mc=null;var r=Sn,o=xl;if(Gt=0,wl=Sn=null,xl=0,(ht&6)!==0)throw Error(c(331));var h=ht;if(ht|=4,Eh(r.current),vh(r,r.current,o,n),ht=h,Da(0,!1),se&&typeof se.onPostCommitFiberRoot=="function")try{se.onPostCommitFiberRoot(Vl,r)}catch{}return!0}finally{Y.p=i,x.T=l,jh(t,e)}}function Hh(t,e,n){e=ve(n,e),e=Ws(t.stateNode,e,2),t=on(t,e,2),t!==null&&(kl(t,2),qe(t))}function vt(t,e,n){if(t.tag===3)Hh(t,t,n);else for(;e!==null;){if(e.tag===3){Hh(e,t,n);break}else if(e.tag===1){var l=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(bn===null||!bn.has(l))){t=ve(n,t),n=Vf(2),l=on(e,n,2),l!==null&&(Qf(n,l,e,t),kl(l,2),qe(l));break}}e=e.return}}function Ec(t,e,n){var l=t.pingCache;if(l===null){l=t.pingCache=new mm;var i=new Set;l.set(e,i)}else i=l.get(e),i===void 0&&(i=new Set,l.set(e,i));i.has(n)||(fc=!0,i.add(n),t=Em.bind(null,t,e,n),e.then(t,t))}function Em(t,e,n){var l=t.pingCache;l!==null&&l.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,bt===t&&(ct&n)===n&&(Ot===4||Ot===3&&(ct&62914560)===ct&&300>Ce()-yc?(ht&2)===0&&Cl(t,0):hc|=n,Dl===ct&&(Dl=0)),qe(t)}function Yh(t,e){e===0&&(e=Mr()),t=dl(t,e),t!==null&&(kl(t,e),qe(t))}function _m(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),Yh(t,n)}function Am(t,e){var n=0;switch(t.tag){case 13:var l=t.stateNode,i=t.memoizedState;i!==null&&(n=i.retryLane);break;case 19:l=t.stateNode;break;case 22:l=t.stateNode._retryCache;break;default:throw Error(c(314))}l!==null&&l.delete(e),Yh(t,n)}function Tm(t,e){return Mu(t,e)}var Zi=null,zl=null,_c=!1,Ki=!1,Ac=!1,Jn=0;function qe(t){t!==zl&&t.next===null&&(zl===null?Zi=zl=t:zl=zl.next=t),Ki=!0,_c||(_c=!0,Rm())}function Da(t,e){if(!Ac&&Ki){Ac=!0;do for(var n=!1,l=Zi;l!==null;){if(t!==0){var i=l.pendingLanes;if(i===0)var r=0;else{var o=l.suspendedLanes,h=l.pingedLanes;r=(1<<31-ce(42|t)+1)-1,r&=i&~(o&~h),r=r&201326741?r&201326741|1:r?r|2:0}r!==0&&(n=!0,Qh(l,r))}else r=ct,r=Ia(l,l===bt?r:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(r&3)===0||Ql(l,r)||(n=!0,Qh(l,r));l=l.next}while(n);Ac=!1}}function Om(){Xh()}function Xh(){Ki=_c=!1;var t=0;Jn!==0&&(Mm()&&(t=Jn),Jn=0);for(var e=Ce(),n=null,l=Zi;l!==null;){var i=l.next,r=Gh(l,e);r===0?(l.next=null,n===null?Zi=i:n.next=i,i===null&&(zl=n)):(n=l,(t!==0||(r&3)!==0)&&(Ki=!0)),l=i}Da(t)}function Gh(t,e){for(var n=t.suspendedLanes,l=t.pingedLanes,i=t.expirationTimes,r=t.pendingLanes&-62914561;0<r;){var o=31-ce(r),h=1<<o,p=i[o];p===-1?((h&n)===0||(h&l)!==0)&&(i[o]=$y(h,e)):p<=e&&(t.expiredLanes|=h),r&=~h}if(e=bt,n=ct,n=Ia(t,t===e?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l=t.callbackNode,n===0||t===e&&(dt===2||dt===9)||t.cancelPendingCommit!==null)return l!==null&&l!==null&&Bu(l),t.callbackNode=null,t.callbackPriority=0;if((n&3)===0||Ql(t,n)){if(e=n&-n,e===t.callbackPriority)return e;switch(l!==null&&Bu(l),Lu(n)){case 2:case 8:n=Cr;break;case 32:n=Fa;break;case 268435456:n=Ur;break;default:n=Fa}return l=Vh.bind(null,t),n=Mu(n,l),t.callbackPriority=e,t.callbackNode=n,e}return l!==null&&l!==null&&Bu(l),t.callbackPriority=2,t.callbackNode=null,2}function Vh(t,e){if(Gt!==0&&Gt!==5)return t.callbackNode=null,t.callbackPriority=0,null;var n=t.callbackNode;if(ki()&&t.callbackNode!==n)return null;var l=ct;return l=Ia(t,t===bt?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l===0?null:(Oh(t,l,e),Gh(t,Ce()),t.callbackNode!=null&&t.callbackNode===n?Vh.bind(null,t):null)}function Qh(t,e){if(ki())return null;Oh(t,e,!0)}function Rm(){qm(function(){(ht&6)!==0?Mu(xr,Om):Xh()})}function Tc(){return Jn===0&&(Jn=zr()),Jn}function kh(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:ai(""+t)}function Zh(t,e){var n=e.ownerDocument.createElement("input");return n.name=e.name,n.value=e.value,t.id&&n.setAttribute("form",t.id),e.parentNode.insertBefore(n,e),t=new FormData(t),n.parentNode.removeChild(n),t}function Nm(t,e,n,l,i){if(e==="submit"&&n&&n.stateNode===i){var r=kh((i[$t]||null).action),o=l.submitter;o&&(e=(e=o[$t]||null)?kh(e.formAction):o.getAttribute("formAction"),e!==null&&(r=e,o=null));var h=new ci("action","action",null,l,i);t.push({event:h,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(Jn!==0){var p=o?Zh(i,o):new FormData(i);Qs(n,{pending:!0,data:p,method:i.method,action:r},null,p)}}else typeof r=="function"&&(h.preventDefault(),p=o?Zh(i,o):new FormData(i),Qs(n,{pending:!0,data:p,method:i.method,action:r},r,p))},currentTarget:i}]})}}for(var Oc=0;Oc<rs.length;Oc++){var Rc=rs[Oc],Dm=Rc.toLowerCase(),wm=Rc[0].toUpperCase()+Rc.slice(1);Re(Dm,"on"+wm)}Re(To,"onAnimationEnd"),Re(Oo,"onAnimationIteration"),Re(Ro,"onAnimationStart"),Re("dblclick","onDoubleClick"),Re("focusin","onFocus"),Re("focusout","onBlur"),Re(Zp,"onTransitionRun"),Re(Kp,"onTransitionStart"),Re(Jp,"onTransitionCancel"),Re(No,"onTransitionEnd"),ll("onMouseEnter",["mouseout","mouseover"]),ll("onMouseLeave",["mouseout","mouseover"]),ll("onPointerEnter",["pointerout","pointerover"]),ll("onPointerLeave",["pointerout","pointerover"]),Un("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Un("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Un("onBeforeInput",["compositionend","keypress","textInput","paste"]),Un("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Un("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Un("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var wa="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),xm=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(wa));function Kh(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var l=t[n],i=l.event;l=l.listeners;t:{var r=void 0;if(e)for(var o=l.length-1;0<=o;o--){var h=l[o],p=h.instance,A=h.currentTarget;if(h=h.listener,p!==r&&i.isPropagationStopped())break t;r=h,i.currentTarget=A;try{r(i)}catch(w){Mi(w)}i.currentTarget=null,r=p}else for(o=0;o<l.length;o++){if(h=l[o],p=h.instance,A=h.currentTarget,h=h.listener,p!==r&&i.isPropagationStopped())break t;r=h,i.currentTarget=A;try{r(i)}catch(w){Mi(w)}i.currentTarget=null,r=p}}}}function it(t,e){var n=e[Hu];n===void 0&&(n=e[Hu]=new Set);var l=t+"__bubble";n.has(l)||(Jh(e,t,2,!1),n.add(l))}function Nc(t,e,n){var l=0;e&&(l|=4),Jh(n,t,l,e)}var Ji="_reactListening"+Math.random().toString(36).slice(2);function Dc(t){if(!t[Ji]){t[Ji]=!0,Hr.forEach(function(n){n!=="selectionchange"&&(xm.has(n)||Nc(n,!1,t),Nc(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Ji]||(e[Ji]=!0,Nc("selectionchange",!1,e))}}function Jh(t,e,n,l){switch(vd(e)){case 2:var i=l0;break;case 8:i=a0;break;default:i=Gc}n=i.bind(null,e,n,t),i=void 0,!Fu||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(i=!0),l?i!==void 0?t.addEventListener(e,n,{capture:!0,passive:i}):t.addEventListener(e,n,!0):i!==void 0?t.addEventListener(e,n,{passive:i}):t.addEventListener(e,n,!1)}function wc(t,e,n,l,i){var r=l;if((e&1)===0&&(e&2)===0&&l!==null)t:for(;;){if(l===null)return;var o=l.tag;if(o===3||o===4){var h=l.stateNode.containerInfo;if(h===i)break;if(o===4)for(o=l.return;o!==null;){var p=o.tag;if((p===3||p===4)&&o.stateNode.containerInfo===i)return;o=o.return}for(;h!==null;){if(o=tl(h),o===null)return;if(p=o.tag,p===5||p===6||p===26||p===27){l=r=o;continue t}h=h.parentNode}}l=l.return}Ir(function(){var A=r,w=Ju(n),U=[];t:{var O=Do.get(t);if(O!==void 0){var R=ci,W=t;switch(t){case"keypress":if(ui(n)===0)break t;case"keydown":case"keyup":R=Tp;break;case"focusin":W="focus",R=ts;break;case"focusout":W="blur",R=ts;break;case"beforeblur":case"afterblur":R=ts;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":R=no;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":R=hp;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":R=Np;break;case To:case Oo:case Ro:R=pp;break;case No:R=wp;break;case"scroll":case"scrollend":R=op;break;case"wheel":R=Cp;break;case"copy":case"cut":case"paste":R=gp;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":R=ao;break;case"toggle":case"beforetoggle":R=zp}var Z=(e&4)!==0,mt=!Z&&(t==="scroll"||t==="scrollend"),S=Z?O!==null?O+"Capture":null:O;Z=[];for(var v=A,_;v!==null;){var C=v;if(_=C.stateNode,C=C.tag,C!==5&&C!==26&&C!==27||_===null||S===null||(C=Jl(v,S),C!=null&&Z.push(xa(v,C,_))),mt)break;v=v.return}0<Z.length&&(O=new R(O,W,null,n,w),U.push({event:O,listeners:Z}))}}if((e&7)===0){t:{if(O=t==="mouseover"||t==="pointerover",R=t==="mouseout"||t==="pointerout",O&&n!==Ku&&(W=n.relatedTarget||n.fromElement)&&(tl(W)||W[In]))break t;if((R||O)&&(O=w.window===w?w:(O=w.ownerDocument)?O.defaultView||O.parentWindow:window,R?(W=n.relatedTarget||n.toElement,R=A,W=W?tl(W):null,W!==null&&(mt=d(W),Z=W.tag,W!==mt||Z!==5&&Z!==27&&Z!==6)&&(W=null)):(R=null,W=A),R!==W)){if(Z=no,C="onMouseLeave",S="onMouseEnter",v="mouse",(t==="pointerout"||t==="pointerover")&&(Z=ao,C="onPointerLeave",S="onPointerEnter",v="pointer"),mt=R==null?O:Kl(R),_=W==null?O:Kl(W),O=new Z(C,v+"leave",R,n,w),O.target=mt,O.relatedTarget=_,C=null,tl(w)===A&&(Z=new Z(S,v+"enter",W,n,w),Z.target=_,Z.relatedTarget=mt,C=Z),mt=C,R&&W)e:{for(Z=R,S=W,v=0,_=Z;_;_=Ml(_))v++;for(_=0,C=S;C;C=Ml(C))_++;for(;0<v-_;)Z=Ml(Z),v--;for(;0<_-v;)S=Ml(S),_--;for(;v--;){if(Z===S||S!==null&&Z===S.alternate)break e;Z=Ml(Z),S=Ml(S)}Z=null}else Z=null;R!==null&&Wh(U,O,R,Z,!1),W!==null&&mt!==null&&Wh(U,mt,W,Z,!0)}}t:{if(O=A?Kl(A):window,R=O.nodeName&&O.nodeName.toLowerCase(),R==="select"||R==="input"&&O.type==="file")var G=ho;else if(oo(O))if(yo)G=Vp;else{G=Xp;var lt=Yp}else R=O.nodeName,!R||R.toLowerCase()!=="input"||O.type!=="checkbox"&&O.type!=="radio"?A&&Zu(A.elementType)&&(G=ho):G=Gp;if(G&&(G=G(t,A))){fo(U,G,n,w);break t}lt&&lt(t,O,A),t==="focusout"&&A&&O.type==="number"&&A.memoizedProps.value!=null&&ku(O,"number",O.value)}switch(lt=A?Kl(A):window,t){case"focusin":(oo(lt)||lt.contentEditable==="true")&&(ol=lt,us=A,na=null);break;case"focusout":na=us=ol=null;break;case"mousedown":ss=!0;break;case"contextmenu":case"mouseup":case"dragend":ss=!1,_o(U,n,w);break;case"selectionchange":if(kp)break;case"keydown":case"keyup":_o(U,n,w)}var V;if(ns)t:{switch(t){case"compositionstart":var K="onCompositionStart";break t;case"compositionend":K="onCompositionEnd";break t;case"compositionupdate":K="onCompositionUpdate";break t}K=void 0}else rl?co(t,n)&&(K="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(K="onCompositionStart");K&&(io&&n.locale!=="ko"&&(rl||K!=="onCompositionStart"?K==="onCompositionEnd"&&rl&&(V=to()):(un=w,$u="value"in un?un.value:un.textContent,rl=!0)),lt=Wi(A,K),0<lt.length&&(K=new lo(K,t,null,n,w),U.push({event:K,listeners:lt}),V?K.data=V:(V=ro(n),V!==null&&(K.data=V)))),(V=Bp?qp(t,n):jp(t,n))&&(K=Wi(A,"onBeforeInput"),0<K.length&&(lt=new lo("onBeforeInput","beforeinput",null,n,w),U.push({event:lt,listeners:K}),lt.data=V)),Nm(U,t,A,n,w)}Kh(U,e)})}function xa(t,e,n){return{instance:t,listener:e,currentTarget:n}}function Wi(t,e){for(var n=e+"Capture",l=[];t!==null;){var i=t,r=i.stateNode;if(i=i.tag,i!==5&&i!==26&&i!==27||r===null||(i=Jl(t,n),i!=null&&l.unshift(xa(t,i,r)),i=Jl(t,e),i!=null&&l.push(xa(t,i,r))),t.tag===3)return l;t=t.return}return[]}function Ml(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Wh(t,e,n,l,i){for(var r=e._reactName,o=[];n!==null&&n!==l;){var h=n,p=h.alternate,A=h.stateNode;if(h=h.tag,p!==null&&p===l)break;h!==5&&h!==26&&h!==27||A===null||(p=A,i?(A=Jl(n,r),A!=null&&o.unshift(xa(n,A,p))):i||(A=Jl(n,r),A!=null&&o.push(xa(n,A,p)))),n=n.return}o.length!==0&&t.push({event:e,listeners:o})}var Cm=/\r\n?/g,Um=/\u0000|\uFFFD/g;function Fh(t){return(typeof t=="string"?t:""+t).replace(Cm,`
`).replace(Um,"")}function $h(t,e){return e=Fh(e),Fh(t)===e}function Fi(){}function pt(t,e,n,l,i,r){switch(n){case"children":typeof l=="string"?e==="body"||e==="textarea"&&l===""||ul(t,l):(typeof l=="number"||typeof l=="bigint")&&e!=="body"&&ul(t,""+l);break;case"className":ei(t,"class",l);break;case"tabIndex":ei(t,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":ei(t,n,l);break;case"style":$r(t,l,r);break;case"data":if(e!=="object"){ei(t,"data",l);break}case"src":case"href":if(l===""&&(e!=="a"||n!=="href")){t.removeAttribute(n);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=ai(""+l),t.setAttribute(n,l);break;case"action":case"formAction":if(typeof l=="function"){t.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof r=="function"&&(n==="formAction"?(e!=="input"&&pt(t,e,"name",i.name,i,null),pt(t,e,"formEncType",i.formEncType,i,null),pt(t,e,"formMethod",i.formMethod,i,null),pt(t,e,"formTarget",i.formTarget,i,null)):(pt(t,e,"encType",i.encType,i,null),pt(t,e,"method",i.method,i,null),pt(t,e,"target",i.target,i,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=ai(""+l),t.setAttribute(n,l);break;case"onClick":l!=null&&(t.onclick=Fi);break;case"onScroll":l!=null&&it("scroll",t);break;case"onScrollEnd":l!=null&&it("scrollend",t);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(c(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(c(60));t.innerHTML=n}}break;case"multiple":t.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":t.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){t.removeAttribute("xlink:href");break}n=ai(""+l),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""+l):t.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""):t.removeAttribute(n);break;case"capture":case"download":l===!0?t.setAttribute(n,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,l):t.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?t.setAttribute(n,l):t.removeAttribute(n);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?t.removeAttribute(n):t.setAttribute(n,l);break;case"popover":it("beforetoggle",t),it("toggle",t),ti(t,"popover",l);break;case"xlinkActuate":Xe(t,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":Xe(t,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":Xe(t,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":Xe(t,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":Xe(t,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":Xe(t,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":Xe(t,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":Xe(t,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":Xe(t,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":ti(t,"is",l);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=cp.get(n)||n,ti(t,n,l))}}function xc(t,e,n,l,i,r){switch(n){case"style":$r(t,l,r);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(c(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(c(60));t.innerHTML=n}}break;case"children":typeof l=="string"?ul(t,l):(typeof l=="number"||typeof l=="bigint")&&ul(t,""+l);break;case"onScroll":l!=null&&it("scroll",t);break;case"onScrollEnd":l!=null&&it("scrollend",t);break;case"onClick":l!=null&&(t.onclick=Fi);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Yr.hasOwnProperty(n))t:{if(n[0]==="o"&&n[1]==="n"&&(i=n.endsWith("Capture"),e=n.slice(2,i?n.length-7:void 0),r=t[$t]||null,r=r!=null?r[n]:null,typeof r=="function"&&t.removeEventListener(e,r,i),typeof l=="function")){typeof r!="function"&&r!==null&&(n in t?t[n]=null:t.hasAttribute(n)&&t.removeAttribute(n)),t.addEventListener(e,l,i);break t}n in t?t[n]=l:l===!0?t.setAttribute(n,""):ti(t,n,l)}}}function Vt(t,e,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":it("error",t),it("load",t);var l=!1,i=!1,r;for(r in n)if(n.hasOwnProperty(r)){var o=n[r];if(o!=null)switch(r){case"src":l=!0;break;case"srcSet":i=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(c(137,e));default:pt(t,e,r,o,n,null)}}i&&pt(t,e,"srcSet",n.srcSet,n,null),l&&pt(t,e,"src",n.src,n,null);return;case"input":it("invalid",t);var h=r=o=i=null,p=null,A=null;for(l in n)if(n.hasOwnProperty(l)){var w=n[l];if(w!=null)switch(l){case"name":i=w;break;case"type":o=w;break;case"checked":p=w;break;case"defaultChecked":A=w;break;case"value":r=w;break;case"defaultValue":h=w;break;case"children":case"dangerouslySetInnerHTML":if(w!=null)throw Error(c(137,e));break;default:pt(t,e,l,w,n,null)}}Kr(t,r,h,p,A,o,i,!1),ni(t);return;case"select":it("invalid",t),l=o=r=null;for(i in n)if(n.hasOwnProperty(i)&&(h=n[i],h!=null))switch(i){case"value":r=h;break;case"defaultValue":o=h;break;case"multiple":l=h;default:pt(t,e,i,h,n,null)}e=r,n=o,t.multiple=!!l,e!=null?il(t,!!l,e,!1):n!=null&&il(t,!!l,n,!0);return;case"textarea":it("invalid",t),r=i=l=null;for(o in n)if(n.hasOwnProperty(o)&&(h=n[o],h!=null))switch(o){case"value":l=h;break;case"defaultValue":i=h;break;case"children":r=h;break;case"dangerouslySetInnerHTML":if(h!=null)throw Error(c(91));break;default:pt(t,e,o,h,n,null)}Wr(t,l,i,r),ni(t);return;case"option":for(p in n)if(n.hasOwnProperty(p)&&(l=n[p],l!=null))switch(p){case"selected":t.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:pt(t,e,p,l,n,null)}return;case"dialog":it("beforetoggle",t),it("toggle",t),it("cancel",t),it("close",t);break;case"iframe":case"object":it("load",t);break;case"video":case"audio":for(l=0;l<wa.length;l++)it(wa[l],t);break;case"image":it("error",t),it("load",t);break;case"details":it("toggle",t);break;case"embed":case"source":case"link":it("error",t),it("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(A in n)if(n.hasOwnProperty(A)&&(l=n[A],l!=null))switch(A){case"children":case"dangerouslySetInnerHTML":throw Error(c(137,e));default:pt(t,e,A,l,n,null)}return;default:if(Zu(e)){for(w in n)n.hasOwnProperty(w)&&(l=n[w],l!==void 0&&xc(t,e,w,l,n,void 0));return}}for(h in n)n.hasOwnProperty(h)&&(l=n[h],l!=null&&pt(t,e,h,l,n,null))}function zm(t,e,n,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,r=null,o=null,h=null,p=null,A=null,w=null;for(R in n){var U=n[R];if(n.hasOwnProperty(R)&&U!=null)switch(R){case"checked":break;case"value":break;case"defaultValue":p=U;default:l.hasOwnProperty(R)||pt(t,e,R,null,l,U)}}for(var O in l){var R=l[O];if(U=n[O],l.hasOwnProperty(O)&&(R!=null||U!=null))switch(O){case"type":r=R;break;case"name":i=R;break;case"checked":A=R;break;case"defaultChecked":w=R;break;case"value":o=R;break;case"defaultValue":h=R;break;case"children":case"dangerouslySetInnerHTML":if(R!=null)throw Error(c(137,e));break;default:R!==U&&pt(t,e,O,R,l,U)}}Qu(t,o,h,p,A,w,r,i);return;case"select":R=o=h=O=null;for(r in n)if(p=n[r],n.hasOwnProperty(r)&&p!=null)switch(r){case"value":break;case"multiple":R=p;default:l.hasOwnProperty(r)||pt(t,e,r,null,l,p)}for(i in l)if(r=l[i],p=n[i],l.hasOwnProperty(i)&&(r!=null||p!=null))switch(i){case"value":O=r;break;case"defaultValue":h=r;break;case"multiple":o=r;default:r!==p&&pt(t,e,i,r,l,p)}e=h,n=o,l=R,O!=null?il(t,!!n,O,!1):!!l!=!!n&&(e!=null?il(t,!!n,e,!0):il(t,!!n,n?[]:"",!1));return;case"textarea":R=O=null;for(h in n)if(i=n[h],n.hasOwnProperty(h)&&i!=null&&!l.hasOwnProperty(h))switch(h){case"value":break;case"children":break;default:pt(t,e,h,null,l,i)}for(o in l)if(i=l[o],r=n[o],l.hasOwnProperty(o)&&(i!=null||r!=null))switch(o){case"value":O=i;break;case"defaultValue":R=i;break;case"children":break;case"dangerouslySetInnerHTML":if(i!=null)throw Error(c(91));break;default:i!==r&&pt(t,e,o,i,l,r)}Jr(t,O,R);return;case"option":for(var W in n)if(O=n[W],n.hasOwnProperty(W)&&O!=null&&!l.hasOwnProperty(W))switch(W){case"selected":t.selected=!1;break;default:pt(t,e,W,null,l,O)}for(p in l)if(O=l[p],R=n[p],l.hasOwnProperty(p)&&O!==R&&(O!=null||R!=null))switch(p){case"selected":t.selected=O&&typeof O!="function"&&typeof O!="symbol";break;default:pt(t,e,p,O,l,R)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var Z in n)O=n[Z],n.hasOwnProperty(Z)&&O!=null&&!l.hasOwnProperty(Z)&&pt(t,e,Z,null,l,O);for(A in l)if(O=l[A],R=n[A],l.hasOwnProperty(A)&&O!==R&&(O!=null||R!=null))switch(A){case"children":case"dangerouslySetInnerHTML":if(O!=null)throw Error(c(137,e));break;default:pt(t,e,A,O,l,R)}return;default:if(Zu(e)){for(var mt in n)O=n[mt],n.hasOwnProperty(mt)&&O!==void 0&&!l.hasOwnProperty(mt)&&xc(t,e,mt,void 0,l,O);for(w in l)O=l[w],R=n[w],!l.hasOwnProperty(w)||O===R||O===void 0&&R===void 0||xc(t,e,w,O,l,R);return}}for(var S in n)O=n[S],n.hasOwnProperty(S)&&O!=null&&!l.hasOwnProperty(S)&&pt(t,e,S,null,l,O);for(U in l)O=l[U],R=n[U],!l.hasOwnProperty(U)||O===R||O==null&&R==null||pt(t,e,U,O,l,R)}var Cc=null,Uc=null;function $i(t){return t.nodeType===9?t:t.ownerDocument}function Ph(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Ih(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function zc(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var Mc=null;function Mm(){var t=window.event;return t&&t.type==="popstate"?t===Mc?!1:(Mc=t,!0):(Mc=null,!1)}var td=typeof setTimeout=="function"?setTimeout:void 0,Bm=typeof clearTimeout=="function"?clearTimeout:void 0,ed=typeof Promise=="function"?Promise:void 0,qm=typeof queueMicrotask=="function"?queueMicrotask:typeof ed<"u"?function(t){return ed.resolve(null).then(t).catch(jm)}:td;function jm(t){setTimeout(function(){throw t})}function _n(t){return t==="head"}function nd(t,e){var n=e,l=0,i=0;do{var r=n.nextSibling;if(t.removeChild(n),r&&r.nodeType===8)if(n=r.data,n==="/$"){if(0<l&&8>l){n=l;var o=t.ownerDocument;if(n&1&&Ca(o.documentElement),n&2&&Ca(o.body),n&4)for(n=o.head,Ca(n),o=n.firstChild;o;){var h=o.nextSibling,p=o.nodeName;o[Zl]||p==="SCRIPT"||p==="STYLE"||p==="LINK"&&o.rel.toLowerCase()==="stylesheet"||n.removeChild(o),o=h}}if(i===0){t.removeChild(r),Ha(e);return}i--}else n==="$"||n==="$?"||n==="$!"?i++:l=n.charCodeAt(0)-48;else l=0;n=r}while(n);Ha(e)}function Bc(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var n=e;switch(e=e.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Bc(n),Yu(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}t.removeChild(n)}}function Lm(t,e,n,l){for(;t.nodeType===1;){var i=n;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!l&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(l){if(!t[Zl])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(r=t.getAttribute("rel"),r==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(r!==i.rel||t.getAttribute("href")!==(i.href==null||i.href===""?null:i.href)||t.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin)||t.getAttribute("title")!==(i.title==null?null:i.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(r=t.getAttribute("src"),(r!==(i.src==null?null:i.src)||t.getAttribute("type")!==(i.type==null?null:i.type)||t.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin))&&r&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var r=i.name==null?null:""+i.name;if(i.type==="hidden"&&t.getAttribute("name")===r)return t}else return t;if(t=De(t.nextSibling),t===null)break}return null}function Hm(t,e,n){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!n||(t=De(t.nextSibling),t===null))return null;return t}function qc(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function Ym(t,e){var n=t.ownerDocument;if(t.data!=="$?"||n.readyState==="complete")e();else{var l=function(){e(),n.removeEventListener("DOMContentLoaded",l)};n.addEventListener("DOMContentLoaded",l),t._reactRetry=l}}function De(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var jc=null;function ld(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}function ad(t,e,n){switch(e=$i(n),t){case"html":if(t=e.documentElement,!t)throw Error(c(452));return t;case"head":if(t=e.head,!t)throw Error(c(453));return t;case"body":if(t=e.body,!t)throw Error(c(454));return t;default:throw Error(c(451))}}function Ca(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);Yu(t)}var Te=new Map,id=new Set;function Pi(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var en=Y.d;Y.d={f:Xm,r:Gm,D:Vm,C:Qm,L:km,m:Zm,X:Jm,S:Km,M:Wm};function Xm(){var t=en.f(),e=Vi();return t||e}function Gm(t){var e=el(t);e!==null&&e.tag===5&&e.type==="form"?Of(e):en.r(t)}var Bl=typeof document>"u"?null:document;function ud(t,e,n){var l=Bl;if(l&&typeof e=="string"&&e){var i=ge(e);i='link[rel="'+t+'"][href="'+i+'"]',typeof n=="string"&&(i+='[crossorigin="'+n+'"]'),id.has(i)||(id.add(i),t={rel:t,crossOrigin:n,href:e},l.querySelector(i)===null&&(e=l.createElement("link"),Vt(e,"link",t),jt(e),l.head.appendChild(e)))}}function Vm(t){en.D(t),ud("dns-prefetch",t,null)}function Qm(t,e){en.C(t,e),ud("preconnect",t,e)}function km(t,e,n){en.L(t,e,n);var l=Bl;if(l&&t&&e){var i='link[rel="preload"][as="'+ge(e)+'"]';e==="image"&&n&&n.imageSrcSet?(i+='[imagesrcset="'+ge(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(i+='[imagesizes="'+ge(n.imageSizes)+'"]')):i+='[href="'+ge(t)+'"]';var r=i;switch(e){case"style":r=ql(t);break;case"script":r=jl(t)}Te.has(r)||(t=b({rel:"preload",href:e==="image"&&n&&n.imageSrcSet?void 0:t,as:e},n),Te.set(r,t),l.querySelector(i)!==null||e==="style"&&l.querySelector(Ua(r))||e==="script"&&l.querySelector(za(r))||(e=l.createElement("link"),Vt(e,"link",t),jt(e),l.head.appendChild(e)))}}function Zm(t,e){en.m(t,e);var n=Bl;if(n&&t){var l=e&&typeof e.as=="string"?e.as:"script",i='link[rel="modulepreload"][as="'+ge(l)+'"][href="'+ge(t)+'"]',r=i;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":r=jl(t)}if(!Te.has(r)&&(t=b({rel:"modulepreload",href:t},e),Te.set(r,t),n.querySelector(i)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(za(r)))return}l=n.createElement("link"),Vt(l,"link",t),jt(l),n.head.appendChild(l)}}}function Km(t,e,n){en.S(t,e,n);var l=Bl;if(l&&t){var i=nl(l).hoistableStyles,r=ql(t);e=e||"default";var o=i.get(r);if(!o){var h={loading:0,preload:null};if(o=l.querySelector(Ua(r)))h.loading=5;else{t=b({rel:"stylesheet",href:t,"data-precedence":e},n),(n=Te.get(r))&&Lc(t,n);var p=o=l.createElement("link");jt(p),Vt(p,"link",t),p._p=new Promise(function(A,w){p.onload=A,p.onerror=w}),p.addEventListener("load",function(){h.loading|=1}),p.addEventListener("error",function(){h.loading|=2}),h.loading|=4,Ii(o,e,l)}o={type:"stylesheet",instance:o,count:1,state:h},i.set(r,o)}}}function Jm(t,e){en.X(t,e);var n=Bl;if(n&&t){var l=nl(n).hoistableScripts,i=jl(t),r=l.get(i);r||(r=n.querySelector(za(i)),r||(t=b({src:t,async:!0},e),(e=Te.get(i))&&Hc(t,e),r=n.createElement("script"),jt(r),Vt(r,"link",t),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},l.set(i,r))}}function Wm(t,e){en.M(t,e);var n=Bl;if(n&&t){var l=nl(n).hoistableScripts,i=jl(t),r=l.get(i);r||(r=n.querySelector(za(i)),r||(t=b({src:t,async:!0,type:"module"},e),(e=Te.get(i))&&Hc(t,e),r=n.createElement("script"),jt(r),Vt(r,"link",t),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},l.set(i,r))}}function sd(t,e,n,l){var i=(i=$.current)?Pi(i):null;if(!i)throw Error(c(446));switch(t){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(e=ql(n.href),n=nl(i).hoistableStyles,l=n.get(e),l||(l={type:"style",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){t=ql(n.href);var r=nl(i).hoistableStyles,o=r.get(t);if(o||(i=i.ownerDocument||i,o={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},r.set(t,o),(r=i.querySelector(Ua(t)))&&!r._p&&(o.instance=r,o.state.loading=5),Te.has(t)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Te.set(t,n),r||Fm(i,t,n,o.state))),e&&l===null)throw Error(c(528,""));return o}if(e&&l!==null)throw Error(c(529,""));return null;case"script":return e=n.async,n=n.src,typeof n=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=jl(n),n=nl(i).hoistableScripts,l=n.get(e),l||(l={type:"script",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(c(444,t))}}function ql(t){return'href="'+ge(t)+'"'}function Ua(t){return'link[rel="stylesheet"]['+t+"]"}function cd(t){return b({},t,{"data-precedence":t.precedence,precedence:null})}function Fm(t,e,n,l){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?l.loading=1:(e=t.createElement("link"),l.preload=e,e.addEventListener("load",function(){return l.loading|=1}),e.addEventListener("error",function(){return l.loading|=2}),Vt(e,"link",n),jt(e),t.head.appendChild(e))}function jl(t){return'[src="'+ge(t)+'"]'}function za(t){return"script[async]"+t}function rd(t,e,n){if(e.count++,e.instance===null)switch(e.type){case"style":var l=t.querySelector('style[data-href~="'+ge(n.href)+'"]');if(l)return e.instance=l,jt(l),l;var i=b({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return l=(t.ownerDocument||t).createElement("style"),jt(l),Vt(l,"style",i),Ii(l,n.precedence,t),e.instance=l;case"stylesheet":i=ql(n.href);var r=t.querySelector(Ua(i));if(r)return e.state.loading|=4,e.instance=r,jt(r),r;l=cd(n),(i=Te.get(i))&&Lc(l,i),r=(t.ownerDocument||t).createElement("link"),jt(r);var o=r;return o._p=new Promise(function(h,p){o.onload=h,o.onerror=p}),Vt(r,"link",l),e.state.loading|=4,Ii(r,n.precedence,t),e.instance=r;case"script":return r=jl(n.src),(i=t.querySelector(za(r)))?(e.instance=i,jt(i),i):(l=n,(i=Te.get(r))&&(l=b({},n),Hc(l,i)),t=t.ownerDocument||t,i=t.createElement("script"),jt(i),Vt(i,"link",l),t.head.appendChild(i),e.instance=i);case"void":return null;default:throw Error(c(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(l=e.instance,e.state.loading|=4,Ii(l,n.precedence,t));return e.instance}function Ii(t,e,n){for(var l=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=l.length?l[l.length-1]:null,r=i,o=0;o<l.length;o++){var h=l[o];if(h.dataset.precedence===e)r=h;else if(r!==i)break}r?r.parentNode.insertBefore(t,r.nextSibling):(e=n.nodeType===9?n.head:n,e.insertBefore(t,e.firstChild))}function Lc(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function Hc(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var tu=null;function od(t,e,n){if(tu===null){var l=new Map,i=tu=new Map;i.set(n,l)}else i=tu,l=i.get(n),l||(l=new Map,i.set(n,l));if(l.has(t))return l;for(l.set(t,null),n=n.getElementsByTagName(t),i=0;i<n.length;i++){var r=n[i];if(!(r[Zl]||r[Zt]||t==="link"&&r.getAttribute("rel")==="stylesheet")&&r.namespaceURI!=="http://www.w3.org/2000/svg"){var o=r.getAttribute(e)||"";o=t+o;var h=l.get(o);h?h.push(r):l.set(o,[r])}}return l}function fd(t,e,n){t=t.ownerDocument||t,t.head.insertBefore(n,e==="title"?t.querySelector("head > title"):null)}function $m(t,e,n){if(n===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function hd(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Ma=null;function Pm(){}function Im(t,e,n){if(Ma===null)throw Error(c(475));var l=Ma;if(e.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var i=ql(n.href),r=t.querySelector(Ua(i));if(r){t=r._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(l.count++,l=eu.bind(l),t.then(l,l)),e.state.loading|=4,e.instance=r,jt(r);return}r=t.ownerDocument||t,n=cd(n),(i=Te.get(i))&&Lc(n,i),r=r.createElement("link"),jt(r);var o=r;o._p=new Promise(function(h,p){o.onload=h,o.onerror=p}),Vt(r,"link",n),e.instance=r}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(l.count++,e=eu.bind(l),t.addEventListener("load",e),t.addEventListener("error",e))}}function t0(){if(Ma===null)throw Error(c(475));var t=Ma;return t.stylesheets&&t.count===0&&Yc(t,t.stylesheets),0<t.count?function(e){var n=setTimeout(function(){if(t.stylesheets&&Yc(t,t.stylesheets),t.unsuspend){var l=t.unsuspend;t.unsuspend=null,l()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(n)}}:null}function eu(){if(this.count--,this.count===0){if(this.stylesheets)Yc(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var nu=null;function Yc(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,nu=new Map,e.forEach(e0,t),nu=null,eu.call(t))}function e0(t,e){if(!(e.state.loading&4)){var n=nu.get(t);if(n)var l=n.get(null);else{n=new Map,nu.set(t,n);for(var i=t.querySelectorAll("link[data-precedence],style[data-precedence]"),r=0;r<i.length;r++){var o=i[r];(o.nodeName==="LINK"||o.getAttribute("media")!=="not all")&&(n.set(o.dataset.precedence,o),l=o)}l&&n.set(null,l)}i=e.instance,o=i.getAttribute("data-precedence"),r=n.get(o)||l,r===l&&n.set(null,i),n.set(o,i),this.count++,l=eu.bind(this),i.addEventListener("load",l),i.addEventListener("error",l),r?r.parentNode.insertBefore(i,r.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(i,t.firstChild)),e.state.loading|=4}}var Ba={$$typeof:et,Provider:null,Consumer:null,_currentValue:J,_currentValue2:J,_threadCount:0};function n0(t,e,n,l,i,r,o,h){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=qu(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=qu(0),this.hiddenUpdates=qu(null),this.identifierPrefix=l,this.onUncaughtError=i,this.onCaughtError=r,this.onRecoverableError=o,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=h,this.incompleteTransitions=new Map}function dd(t,e,n,l,i,r,o,h,p,A,w,U){return t=new n0(t,e,n,o,h,p,A,U),e=1,r===!0&&(e|=24),r=oe(3,null,null,e),t.current=r,r.stateNode=t,e=Es(),e.refCount++,t.pooledCache=e,e.refCount++,r.memoizedState={element:l,isDehydrated:n,cache:e},Os(r),t}function yd(t){return t?(t=yl,t):yl}function pd(t,e,n,l,i,r){i=yd(i),l.context===null?l.context=i:l.pendingContext=i,l=rn(e),l.payload={element:n},r=r===void 0?null:r,r!==null&&(l.callback=r),n=on(t,l,e),n!==null&&(pe(n,t,e),fa(n,t,e))}function md(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function Xc(t,e){md(t,e),(t=t.alternate)&&md(t,e)}function gd(t){if(t.tag===13){var e=dl(t,67108864);e!==null&&pe(e,t,67108864),Xc(t,67108864)}}var lu=!0;function l0(t,e,n,l){var i=x.T;x.T=null;var r=Y.p;try{Y.p=2,Gc(t,e,n,l)}finally{Y.p=r,x.T=i}}function a0(t,e,n,l){var i=x.T;x.T=null;var r=Y.p;try{Y.p=8,Gc(t,e,n,l)}finally{Y.p=r,x.T=i}}function Gc(t,e,n,l){if(lu){var i=Vc(l);if(i===null)wc(t,e,l,au,n),bd(t,l);else if(u0(i,t,e,n,l))l.stopPropagation();else if(bd(t,l),e&4&&-1<i0.indexOf(t)){for(;i!==null;){var r=el(i);if(r!==null)switch(r.tag){case 3:if(r=r.stateNode,r.current.memoizedState.isDehydrated){var o=Cn(r.pendingLanes);if(o!==0){var h=r;for(h.pendingLanes|=2,h.entangledLanes|=2;o;){var p=1<<31-ce(o);h.entanglements[1]|=p,o&=~p}qe(r),(ht&6)===0&&(Xi=Ce()+500,Da(0))}}break;case 13:h=dl(r,2),h!==null&&pe(h,r,2),Vi(),Xc(r,2)}if(r=Vc(l),r===null&&wc(t,e,l,au,n),r===i)break;i=r}i!==null&&l.stopPropagation()}else wc(t,e,l,null,n)}}function Vc(t){return t=Ju(t),Qc(t)}var au=null;function Qc(t){if(au=null,t=tl(t),t!==null){var e=d(t);if(e===null)t=null;else{var n=e.tag;if(n===13){if(t=y(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return au=t,null}function vd(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Qy()){case xr:return 2;case Cr:return 8;case Fa:case ky:return 32;case Ur:return 268435456;default:return 32}default:return 32}}var kc=!1,An=null,Tn=null,On=null,qa=new Map,ja=new Map,Rn=[],i0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function bd(t,e){switch(t){case"focusin":case"focusout":An=null;break;case"dragenter":case"dragleave":Tn=null;break;case"mouseover":case"mouseout":On=null;break;case"pointerover":case"pointerout":qa.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":ja.delete(e.pointerId)}}function La(t,e,n,l,i,r){return t===null||t.nativeEvent!==r?(t={blockedOn:e,domEventName:n,eventSystemFlags:l,nativeEvent:r,targetContainers:[i]},e!==null&&(e=el(e),e!==null&&gd(e)),t):(t.eventSystemFlags|=l,e=t.targetContainers,i!==null&&e.indexOf(i)===-1&&e.push(i),t)}function u0(t,e,n,l,i){switch(e){case"focusin":return An=La(An,t,e,n,l,i),!0;case"dragenter":return Tn=La(Tn,t,e,n,l,i),!0;case"mouseover":return On=La(On,t,e,n,l,i),!0;case"pointerover":var r=i.pointerId;return qa.set(r,La(qa.get(r)||null,t,e,n,l,i)),!0;case"gotpointercapture":return r=i.pointerId,ja.set(r,La(ja.get(r)||null,t,e,n,l,i)),!0}return!1}function Sd(t){var e=tl(t.target);if(e!==null){var n=d(e);if(n!==null){if(e=n.tag,e===13){if(e=y(n),e!==null){t.blockedOn=e,Iy(t.priority,function(){if(n.tag===13){var l=ye();l=ju(l);var i=dl(n,l);i!==null&&pe(i,n,l),Xc(n,l)}});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function iu(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=Vc(t.nativeEvent);if(n===null){n=t.nativeEvent;var l=new n.constructor(n.type,n);Ku=l,n.target.dispatchEvent(l),Ku=null}else return e=el(n),e!==null&&gd(e),t.blockedOn=n,!1;e.shift()}return!0}function Ed(t,e,n){iu(t)&&n.delete(e)}function s0(){kc=!1,An!==null&&iu(An)&&(An=null),Tn!==null&&iu(Tn)&&(Tn=null),On!==null&&iu(On)&&(On=null),qa.forEach(Ed),ja.forEach(Ed)}function uu(t,e){t.blockedOn===e&&(t.blockedOn=null,kc||(kc=!0,u.unstable_scheduleCallback(u.unstable_NormalPriority,s0)))}var su=null;function _d(t){su!==t&&(su=t,u.unstable_scheduleCallback(u.unstable_NormalPriority,function(){su===t&&(su=null);for(var e=0;e<t.length;e+=3){var n=t[e],l=t[e+1],i=t[e+2];if(typeof l!="function"){if(Qc(l||n)===null)continue;break}var r=el(n);r!==null&&(t.splice(e,3),e-=3,Qs(r,{pending:!0,data:i,method:n.method,action:l},l,i))}}))}function Ha(t){function e(p){return uu(p,t)}An!==null&&uu(An,t),Tn!==null&&uu(Tn,t),On!==null&&uu(On,t),qa.forEach(e),ja.forEach(e);for(var n=0;n<Rn.length;n++){var l=Rn[n];l.blockedOn===t&&(l.blockedOn=null)}for(;0<Rn.length&&(n=Rn[0],n.blockedOn===null);)Sd(n),n.blockedOn===null&&Rn.shift();if(n=(t.ownerDocument||t).$$reactFormReplay,n!=null)for(l=0;l<n.length;l+=3){var i=n[l],r=n[l+1],o=i[$t]||null;if(typeof r=="function")o||_d(n);else if(o){var h=null;if(r&&r.hasAttribute("formAction")){if(i=r,o=r[$t]||null)h=o.formAction;else if(Qc(i)!==null)continue}else h=o.action;typeof h=="function"?n[l+1]=h:(n.splice(l,3),l-=3),_d(n)}}}function Zc(t){this._internalRoot=t}cu.prototype.render=Zc.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(c(409));var n=e.current,l=ye();pd(n,l,t,e,null,null)},cu.prototype.unmount=Zc.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;pd(t.current,2,null,t,null,null),Vi(),e[In]=null}};function cu(t){this._internalRoot=t}cu.prototype.unstable_scheduleHydration=function(t){if(t){var e=jr();t={blockedOn:null,target:t,priority:e};for(var n=0;n<Rn.length&&e!==0&&e<Rn[n].priority;n++);Rn.splice(n,0,t),n===0&&Sd(t)}};var Ad=a.version;if(Ad!=="19.1.0")throw Error(c(527,Ad,"19.1.0"));Y.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(c(188)):(t=Object.keys(t).join(","),Error(c(268,t)));return t=T(e),t=t!==null?m(t):null,t=t===null?null:t.stateNode,t};var c0={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:x,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ru=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ru.isDisabled&&ru.supportsFiber)try{Vl=ru.inject(c0),se=ru}catch{}}return Xa.createRoot=function(t,e){if(!f(t))throw Error(c(299));var n=!1,l="",i=Hf,r=Yf,o=Xf,h=null;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(l=e.identifierPrefix),e.onUncaughtError!==void 0&&(i=e.onUncaughtError),e.onCaughtError!==void 0&&(r=e.onCaughtError),e.onRecoverableError!==void 0&&(o=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(h=e.unstable_transitionCallbacks)),e=dd(t,1,!1,null,null,n,l,i,r,o,h,null),t[In]=e.current,Dc(t),new Zc(e)},Xa.hydrateRoot=function(t,e,n){if(!f(t))throw Error(c(299));var l=!1,i="",r=Hf,o=Yf,h=Xf,p=null,A=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onUncaughtError!==void 0&&(r=n.onUncaughtError),n.onCaughtError!==void 0&&(o=n.onCaughtError),n.onRecoverableError!==void 0&&(h=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(p=n.unstable_transitionCallbacks),n.formState!==void 0&&(A=n.formState)),e=dd(t,1,!0,e,n??null,l,i,r,o,h,p,A),e.context=yd(null),n=e.current,l=ye(),l=ju(l),i=rn(l),i.callback=null,on(n,i,l),n=l,e.current.lanes=n,kl(e,n),qe(e),t[In]=e.current,Dc(t),new cu(e)},Xa.version="19.1.0",Xa}var Md;function b0(){if(Md)return Wc.exports;Md=1;function u(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(u)}catch(a){console.error(a)}}return u(),Wc.exports=v0(),Wc.exports}var S0=b0();function ly(u,a){return function(){return u.apply(a,arguments)}}const{toString:E0}=Object.prototype,{getPrototypeOf:_r}=Object,{iterator:Au,toStringTag:ay}=Symbol,Tu=(u=>a=>{const s=E0.call(a);return u[s]||(u[s]=s.slice(8,-1).toLowerCase())})(Object.create(null)),xe=u=>(u=u.toLowerCase(),a=>Tu(a)===u),Ou=u=>a=>typeof a===u,{isArray:Yl}=Array,ka=Ou("undefined");function _0(u){return u!==null&&!ka(u)&&u.constructor!==null&&!ka(u.constructor)&&le(u.constructor.isBuffer)&&u.constructor.isBuffer(u)}const iy=xe("ArrayBuffer");function A0(u){let a;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?a=ArrayBuffer.isView(u):a=u&&u.buffer&&iy(u.buffer),a}const T0=Ou("string"),le=Ou("function"),uy=Ou("number"),Ru=u=>u!==null&&typeof u=="object",O0=u=>u===!0||u===!1,du=u=>{if(Tu(u)!=="object")return!1;const a=_r(u);return(a===null||a===Object.prototype||Object.getPrototypeOf(a)===null)&&!(ay in u)&&!(Au in u)},R0=xe("Date"),N0=xe("File"),D0=xe("Blob"),w0=xe("FileList"),x0=u=>Ru(u)&&le(u.pipe),C0=u=>{let a;return u&&(typeof FormData=="function"&&u instanceof FormData||le(u.append)&&((a=Tu(u))==="formdata"||a==="object"&&le(u.toString)&&u.toString()==="[object FormData]"))},U0=xe("URLSearchParams"),[z0,M0,B0,q0]=["ReadableStream","Request","Response","Headers"].map(xe),j0=u=>u.trim?u.trim():u.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Ka(u,a,{allOwnKeys:s=!1}={}){if(u===null||typeof u>"u")return;let c,f;if(typeof u!="object"&&(u=[u]),Yl(u))for(c=0,f=u.length;c<f;c++)a.call(null,u[c],c,u);else{const d=s?Object.getOwnPropertyNames(u):Object.keys(u),y=d.length;let E;for(c=0;c<y;c++)E=d[c],a.call(null,u[E],E,u)}}function sy(u,a){a=a.toLowerCase();const s=Object.keys(u);let c=s.length,f;for(;c-- >0;)if(f=s[c],a===f.toLowerCase())return f;return null}const Wn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,cy=u=>!ka(u)&&u!==Wn;function sr(){const{caseless:u}=cy(this)&&this||{},a={},s=(c,f)=>{const d=u&&sy(a,f)||f;du(a[d])&&du(c)?a[d]=sr(a[d],c):du(c)?a[d]=sr({},c):Yl(c)?a[d]=c.slice():a[d]=c};for(let c=0,f=arguments.length;c<f;c++)arguments[c]&&Ka(arguments[c],s);return a}const L0=(u,a,s,{allOwnKeys:c}={})=>(Ka(a,(f,d)=>{s&&le(f)?u[d]=ly(f,s):u[d]=f},{allOwnKeys:c}),u),H0=u=>(u.charCodeAt(0)===65279&&(u=u.slice(1)),u),Y0=(u,a,s,c)=>{u.prototype=Object.create(a.prototype,c),u.prototype.constructor=u,Object.defineProperty(u,"super",{value:a.prototype}),s&&Object.assign(u.prototype,s)},X0=(u,a,s,c)=>{let f,d,y;const E={};if(a=a||{},u==null)return a;do{for(f=Object.getOwnPropertyNames(u),d=f.length;d-- >0;)y=f[d],(!c||c(y,u,a))&&!E[y]&&(a[y]=u[y],E[y]=!0);u=s!==!1&&_r(u)}while(u&&(!s||s(u,a))&&u!==Object.prototype);return a},G0=(u,a,s)=>{u=String(u),(s===void 0||s>u.length)&&(s=u.length),s-=a.length;const c=u.indexOf(a,s);return c!==-1&&c===s},V0=u=>{if(!u)return null;if(Yl(u))return u;let a=u.length;if(!uy(a))return null;const s=new Array(a);for(;a-- >0;)s[a]=u[a];return s},Q0=(u=>a=>u&&a instanceof u)(typeof Uint8Array<"u"&&_r(Uint8Array)),k0=(u,a)=>{const c=(u&&u[Au]).call(u);let f;for(;(f=c.next())&&!f.done;){const d=f.value;a.call(u,d[0],d[1])}},Z0=(u,a)=>{let s;const c=[];for(;(s=u.exec(a))!==null;)c.push(s);return c},K0=xe("HTMLFormElement"),J0=u=>u.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(s,c,f){return c.toUpperCase()+f}),Bd=(({hasOwnProperty:u})=>(a,s)=>u.call(a,s))(Object.prototype),W0=xe("RegExp"),ry=(u,a)=>{const s=Object.getOwnPropertyDescriptors(u),c={};Ka(s,(f,d)=>{let y;(y=a(f,d,u))!==!1&&(c[d]=y||f)}),Object.defineProperties(u,c)},F0=u=>{ry(u,(a,s)=>{if(le(u)&&["arguments","caller","callee"].indexOf(s)!==-1)return!1;const c=u[s];if(le(c)){if(a.enumerable=!1,"writable"in a){a.writable=!1;return}a.set||(a.set=()=>{throw Error("Can not rewrite read-only method '"+s+"'")})}})},$0=(u,a)=>{const s={},c=f=>{f.forEach(d=>{s[d]=!0})};return Yl(u)?c(u):c(String(u).split(a)),s},P0=()=>{},I0=(u,a)=>u!=null&&Number.isFinite(u=+u)?u:a;function tg(u){return!!(u&&le(u.append)&&u[ay]==="FormData"&&u[Au])}const eg=u=>{const a=new Array(10),s=(c,f)=>{if(Ru(c)){if(a.indexOf(c)>=0)return;if(!("toJSON"in c)){a[f]=c;const d=Yl(c)?[]:{};return Ka(c,(y,E)=>{const T=s(y,f+1);!ka(T)&&(d[E]=T)}),a[f]=void 0,d}}return c};return s(u,0)},ng=xe("AsyncFunction"),lg=u=>u&&(Ru(u)||le(u))&&le(u.then)&&le(u.catch),oy=((u,a)=>u?setImmediate:a?((s,c)=>(Wn.addEventListener("message",({source:f,data:d})=>{f===Wn&&d===s&&c.length&&c.shift()()},!1),f=>{c.push(f),Wn.postMessage(s,"*")}))(`axios@${Math.random()}`,[]):s=>setTimeout(s))(typeof setImmediate=="function",le(Wn.postMessage)),ag=typeof queueMicrotask<"u"?queueMicrotask.bind(Wn):typeof process<"u"&&process.nextTick||oy,ig=u=>u!=null&&le(u[Au]),N={isArray:Yl,isArrayBuffer:iy,isBuffer:_0,isFormData:C0,isArrayBufferView:A0,isString:T0,isNumber:uy,isBoolean:O0,isObject:Ru,isPlainObject:du,isReadableStream:z0,isRequest:M0,isResponse:B0,isHeaders:q0,isUndefined:ka,isDate:R0,isFile:N0,isBlob:D0,isRegExp:W0,isFunction:le,isStream:x0,isURLSearchParams:U0,isTypedArray:Q0,isFileList:w0,forEach:Ka,merge:sr,extend:L0,trim:j0,stripBOM:H0,inherits:Y0,toFlatObject:X0,kindOf:Tu,kindOfTest:xe,endsWith:G0,toArray:V0,forEachEntry:k0,matchAll:Z0,isHTMLForm:K0,hasOwnProperty:Bd,hasOwnProp:Bd,reduceDescriptors:ry,freezeMethods:F0,toObjectSet:$0,toCamelCase:J0,noop:P0,toFiniteNumber:I0,findKey:sy,global:Wn,isContextDefined:cy,isSpecCompliantForm:tg,toJSONObject:eg,isAsyncFn:ng,isThenable:lg,setImmediate:oy,asap:ag,isIterable:ig};function I(u,a,s,c,f){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=u,this.name="AxiosError",a&&(this.code=a),s&&(this.config=s),c&&(this.request=c),f&&(this.response=f,this.status=f.status?f.status:null)}N.inherits(I,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:N.toJSONObject(this.config),code:this.code,status:this.status}}});const fy=I.prototype,hy={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(u=>{hy[u]={value:u}});Object.defineProperties(I,hy);Object.defineProperty(fy,"isAxiosError",{value:!0});I.from=(u,a,s,c,f,d)=>{const y=Object.create(fy);return N.toFlatObject(u,y,function(T){return T!==Error.prototype},E=>E!=="isAxiosError"),I.call(y,u.message,a,s,c,f),y.cause=u,y.name=u.name,d&&Object.assign(y,d),y};const ug=null;function cr(u){return N.isPlainObject(u)||N.isArray(u)}function dy(u){return N.endsWith(u,"[]")?u.slice(0,-2):u}function qd(u,a,s){return u?u.concat(a).map(function(f,d){return f=dy(f),!s&&d?"["+f+"]":f}).join(s?".":""):a}function sg(u){return N.isArray(u)&&!u.some(cr)}const cg=N.toFlatObject(N,{},null,function(a){return/^is[A-Z]/.test(a)});function Nu(u,a,s){if(!N.isObject(u))throw new TypeError("target must be an object");a=a||new FormData,s=N.toFlatObject(s,{metaTokens:!0,dots:!1,indexes:!1},!1,function(j,q){return!N.isUndefined(q[j])});const c=s.metaTokens,f=s.visitor||b,d=s.dots,y=s.indexes,T=(s.Blob||typeof Blob<"u"&&Blob)&&N.isSpecCompliantForm(a);if(!N.isFunction(f))throw new TypeError("visitor must be a function");function m(B){if(B===null)return"";if(N.isDate(B))return B.toISOString();if(N.isBoolean(B))return B.toString();if(!T&&N.isBlob(B))throw new I("Blob is not supported. Use a Buffer instead.");return N.isArrayBuffer(B)||N.isTypedArray(B)?T&&typeof Blob=="function"?new Blob([B]):Buffer.from(B):B}function b(B,j,q){let Q=B;if(B&&!q&&typeof B=="object"){if(N.endsWith(j,"{}"))j=c?j:j.slice(0,-2),B=JSON.stringify(B);else if(N.isArray(B)&&sg(B)||(N.isFileList(B)||N.endsWith(j,"[]"))&&(Q=N.toArray(B)))return j=dy(j),Q.forEach(function(et,Rt){!(N.isUndefined(et)||et===null)&&a.append(y===!0?qd([j],Rt,d):y===null?j:j+"[]",m(et))}),!1}return cr(B)?!0:(a.append(qd(q,j,d),m(B)),!1)}const D=[],z=Object.assign(cg,{defaultVisitor:b,convertValue:m,isVisitable:cr});function L(B,j){if(!N.isUndefined(B)){if(D.indexOf(B)!==-1)throw Error("Circular reference detected in "+j.join("."));D.push(B),N.forEach(B,function(Q,P){(!(N.isUndefined(Q)||Q===null)&&f.call(a,Q,N.isString(P)?P.trim():P,j,z))===!0&&L(Q,j?j.concat(P):[P])}),D.pop()}}if(!N.isObject(u))throw new TypeError("data must be an object");return L(u),a}function jd(u){const a={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(u).replace(/[!'()~]|%20|%00/g,function(c){return a[c]})}function Ar(u,a){this._pairs=[],u&&Nu(u,this,a)}const yy=Ar.prototype;yy.append=function(a,s){this._pairs.push([a,s])};yy.toString=function(a){const s=a?function(c){return a.call(this,c,jd)}:jd;return this._pairs.map(function(f){return s(f[0])+"="+s(f[1])},"").join("&")};function rg(u){return encodeURIComponent(u).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function py(u,a,s){if(!a)return u;const c=s&&s.encode||rg;N.isFunction(s)&&(s={serialize:s});const f=s&&s.serialize;let d;if(f?d=f(a,s):d=N.isURLSearchParams(a)?a.toString():new Ar(a,s).toString(c),d){const y=u.indexOf("#");y!==-1&&(u=u.slice(0,y)),u+=(u.indexOf("?")===-1?"?":"&")+d}return u}class Ld{constructor(){this.handlers=[]}use(a,s,c){return this.handlers.push({fulfilled:a,rejected:s,synchronous:c?c.synchronous:!1,runWhen:c?c.runWhen:null}),this.handlers.length-1}eject(a){this.handlers[a]&&(this.handlers[a]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(a){N.forEach(this.handlers,function(c){c!==null&&a(c)})}}const my={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},og=typeof URLSearchParams<"u"?URLSearchParams:Ar,fg=typeof FormData<"u"?FormData:null,hg=typeof Blob<"u"?Blob:null,dg={isBrowser:!0,classes:{URLSearchParams:og,FormData:fg,Blob:hg},protocols:["http","https","file","blob","url","data"]},Tr=typeof window<"u"&&typeof document<"u",rr=typeof navigator=="object"&&navigator||void 0,yg=Tr&&(!rr||["ReactNative","NativeScript","NS"].indexOf(rr.product)<0),pg=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",mg=Tr&&window.location.href||"http://localhost",gg=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Tr,hasStandardBrowserEnv:yg,hasStandardBrowserWebWorkerEnv:pg,navigator:rr,origin:mg},Symbol.toStringTag,{value:"Module"})),Wt={...gg,...dg};function vg(u,a){return Nu(u,new Wt.classes.URLSearchParams,Object.assign({visitor:function(s,c,f,d){return Wt.isNode&&N.isBuffer(s)?(this.append(c,s.toString("base64")),!1):d.defaultVisitor.apply(this,arguments)}},a))}function bg(u){return N.matchAll(/\w+|\[(\w*)]/g,u).map(a=>a[0]==="[]"?"":a[1]||a[0])}function Sg(u){const a={},s=Object.keys(u);let c;const f=s.length;let d;for(c=0;c<f;c++)d=s[c],a[d]=u[d];return a}function gy(u){function a(s,c,f,d){let y=s[d++];if(y==="__proto__")return!0;const E=Number.isFinite(+y),T=d>=s.length;return y=!y&&N.isArray(f)?f.length:y,T?(N.hasOwnProp(f,y)?f[y]=[f[y],c]:f[y]=c,!E):((!f[y]||!N.isObject(f[y]))&&(f[y]=[]),a(s,c,f[y],d)&&N.isArray(f[y])&&(f[y]=Sg(f[y])),!E)}if(N.isFormData(u)&&N.isFunction(u.entries)){const s={};return N.forEachEntry(u,(c,f)=>{a(bg(c),f,s,0)}),s}return null}function Eg(u,a,s){if(N.isString(u))try{return(a||JSON.parse)(u),N.trim(u)}catch(c){if(c.name!=="SyntaxError")throw c}return(s||JSON.stringify)(u)}const Ja={transitional:my,adapter:["xhr","http","fetch"],transformRequest:[function(a,s){const c=s.getContentType()||"",f=c.indexOf("application/json")>-1,d=N.isObject(a);if(d&&N.isHTMLForm(a)&&(a=new FormData(a)),N.isFormData(a))return f?JSON.stringify(gy(a)):a;if(N.isArrayBuffer(a)||N.isBuffer(a)||N.isStream(a)||N.isFile(a)||N.isBlob(a)||N.isReadableStream(a))return a;if(N.isArrayBufferView(a))return a.buffer;if(N.isURLSearchParams(a))return s.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),a.toString();let E;if(d){if(c.indexOf("application/x-www-form-urlencoded")>-1)return vg(a,this.formSerializer).toString();if((E=N.isFileList(a))||c.indexOf("multipart/form-data")>-1){const T=this.env&&this.env.FormData;return Nu(E?{"files[]":a}:a,T&&new T,this.formSerializer)}}return d||f?(s.setContentType("application/json",!1),Eg(a)):a}],transformResponse:[function(a){const s=this.transitional||Ja.transitional,c=s&&s.forcedJSONParsing,f=this.responseType==="json";if(N.isResponse(a)||N.isReadableStream(a))return a;if(a&&N.isString(a)&&(c&&!this.responseType||f)){const y=!(s&&s.silentJSONParsing)&&f;try{return JSON.parse(a)}catch(E){if(y)throw E.name==="SyntaxError"?I.from(E,I.ERR_BAD_RESPONSE,this,null,this.response):E}}return a}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Wt.classes.FormData,Blob:Wt.classes.Blob},validateStatus:function(a){return a>=200&&a<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};N.forEach(["delete","get","head","post","put","patch"],u=>{Ja.headers[u]={}});const _g=N.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Ag=u=>{const a={};let s,c,f;return u&&u.split(`
`).forEach(function(y){f=y.indexOf(":"),s=y.substring(0,f).trim().toLowerCase(),c=y.substring(f+1).trim(),!(!s||a[s]&&_g[s])&&(s==="set-cookie"?a[s]?a[s].push(c):a[s]=[c]:a[s]=a[s]?a[s]+", "+c:c)}),a},Hd=Symbol("internals");function Ga(u){return u&&String(u).trim().toLowerCase()}function yu(u){return u===!1||u==null?u:N.isArray(u)?u.map(yu):String(u)}function Tg(u){const a=Object.create(null),s=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let c;for(;c=s.exec(u);)a[c[1]]=c[2];return a}const Og=u=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(u.trim());function Ic(u,a,s,c,f){if(N.isFunction(c))return c.call(this,a,s);if(f&&(a=s),!!N.isString(a)){if(N.isString(c))return a.indexOf(c)!==-1;if(N.isRegExp(c))return c.test(a)}}function Rg(u){return u.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(a,s,c)=>s.toUpperCase()+c)}function Ng(u,a){const s=N.toCamelCase(" "+a);["get","set","has"].forEach(c=>{Object.defineProperty(u,c+s,{value:function(f,d,y){return this[c].call(this,a,f,d,y)},configurable:!0})})}let ae=class{constructor(a){a&&this.set(a)}set(a,s,c){const f=this;function d(E,T,m){const b=Ga(T);if(!b)throw new Error("header name must be a non-empty string");const D=N.findKey(f,b);(!D||f[D]===void 0||m===!0||m===void 0&&f[D]!==!1)&&(f[D||T]=yu(E))}const y=(E,T)=>N.forEach(E,(m,b)=>d(m,b,T));if(N.isPlainObject(a)||a instanceof this.constructor)y(a,s);else if(N.isString(a)&&(a=a.trim())&&!Og(a))y(Ag(a),s);else if(N.isObject(a)&&N.isIterable(a)){let E={},T,m;for(const b of a){if(!N.isArray(b))throw TypeError("Object iterator must return a key-value pair");E[m=b[0]]=(T=E[m])?N.isArray(T)?[...T,b[1]]:[T,b[1]]:b[1]}y(E,s)}else a!=null&&d(s,a,c);return this}get(a,s){if(a=Ga(a),a){const c=N.findKey(this,a);if(c){const f=this[c];if(!s)return f;if(s===!0)return Tg(f);if(N.isFunction(s))return s.call(this,f,c);if(N.isRegExp(s))return s.exec(f);throw new TypeError("parser must be boolean|regexp|function")}}}has(a,s){if(a=Ga(a),a){const c=N.findKey(this,a);return!!(c&&this[c]!==void 0&&(!s||Ic(this,this[c],c,s)))}return!1}delete(a,s){const c=this;let f=!1;function d(y){if(y=Ga(y),y){const E=N.findKey(c,y);E&&(!s||Ic(c,c[E],E,s))&&(delete c[E],f=!0)}}return N.isArray(a)?a.forEach(d):d(a),f}clear(a){const s=Object.keys(this);let c=s.length,f=!1;for(;c--;){const d=s[c];(!a||Ic(this,this[d],d,a,!0))&&(delete this[d],f=!0)}return f}normalize(a){const s=this,c={};return N.forEach(this,(f,d)=>{const y=N.findKey(c,d);if(y){s[y]=yu(f),delete s[d];return}const E=a?Rg(d):String(d).trim();E!==d&&delete s[d],s[E]=yu(f),c[E]=!0}),this}concat(...a){return this.constructor.concat(this,...a)}toJSON(a){const s=Object.create(null);return N.forEach(this,(c,f)=>{c!=null&&c!==!1&&(s[f]=a&&N.isArray(c)?c.join(", "):c)}),s}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([a,s])=>a+": "+s).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(a){return a instanceof this?a:new this(a)}static concat(a,...s){const c=new this(a);return s.forEach(f=>c.set(f)),c}static accessor(a){const c=(this[Hd]=this[Hd]={accessors:{}}).accessors,f=this.prototype;function d(y){const E=Ga(y);c[E]||(Ng(f,y),c[E]=!0)}return N.isArray(a)?a.forEach(d):d(a),this}};ae.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);N.reduceDescriptors(ae.prototype,({value:u},a)=>{let s=a[0].toUpperCase()+a.slice(1);return{get:()=>u,set(c){this[s]=c}}});N.freezeMethods(ae);function tr(u,a){const s=this||Ja,c=a||s,f=ae.from(c.headers);let d=c.data;return N.forEach(u,function(E){d=E.call(s,d,f.normalize(),a?a.status:void 0)}),f.normalize(),d}function vy(u){return!!(u&&u.__CANCEL__)}function Xl(u,a,s){I.call(this,u??"canceled",I.ERR_CANCELED,a,s),this.name="CanceledError"}N.inherits(Xl,I,{__CANCEL__:!0});function by(u,a,s){const c=s.config.validateStatus;!s.status||!c||c(s.status)?u(s):a(new I("Request failed with status code "+s.status,[I.ERR_BAD_REQUEST,I.ERR_BAD_RESPONSE][Math.floor(s.status/100)-4],s.config,s.request,s))}function Dg(u){const a=/^([-+\w]{1,25})(:?\/\/|:)/.exec(u);return a&&a[1]||""}function wg(u,a){u=u||10;const s=new Array(u),c=new Array(u);let f=0,d=0,y;return a=a!==void 0?a:1e3,function(T){const m=Date.now(),b=c[d];y||(y=m),s[f]=T,c[f]=m;let D=d,z=0;for(;D!==f;)z+=s[D++],D=D%u;if(f=(f+1)%u,f===d&&(d=(d+1)%u),m-y<a)return;const L=b&&m-b;return L?Math.round(z*1e3/L):void 0}}function xg(u,a){let s=0,c=1e3/a,f,d;const y=(m,b=Date.now())=>{s=b,f=null,d&&(clearTimeout(d),d=null),u.apply(null,m)};return[(...m)=>{const b=Date.now(),D=b-s;D>=c?y(m,b):(f=m,d||(d=setTimeout(()=>{d=null,y(f)},c-D)))},()=>f&&y(f)]}const Eu=(u,a,s=3)=>{let c=0;const f=wg(50,250);return xg(d=>{const y=d.loaded,E=d.lengthComputable?d.total:void 0,T=y-c,m=f(T),b=y<=E;c=y;const D={loaded:y,total:E,progress:E?y/E:void 0,bytes:T,rate:m||void 0,estimated:m&&E&&b?(E-y)/m:void 0,event:d,lengthComputable:E!=null,[a?"download":"upload"]:!0};u(D)},s)},Yd=(u,a)=>{const s=u!=null;return[c=>a[0]({lengthComputable:s,total:u,loaded:c}),a[1]]},Xd=u=>(...a)=>N.asap(()=>u(...a)),Cg=Wt.hasStandardBrowserEnv?((u,a)=>s=>(s=new URL(s,Wt.origin),u.protocol===s.protocol&&u.host===s.host&&(a||u.port===s.port)))(new URL(Wt.origin),Wt.navigator&&/(msie|trident)/i.test(Wt.navigator.userAgent)):()=>!0,Ug=Wt.hasStandardBrowserEnv?{write(u,a,s,c,f,d){const y=[u+"="+encodeURIComponent(a)];N.isNumber(s)&&y.push("expires="+new Date(s).toGMTString()),N.isString(c)&&y.push("path="+c),N.isString(f)&&y.push("domain="+f),d===!0&&y.push("secure"),document.cookie=y.join("; ")},read(u){const a=document.cookie.match(new RegExp("(^|;\\s*)("+u+")=([^;]*)"));return a?decodeURIComponent(a[3]):null},remove(u){this.write(u,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function zg(u){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(u)}function Mg(u,a){return a?u.replace(/\/?\/$/,"")+"/"+a.replace(/^\/+/,""):u}function Sy(u,a,s){let c=!zg(a);return u&&(c||s==!1)?Mg(u,a):a}const Gd=u=>u instanceof ae?{...u}:u;function $n(u,a){a=a||{};const s={};function c(m,b,D,z){return N.isPlainObject(m)&&N.isPlainObject(b)?N.merge.call({caseless:z},m,b):N.isPlainObject(b)?N.merge({},b):N.isArray(b)?b.slice():b}function f(m,b,D,z){if(N.isUndefined(b)){if(!N.isUndefined(m))return c(void 0,m,D,z)}else return c(m,b,D,z)}function d(m,b){if(!N.isUndefined(b))return c(void 0,b)}function y(m,b){if(N.isUndefined(b)){if(!N.isUndefined(m))return c(void 0,m)}else return c(void 0,b)}function E(m,b,D){if(D in a)return c(m,b);if(D in u)return c(void 0,m)}const T={url:d,method:d,data:d,baseURL:y,transformRequest:y,transformResponse:y,paramsSerializer:y,timeout:y,timeoutMessage:y,withCredentials:y,withXSRFToken:y,adapter:y,responseType:y,xsrfCookieName:y,xsrfHeaderName:y,onUploadProgress:y,onDownloadProgress:y,decompress:y,maxContentLength:y,maxBodyLength:y,beforeRedirect:y,transport:y,httpAgent:y,httpsAgent:y,cancelToken:y,socketPath:y,responseEncoding:y,validateStatus:E,headers:(m,b,D)=>f(Gd(m),Gd(b),D,!0)};return N.forEach(Object.keys(Object.assign({},u,a)),function(b){const D=T[b]||f,z=D(u[b],a[b],b);N.isUndefined(z)&&D!==E||(s[b]=z)}),s}const Ey=u=>{const a=$n({},u);let{data:s,withXSRFToken:c,xsrfHeaderName:f,xsrfCookieName:d,headers:y,auth:E}=a;a.headers=y=ae.from(y),a.url=py(Sy(a.baseURL,a.url,a.allowAbsoluteUrls),u.params,u.paramsSerializer),E&&y.set("Authorization","Basic "+btoa((E.username||"")+":"+(E.password?unescape(encodeURIComponent(E.password)):"")));let T;if(N.isFormData(s)){if(Wt.hasStandardBrowserEnv||Wt.hasStandardBrowserWebWorkerEnv)y.setContentType(void 0);else if((T=y.getContentType())!==!1){const[m,...b]=T?T.split(";").map(D=>D.trim()).filter(Boolean):[];y.setContentType([m||"multipart/form-data",...b].join("; "))}}if(Wt.hasStandardBrowserEnv&&(c&&N.isFunction(c)&&(c=c(a)),c||c!==!1&&Cg(a.url))){const m=f&&d&&Ug.read(d);m&&y.set(f,m)}return a},Bg=typeof XMLHttpRequest<"u",qg=Bg&&function(u){return new Promise(function(s,c){const f=Ey(u);let d=f.data;const y=ae.from(f.headers).normalize();let{responseType:E,onUploadProgress:T,onDownloadProgress:m}=f,b,D,z,L,B;function j(){L&&L(),B&&B(),f.cancelToken&&f.cancelToken.unsubscribe(b),f.signal&&f.signal.removeEventListener("abort",b)}let q=new XMLHttpRequest;q.open(f.method.toUpperCase(),f.url,!0),q.timeout=f.timeout;function Q(){if(!q)return;const et=ae.from("getAllResponseHeaders"in q&&q.getAllResponseHeaders()),F={data:!E||E==="text"||E==="json"?q.responseText:q.response,status:q.status,statusText:q.statusText,headers:et,config:u,request:q};by(function(Nt){s(Nt),j()},function(Nt){c(Nt),j()},F),q=null}"onloadend"in q?q.onloadend=Q:q.onreadystatechange=function(){!q||q.readyState!==4||q.status===0&&!(q.responseURL&&q.responseURL.indexOf("file:")===0)||setTimeout(Q)},q.onabort=function(){q&&(c(new I("Request aborted",I.ECONNABORTED,u,q)),q=null)},q.onerror=function(){c(new I("Network Error",I.ERR_NETWORK,u,q)),q=null},q.ontimeout=function(){let Rt=f.timeout?"timeout of "+f.timeout+"ms exceeded":"timeout exceeded";const F=f.transitional||my;f.timeoutErrorMessage&&(Rt=f.timeoutErrorMessage),c(new I(Rt,F.clarifyTimeoutError?I.ETIMEDOUT:I.ECONNABORTED,u,q)),q=null},d===void 0&&y.setContentType(null),"setRequestHeader"in q&&N.forEach(y.toJSON(),function(Rt,F){q.setRequestHeader(F,Rt)}),N.isUndefined(f.withCredentials)||(q.withCredentials=!!f.withCredentials),E&&E!=="json"&&(q.responseType=f.responseType),m&&([z,B]=Eu(m,!0),q.addEventListener("progress",z)),T&&q.upload&&([D,L]=Eu(T),q.upload.addEventListener("progress",D),q.upload.addEventListener("loadend",L)),(f.cancelToken||f.signal)&&(b=et=>{q&&(c(!et||et.type?new Xl(null,u,q):et),q.abort(),q=null)},f.cancelToken&&f.cancelToken.subscribe(b),f.signal&&(f.signal.aborted?b():f.signal.addEventListener("abort",b)));const P=Dg(f.url);if(P&&Wt.protocols.indexOf(P)===-1){c(new I("Unsupported protocol "+P+":",I.ERR_BAD_REQUEST,u));return}q.send(d||null)})},jg=(u,a)=>{const{length:s}=u=u?u.filter(Boolean):[];if(a||s){let c=new AbortController,f;const d=function(m){if(!f){f=!0,E();const b=m instanceof Error?m:this.reason;c.abort(b instanceof I?b:new Xl(b instanceof Error?b.message:b))}};let y=a&&setTimeout(()=>{y=null,d(new I(`timeout ${a} of ms exceeded`,I.ETIMEDOUT))},a);const E=()=>{u&&(y&&clearTimeout(y),y=null,u.forEach(m=>{m.unsubscribe?m.unsubscribe(d):m.removeEventListener("abort",d)}),u=null)};u.forEach(m=>m.addEventListener("abort",d));const{signal:T}=c;return T.unsubscribe=()=>N.asap(E),T}},Lg=function*(u,a){let s=u.byteLength;if(s<a){yield u;return}let c=0,f;for(;c<s;)f=c+a,yield u.slice(c,f),c=f},Hg=async function*(u,a){for await(const s of Yg(u))yield*Lg(s,a)},Yg=async function*(u){if(u[Symbol.asyncIterator]){yield*u;return}const a=u.getReader();try{for(;;){const{done:s,value:c}=await a.read();if(s)break;yield c}}finally{await a.cancel()}},Vd=(u,a,s,c)=>{const f=Hg(u,a);let d=0,y,E=T=>{y||(y=!0,c&&c(T))};return new ReadableStream({async pull(T){try{const{done:m,value:b}=await f.next();if(m){E(),T.close();return}let D=b.byteLength;if(s){let z=d+=D;s(z)}T.enqueue(new Uint8Array(b))}catch(m){throw E(m),m}},cancel(T){return E(T),f.return()}},{highWaterMark:2})},Du=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",_y=Du&&typeof ReadableStream=="function",Xg=Du&&(typeof TextEncoder=="function"?(u=>a=>u.encode(a))(new TextEncoder):async u=>new Uint8Array(await new Response(u).arrayBuffer())),Ay=(u,...a)=>{try{return!!u(...a)}catch{return!1}},Gg=_y&&Ay(()=>{let u=!1;const a=new Request(Wt.origin,{body:new ReadableStream,method:"POST",get duplex(){return u=!0,"half"}}).headers.has("Content-Type");return u&&!a}),Qd=64*1024,or=_y&&Ay(()=>N.isReadableStream(new Response("").body)),_u={stream:or&&(u=>u.body)};Du&&(u=>{["text","arrayBuffer","blob","formData","stream"].forEach(a=>{!_u[a]&&(_u[a]=N.isFunction(u[a])?s=>s[a]():(s,c)=>{throw new I(`Response type '${a}' is not supported`,I.ERR_NOT_SUPPORT,c)})})})(new Response);const Vg=async u=>{if(u==null)return 0;if(N.isBlob(u))return u.size;if(N.isSpecCompliantForm(u))return(await new Request(Wt.origin,{method:"POST",body:u}).arrayBuffer()).byteLength;if(N.isArrayBufferView(u)||N.isArrayBuffer(u))return u.byteLength;if(N.isURLSearchParams(u)&&(u=u+""),N.isString(u))return(await Xg(u)).byteLength},Qg=async(u,a)=>{const s=N.toFiniteNumber(u.getContentLength());return s??Vg(a)},kg=Du&&(async u=>{let{url:a,method:s,data:c,signal:f,cancelToken:d,timeout:y,onDownloadProgress:E,onUploadProgress:T,responseType:m,headers:b,withCredentials:D="same-origin",fetchOptions:z}=Ey(u);m=m?(m+"").toLowerCase():"text";let L=jg([f,d&&d.toAbortSignal()],y),B;const j=L&&L.unsubscribe&&(()=>{L.unsubscribe()});let q;try{if(T&&Gg&&s!=="get"&&s!=="head"&&(q=await Qg(b,c))!==0){let F=new Request(a,{method:"POST",body:c,duplex:"half"}),At;if(N.isFormData(c)&&(At=F.headers.get("content-type"))&&b.setContentType(At),F.body){const[Nt,qt]=Yd(q,Eu(Xd(T)));c=Vd(F.body,Qd,Nt,qt)}}N.isString(D)||(D=D?"include":"omit");const Q="credentials"in Request.prototype;B=new Request(a,{...z,signal:L,method:s.toUpperCase(),headers:b.normalize().toJSON(),body:c,duplex:"half",credentials:Q?D:void 0});let P=await fetch(B,z);const et=or&&(m==="stream"||m==="response");if(or&&(E||et&&j)){const F={};["status","statusText","headers"].forEach(ie=>{F[ie]=P[ie]});const At=N.toFiniteNumber(P.headers.get("content-length")),[Nt,qt]=E&&Yd(At,Eu(Xd(E),!0))||[];P=new Response(Vd(P.body,Qd,Nt,()=>{qt&&qt(),j&&j()}),F)}m=m||"text";let Rt=await _u[N.findKey(_u,m)||"text"](P,u);return!et&&j&&j(),await new Promise((F,At)=>{by(F,At,{data:Rt,headers:ae.from(P.headers),status:P.status,statusText:P.statusText,config:u,request:B})})}catch(Q){throw j&&j(),Q&&Q.name==="TypeError"&&/Load failed|fetch/i.test(Q.message)?Object.assign(new I("Network Error",I.ERR_NETWORK,u,B),{cause:Q.cause||Q}):I.from(Q,Q&&Q.code,u,B)}}),fr={http:ug,xhr:qg,fetch:kg};N.forEach(fr,(u,a)=>{if(u){try{Object.defineProperty(u,"name",{value:a})}catch{}Object.defineProperty(u,"adapterName",{value:a})}});const kd=u=>`- ${u}`,Zg=u=>N.isFunction(u)||u===null||u===!1,Ty={getAdapter:u=>{u=N.isArray(u)?u:[u];const{length:a}=u;let s,c;const f={};for(let d=0;d<a;d++){s=u[d];let y;if(c=s,!Zg(s)&&(c=fr[(y=String(s)).toLowerCase()],c===void 0))throw new I(`Unknown adapter '${y}'`);if(c)break;f[y||"#"+d]=c}if(!c){const d=Object.entries(f).map(([E,T])=>`adapter ${E} `+(T===!1?"is not supported by the environment":"is not available in the build"));let y=a?d.length>1?`since :
`+d.map(kd).join(`
`):" "+kd(d[0]):"as no adapter specified";throw new I("There is no suitable adapter to dispatch the request "+y,"ERR_NOT_SUPPORT")}return c},adapters:fr};function er(u){if(u.cancelToken&&u.cancelToken.throwIfRequested(),u.signal&&u.signal.aborted)throw new Xl(null,u)}function Zd(u){return er(u),u.headers=ae.from(u.headers),u.data=tr.call(u,u.transformRequest),["post","put","patch"].indexOf(u.method)!==-1&&u.headers.setContentType("application/x-www-form-urlencoded",!1),Ty.getAdapter(u.adapter||Ja.adapter)(u).then(function(c){return er(u),c.data=tr.call(u,u.transformResponse,c),c.headers=ae.from(c.headers),c},function(c){return vy(c)||(er(u),c&&c.response&&(c.response.data=tr.call(u,u.transformResponse,c.response),c.response.headers=ae.from(c.response.headers))),Promise.reject(c)})}const Oy="1.10.0",wu={};["object","boolean","number","function","string","symbol"].forEach((u,a)=>{wu[u]=function(c){return typeof c===u||"a"+(a<1?"n ":" ")+u}});const Kd={};wu.transitional=function(a,s,c){function f(d,y){return"[Axios v"+Oy+"] Transitional option '"+d+"'"+y+(c?". "+c:"")}return(d,y,E)=>{if(a===!1)throw new I(f(y," has been removed"+(s?" in "+s:"")),I.ERR_DEPRECATED);return s&&!Kd[y]&&(Kd[y]=!0,console.warn(f(y," has been deprecated since v"+s+" and will be removed in the near future"))),a?a(d,y,E):!0}};wu.spelling=function(a){return(s,c)=>(console.warn(`${c} is likely a misspelling of ${a}`),!0)};function Kg(u,a,s){if(typeof u!="object")throw new I("options must be an object",I.ERR_BAD_OPTION_VALUE);const c=Object.keys(u);let f=c.length;for(;f-- >0;){const d=c[f],y=a[d];if(y){const E=u[d],T=E===void 0||y(E,d,u);if(T!==!0)throw new I("option "+d+" must be "+T,I.ERR_BAD_OPTION_VALUE);continue}if(s!==!0)throw new I("Unknown option "+d,I.ERR_BAD_OPTION)}}const pu={assertOptions:Kg,validators:wu},je=pu.validators;let Fn=class{constructor(a){this.defaults=a||{},this.interceptors={request:new Ld,response:new Ld}}async request(a,s){try{return await this._request(a,s)}catch(c){if(c instanceof Error){let f={};Error.captureStackTrace?Error.captureStackTrace(f):f=new Error;const d=f.stack?f.stack.replace(/^.+\n/,""):"";try{c.stack?d&&!String(c.stack).endsWith(d.replace(/^.+\n.+\n/,""))&&(c.stack+=`
`+d):c.stack=d}catch{}}throw c}}_request(a,s){typeof a=="string"?(s=s||{},s.url=a):s=a||{},s=$n(this.defaults,s);const{transitional:c,paramsSerializer:f,headers:d}=s;c!==void 0&&pu.assertOptions(c,{silentJSONParsing:je.transitional(je.boolean),forcedJSONParsing:je.transitional(je.boolean),clarifyTimeoutError:je.transitional(je.boolean)},!1),f!=null&&(N.isFunction(f)?s.paramsSerializer={serialize:f}:pu.assertOptions(f,{encode:je.function,serialize:je.function},!0)),s.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?s.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:s.allowAbsoluteUrls=!0),pu.assertOptions(s,{baseUrl:je.spelling("baseURL"),withXsrfToken:je.spelling("withXSRFToken")},!0),s.method=(s.method||this.defaults.method||"get").toLowerCase();let y=d&&N.merge(d.common,d[s.method]);d&&N.forEach(["delete","get","head","post","put","patch","common"],B=>{delete d[B]}),s.headers=ae.concat(y,d);const E=[];let T=!0;this.interceptors.request.forEach(function(j){typeof j.runWhen=="function"&&j.runWhen(s)===!1||(T=T&&j.synchronous,E.unshift(j.fulfilled,j.rejected))});const m=[];this.interceptors.response.forEach(function(j){m.push(j.fulfilled,j.rejected)});let b,D=0,z;if(!T){const B=[Zd.bind(this),void 0];for(B.unshift.apply(B,E),B.push.apply(B,m),z=B.length,b=Promise.resolve(s);D<z;)b=b.then(B[D++],B[D++]);return b}z=E.length;let L=s;for(D=0;D<z;){const B=E[D++],j=E[D++];try{L=B(L)}catch(q){j.call(this,q);break}}try{b=Zd.call(this,L)}catch(B){return Promise.reject(B)}for(D=0,z=m.length;D<z;)b=b.then(m[D++],m[D++]);return b}getUri(a){a=$n(this.defaults,a);const s=Sy(a.baseURL,a.url,a.allowAbsoluteUrls);return py(s,a.params,a.paramsSerializer)}};N.forEach(["delete","get","head","options"],function(a){Fn.prototype[a]=function(s,c){return this.request($n(c||{},{method:a,url:s,data:(c||{}).data}))}});N.forEach(["post","put","patch"],function(a){function s(c){return function(d,y,E){return this.request($n(E||{},{method:a,headers:c?{"Content-Type":"multipart/form-data"}:{},url:d,data:y}))}}Fn.prototype[a]=s(),Fn.prototype[a+"Form"]=s(!0)});let Jg=class Ry{constructor(a){if(typeof a!="function")throw new TypeError("executor must be a function.");let s;this.promise=new Promise(function(d){s=d});const c=this;this.promise.then(f=>{if(!c._listeners)return;let d=c._listeners.length;for(;d-- >0;)c._listeners[d](f);c._listeners=null}),this.promise.then=f=>{let d;const y=new Promise(E=>{c.subscribe(E),d=E}).then(f);return y.cancel=function(){c.unsubscribe(d)},y},a(function(d,y,E){c.reason||(c.reason=new Xl(d,y,E),s(c.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(a){if(this.reason){a(this.reason);return}this._listeners?this._listeners.push(a):this._listeners=[a]}unsubscribe(a){if(!this._listeners)return;const s=this._listeners.indexOf(a);s!==-1&&this._listeners.splice(s,1)}toAbortSignal(){const a=new AbortController,s=c=>{a.abort(c)};return this.subscribe(s),a.signal.unsubscribe=()=>this.unsubscribe(s),a.signal}static source(){let a;return{token:new Ry(function(f){a=f}),cancel:a}}};function Wg(u){return function(s){return u.apply(null,s)}}function Fg(u){return N.isObject(u)&&u.isAxiosError===!0}const hr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(hr).forEach(([u,a])=>{hr[a]=u});function Ny(u){const a=new Fn(u),s=ly(Fn.prototype.request,a);return N.extend(s,Fn.prototype,a,{allOwnKeys:!0}),N.extend(s,a,null,{allOwnKeys:!0}),s.create=function(f){return Ny($n(u,f))},s}const xt=Ny(Ja);xt.Axios=Fn;xt.CanceledError=Xl;xt.CancelToken=Jg;xt.isCancel=vy;xt.VERSION=Oy;xt.toFormData=Nu;xt.AxiosError=I;xt.Cancel=xt.CanceledError;xt.all=function(a){return Promise.all(a)};xt.spread=Wg;xt.isAxiosError=Fg;xt.mergeConfig=$n;xt.AxiosHeaders=ae;xt.formToJSON=u=>gy(N.isHTMLForm(u)?new FormData(u):u);xt.getAdapter=Ty.getAdapter;xt.HttpStatusCode=hr;xt.default=xt;const{Axios:ob,AxiosError:fb,CanceledError:hb,isCancel:db,CancelToken:yb,VERSION:pb,all:mb,Cancel:gb,isAxiosError:vb,spread:bb,toFormData:Sb,AxiosHeaders:Eb,HttpStatusCode:_b,formToJSON:Ab,getAdapter:Tb,mergeConfig:Ob}=xt;class $g{api;authToken;constructor(a){this.authToken=a.token,this.api=xt.create({baseURL:a.baseURL||"http://localhost:5000/api/v1",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.authToken}`},timeout:3e4}),this.api.interceptors.response.use(s=>s,s=>Promise.reject(this.handleError(s)))}handleError(a){if(a.response){const s=a.response.data;return{error:s?.error||"API Error",code:s?.code||"UNKNOWN_ERROR",details:{status:a.response.status,statusText:a.response.statusText,...s?.details}}}return a.request?{error:"Network Error - Unable to reach server",code:"NETWORK_ERROR",details:{message:a.message}}:{error:a.message||"Unknown Error",code:"UNKNOWN_ERROR"}}async checkAuth(){try{return await this.getProjects(),!0}catch(a){if(a.details?.status===401)return!1;throw a}}updateAuthToken(a){this.authToken=a,this.api.defaults.headers.Authorization=`Bearer ${a}`}async getProjects(){return(await this.api.get("/projects")).data.projects}async getProject(a){return(await this.api.get(`/projects/${a}`)).data}async createProject(a){return(await this.api.post("/projects",a)).data}async updateProject(a,s){return(await this.api.put(`/projects/${a}`,s)).data}async deleteProject(a){await this.api.delete(`/projects/${a}`)}async getAgents(){return(await this.api.get("/agents")).data.agents}async getAgentStatus(a){return(await this.api.get(`/agents/${a}/status`)).data}async executeWorkflow(a){return(await this.api.post("/agents/execute",a)).data}async analyzeProject(a){return(await this.api.post("/agents/prompt-engineer/analyze",a)).data}async suggestStrategy(a){return(await this.api.post("/agents/prompt-engineer/suggest-strategy",a)).data}async scoreContext(a){return(await this.api.post("/agents/prompt-engineer/score-context",a)).data}async getPromptStrategy(a,s){const c=await this.analyzeProject({project_id:a,current_stage:"research",user_objectives:s}),f=await this.suggestStrategy({analysis_id:"temp_id",domain_context:c.analysis.current_objective,priority_goals:s});return{analysis:c,strategy:f}}}let dr=null;const Pg=u=>{const a=new $g(u);return dr=a,a},Le=()=>{if(!dr)throw new Error("API client not initialized. Call createClient() first.");return dr},He=Object.create(null);He.open="0";He.close="1";He.ping="2";He.pong="3";He.message="4";He.upgrade="5";He.noop="6";const mu=Object.create(null);Object.keys(He).forEach(u=>{mu[He[u]]=u});const yr={type:"error",data:"parser error"},Dy=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",wy=typeof ArrayBuffer=="function",xy=u=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(u):u&&u.buffer instanceof ArrayBuffer,Or=({type:u,data:a},s,c)=>Dy&&a instanceof Blob?s?c(a):Jd(a,c):wy&&(a instanceof ArrayBuffer||xy(a))?s?c(a):Jd(new Blob([a]),c):c(He[u]+(a||"")),Jd=(u,a)=>{const s=new FileReader;return s.onload=function(){const c=s.result.split(",")[1];a("b"+(c||""))},s.readAsDataURL(u)};function Wd(u){return u instanceof Uint8Array?u:u instanceof ArrayBuffer?new Uint8Array(u):new Uint8Array(u.buffer,u.byteOffset,u.byteLength)}let nr;function Ig(u,a){if(Dy&&u.data instanceof Blob)return u.data.arrayBuffer().then(Wd).then(a);if(wy&&(u.data instanceof ArrayBuffer||xy(u.data)))return a(Wd(u.data));Or(u,!1,s=>{nr||(nr=new TextEncoder),a(nr.encode(s))})}const Fd="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Qa=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let u=0;u<Fd.length;u++)Qa[Fd.charCodeAt(u)]=u;const tv=u=>{let a=u.length*.75,s=u.length,c,f=0,d,y,E,T;u[u.length-1]==="="&&(a--,u[u.length-2]==="="&&a--);const m=new ArrayBuffer(a),b=new Uint8Array(m);for(c=0;c<s;c+=4)d=Qa[u.charCodeAt(c)],y=Qa[u.charCodeAt(c+1)],E=Qa[u.charCodeAt(c+2)],T=Qa[u.charCodeAt(c+3)],b[f++]=d<<2|y>>4,b[f++]=(y&15)<<4|E>>2,b[f++]=(E&3)<<6|T&63;return m},ev=typeof ArrayBuffer=="function",Rr=(u,a)=>{if(typeof u!="string")return{type:"message",data:Cy(u,a)};const s=u.charAt(0);return s==="b"?{type:"message",data:nv(u.substring(1),a)}:mu[s]?u.length>1?{type:mu[s],data:u.substring(1)}:{type:mu[s]}:yr},nv=(u,a)=>{if(ev){const s=tv(u);return Cy(s,a)}else return{base64:!0,data:u}},Cy=(u,a)=>{switch(a){case"blob":return u instanceof Blob?u:new Blob([u]);case"arraybuffer":default:return u instanceof ArrayBuffer?u:u.buffer}},Uy="",lv=(u,a)=>{const s=u.length,c=new Array(s);let f=0;u.forEach((d,y)=>{Or(d,!1,E=>{c[y]=E,++f===s&&a(c.join(Uy))})})},av=(u,a)=>{const s=u.split(Uy),c=[];for(let f=0;f<s.length;f++){const d=Rr(s[f],a);if(c.push(d),d.type==="error")break}return c};function iv(){return new TransformStream({transform(u,a){Ig(u,s=>{const c=s.length;let f;if(c<126)f=new Uint8Array(1),new DataView(f.buffer).setUint8(0,c);else if(c<65536){f=new Uint8Array(3);const d=new DataView(f.buffer);d.setUint8(0,126),d.setUint16(1,c)}else{f=new Uint8Array(9);const d=new DataView(f.buffer);d.setUint8(0,127),d.setBigUint64(1,BigInt(c))}u.data&&typeof u.data!="string"&&(f[0]|=128),a.enqueue(f),a.enqueue(s)})}})}let lr;function ou(u){return u.reduce((a,s)=>a+s.length,0)}function fu(u,a){if(u[0].length===a)return u.shift();const s=new Uint8Array(a);let c=0;for(let f=0;f<a;f++)s[f]=u[0][c++],c===u[0].length&&(u.shift(),c=0);return u.length&&c<u[0].length&&(u[0]=u[0].slice(c)),s}function uv(u,a){lr||(lr=new TextDecoder);const s=[];let c=0,f=-1,d=!1;return new TransformStream({transform(y,E){for(s.push(y);;){if(c===0){if(ou(s)<1)break;const T=fu(s,1);d=(T[0]&128)===128,f=T[0]&127,f<126?c=3:f===126?c=1:c=2}else if(c===1){if(ou(s)<2)break;const T=fu(s,2);f=new DataView(T.buffer,T.byteOffset,T.length).getUint16(0),c=3}else if(c===2){if(ou(s)<8)break;const T=fu(s,8),m=new DataView(T.buffer,T.byteOffset,T.length),b=m.getUint32(0);if(b>Math.pow(2,21)-1){E.enqueue(yr);break}f=b*Math.pow(2,32)+m.getUint32(4),c=3}else{if(ou(s)<f)break;const T=fu(s,f);E.enqueue(Rr(d?T:lr.decode(T),a)),c=0}if(f===0||f>u){E.enqueue(yr);break}}}})}const zy=4;function zt(u){if(u)return sv(u)}function sv(u){for(var a in zt.prototype)u[a]=zt.prototype[a];return u}zt.prototype.on=zt.prototype.addEventListener=function(u,a){return this._callbacks=this._callbacks||{},(this._callbacks["$"+u]=this._callbacks["$"+u]||[]).push(a),this};zt.prototype.once=function(u,a){function s(){this.off(u,s),a.apply(this,arguments)}return s.fn=a,this.on(u,s),this};zt.prototype.off=zt.prototype.removeListener=zt.prototype.removeAllListeners=zt.prototype.removeEventListener=function(u,a){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var s=this._callbacks["$"+u];if(!s)return this;if(arguments.length==1)return delete this._callbacks["$"+u],this;for(var c,f=0;f<s.length;f++)if(c=s[f],c===a||c.fn===a){s.splice(f,1);break}return s.length===0&&delete this._callbacks["$"+u],this};zt.prototype.emit=function(u){this._callbacks=this._callbacks||{};for(var a=new Array(arguments.length-1),s=this._callbacks["$"+u],c=1;c<arguments.length;c++)a[c-1]=arguments[c];if(s){s=s.slice(0);for(var c=0,f=s.length;c<f;++c)s[c].apply(this,a)}return this};zt.prototype.emitReserved=zt.prototype.emit;zt.prototype.listeners=function(u){return this._callbacks=this._callbacks||{},this._callbacks["$"+u]||[]};zt.prototype.hasListeners=function(u){return!!this.listeners(u).length};const xu=typeof Promise=="function"&&typeof Promise.resolve=="function"?a=>Promise.resolve().then(a):(a,s)=>s(a,0),Oe=typeof self<"u"?self:typeof window<"u"?window:Function("return this")(),cv="arraybuffer";function My(u,...a){return a.reduce((s,c)=>(u.hasOwnProperty(c)&&(s[c]=u[c]),s),{})}const rv=Oe.setTimeout,ov=Oe.clearTimeout;function Cu(u,a){a.useNativeTimers?(u.setTimeoutFn=rv.bind(Oe),u.clearTimeoutFn=ov.bind(Oe)):(u.setTimeoutFn=Oe.setTimeout.bind(Oe),u.clearTimeoutFn=Oe.clearTimeout.bind(Oe))}const fv=1.33;function hv(u){return typeof u=="string"?dv(u):Math.ceil((u.byteLength||u.size)*fv)}function dv(u){let a=0,s=0;for(let c=0,f=u.length;c<f;c++)a=u.charCodeAt(c),a<128?s+=1:a<2048?s+=2:a<55296||a>=57344?s+=3:(c++,s+=4);return s}function By(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}function yv(u){let a="";for(let s in u)u.hasOwnProperty(s)&&(a.length&&(a+="&"),a+=encodeURIComponent(s)+"="+encodeURIComponent(u[s]));return a}function pv(u){let a={},s=u.split("&");for(let c=0,f=s.length;c<f;c++){let d=s[c].split("=");a[decodeURIComponent(d[0])]=decodeURIComponent(d[1])}return a}class mv extends Error{constructor(a,s,c){super(a),this.description=s,this.context=c,this.type="TransportError"}}class Nr extends zt{constructor(a){super(),this.writable=!1,Cu(this,a),this.opts=a,this.query=a.query,this.socket=a.socket,this.supportsBinary=!a.forceBase64}onError(a,s,c){return super.emitReserved("error",new mv(a,s,c)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(a){this.readyState==="open"&&this.write(a)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(a){const s=Rr(a,this.socket.binaryType);this.onPacket(s)}onPacket(a){super.emitReserved("packet",a)}onClose(a){this.readyState="closed",super.emitReserved("close",a)}pause(a){}createUri(a,s={}){return a+"://"+this._hostname()+this._port()+this.opts.path+this._query(s)}_hostname(){const a=this.opts.hostname;return a.indexOf(":")===-1?a:"["+a+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(a){const s=yv(a);return s.length?"?"+s:""}}class gv extends Nr{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(a){this.readyState="pausing";const s=()=>{this.readyState="paused",a()};if(this._polling||!this.writable){let c=0;this._polling&&(c++,this.once("pollComplete",function(){--c||s()})),this.writable||(c++,this.once("drain",function(){--c||s()}))}else s()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(a){const s=c=>{if(this.readyState==="opening"&&c.type==="open"&&this.onOpen(),c.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(c)};av(a,this.socket.binaryType).forEach(s),this.readyState!=="closed"&&(this._polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this._poll())}doClose(){const a=()=>{this.write([{type:"close"}])};this.readyState==="open"?a():this.once("open",a)}write(a){this.writable=!1,lv(a,s=>{this.doWrite(s,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const a=this.opts.secure?"https":"http",s=this.query||{};return this.opts.timestampRequests!==!1&&(s[this.opts.timestampParam]=By()),!this.supportsBinary&&!s.sid&&(s.b64=1),this.createUri(a,s)}}let qy=!1;try{qy=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}const vv=qy;function bv(){}class Sv extends gv{constructor(a){if(super(a),typeof location<"u"){const s=location.protocol==="https:";let c=location.port;c||(c=s?"443":"80"),this.xd=typeof location<"u"&&a.hostname!==location.hostname||c!==a.port}}doWrite(a,s){const c=this.request({method:"POST",data:a});c.on("success",s),c.on("error",(f,d)=>{this.onError("xhr post error",f,d)})}doPoll(){const a=this.request();a.on("data",this.onData.bind(this)),a.on("error",(s,c)=>{this.onError("xhr poll error",s,c)}),this.pollXhr=a}}let Ll=class gu extends zt{constructor(a,s,c){super(),this.createRequest=a,Cu(this,c),this._opts=c,this._method=c.method||"GET",this._uri=s,this._data=c.data!==void 0?c.data:null,this._create()}_create(){var a;const s=My(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");s.xdomain=!!this._opts.xd;const c=this._xhr=this.createRequest(s);try{c.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){c.setDisableHeaderCheck&&c.setDisableHeaderCheck(!0);for(let f in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(f)&&c.setRequestHeader(f,this._opts.extraHeaders[f])}}catch{}if(this._method==="POST")try{c.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{c.setRequestHeader("Accept","*/*")}catch{}(a=this._opts.cookieJar)===null||a===void 0||a.addCookies(c),"withCredentials"in c&&(c.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(c.timeout=this._opts.requestTimeout),c.onreadystatechange=()=>{var f;c.readyState===3&&((f=this._opts.cookieJar)===null||f===void 0||f.parseCookies(c.getResponseHeader("set-cookie"))),c.readyState===4&&(c.status===200||c.status===1223?this._onLoad():this.setTimeoutFn(()=>{this._onError(typeof c.status=="number"?c.status:0)},0))},c.send(this._data)}catch(f){this.setTimeoutFn(()=>{this._onError(f)},0);return}typeof document<"u"&&(this._index=gu.requestsCount++,gu.requests[this._index]=this)}_onError(a){this.emitReserved("error",a,this._xhr),this._cleanup(!0)}_cleanup(a){if(!(typeof this._xhr>"u"||this._xhr===null)){if(this._xhr.onreadystatechange=bv,a)try{this._xhr.abort()}catch{}typeof document<"u"&&delete gu.requests[this._index],this._xhr=null}}_onLoad(){const a=this._xhr.responseText;a!==null&&(this.emitReserved("data",a),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}};Ll.requestsCount=0;Ll.requests={};if(typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",$d);else if(typeof addEventListener=="function"){const u="onpagehide"in Oe?"pagehide":"unload";addEventListener(u,$d,!1)}}function $d(){for(let u in Ll.requests)Ll.requests.hasOwnProperty(u)&&Ll.requests[u].abort()}const Ev=function(){const u=jy({xdomain:!1});return u&&u.responseType!==null}();class _v extends Sv{constructor(a){super(a);const s=a&&a.forceBase64;this.supportsBinary=Ev&&!s}request(a={}){return Object.assign(a,{xd:this.xd},this.opts),new Ll(jy,this.uri(),a)}}function jy(u){const a=u.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!a||vv))return new XMLHttpRequest}catch{}if(!a)try{return new Oe[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch{}}const Ly=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class Av extends Nr{get name(){return"websocket"}doOpen(){const a=this.uri(),s=this.opts.protocols,c=Ly?{}:My(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(c.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(a,s,c)}catch(f){return this.emitReserved("error",f)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=a=>this.onClose({description:"websocket connection closed",context:a}),this.ws.onmessage=a=>this.onData(a.data),this.ws.onerror=a=>this.onError("websocket error",a)}write(a){this.writable=!1;for(let s=0;s<a.length;s++){const c=a[s],f=s===a.length-1;Or(c,this.supportsBinary,d=>{try{this.doWrite(c,d)}catch{}f&&xu(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const a=this.opts.secure?"wss":"ws",s=this.query||{};return this.opts.timestampRequests&&(s[this.opts.timestampParam]=By()),this.supportsBinary||(s.b64=1),this.createUri(a,s)}}const ar=Oe.WebSocket||Oe.MozWebSocket;class Tv extends Av{createSocket(a,s,c){return Ly?new ar(a,s,c):s?new ar(a,s):new ar(a)}doWrite(a,s){this.ws.send(s)}}class Ov extends Nr{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(a){return this.emitReserved("error",a)}this._transport.closed.then(()=>{this.onClose()}).catch(a=>{this.onError("webtransport error",a)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(a=>{const s=uv(Number.MAX_SAFE_INTEGER,this.socket.binaryType),c=a.readable.pipeThrough(s).getReader(),f=iv();f.readable.pipeTo(a.writable),this._writer=f.writable.getWriter();const d=()=>{c.read().then(({done:E,value:T})=>{E||(this.onPacket(T),d())}).catch(E=>{})};d();const y={type:"open"};this.query.sid&&(y.data=`{"sid":"${this.query.sid}"}`),this._writer.write(y).then(()=>this.onOpen())})})}write(a){this.writable=!1;for(let s=0;s<a.length;s++){const c=a[s],f=s===a.length-1;this._writer.write(c).then(()=>{f&&xu(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var a;(a=this._transport)===null||a===void 0||a.close()}}const Rv={websocket:Tv,webtransport:Ov,polling:_v},Nv=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,Dv=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function pr(u){if(u.length>8e3)throw"URI too long";const a=u,s=u.indexOf("["),c=u.indexOf("]");s!=-1&&c!=-1&&(u=u.substring(0,s)+u.substring(s,c).replace(/:/g,";")+u.substring(c,u.length));let f=Nv.exec(u||""),d={},y=14;for(;y--;)d[Dv[y]]=f[y]||"";return s!=-1&&c!=-1&&(d.source=a,d.host=d.host.substring(1,d.host.length-1).replace(/;/g,":"),d.authority=d.authority.replace("[","").replace("]","").replace(/;/g,":"),d.ipv6uri=!0),d.pathNames=wv(d,d.path),d.queryKey=xv(d,d.query),d}function wv(u,a){const s=/\/{2,9}/g,c=a.replace(s,"/").split("/");return(a.slice(0,1)=="/"||a.length===0)&&c.splice(0,1),a.slice(-1)=="/"&&c.splice(c.length-1,1),c}function xv(u,a){const s={};return a.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(c,f,d){f&&(s[f]=d)}),s}const mr=typeof addEventListener=="function"&&typeof removeEventListener=="function",vu=[];mr&&addEventListener("offline",()=>{vu.forEach(u=>u())},!1);class Dn extends zt{constructor(a,s){if(super(),this.binaryType=cv,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,a&&typeof a=="object"&&(s=a,a=null),a){const c=pr(a);s.hostname=c.host,s.secure=c.protocol==="https"||c.protocol==="wss",s.port=c.port,c.query&&(s.query=c.query)}else s.host&&(s.hostname=pr(s.host).host);Cu(this,s),this.secure=s.secure!=null?s.secure:typeof location<"u"&&location.protocol==="https:",s.hostname&&!s.port&&(s.port=this.secure?"443":"80"),this.hostname=s.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=s.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},s.transports.forEach(c=>{const f=c.prototype.name;this.transports.push(f),this._transportsByName[f]=c}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},s),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=pv(this.opts.query)),mr&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},vu.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(a){const s=Object.assign({},this.opts.query);s.EIO=zy,s.transport=a,this.id&&(s.sid=this.id);const c=Object.assign({},this.opts,{query:s,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[a]);return new this._transportsByName[a](c)}_open(){if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}const a=this.opts.rememberUpgrade&&Dn.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1?"websocket":this.transports[0];this.readyState="opening";const s=this.createTransport(a);s.open(),this.setTransport(s)}setTransport(a){this.transport&&this.transport.removeAllListeners(),this.transport=a,a.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",s=>this._onClose("transport close",s))}onOpen(){this.readyState="open",Dn.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush()}_onPacket(a){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",a),this.emitReserved("heartbeat"),a.type){case"open":this.onHandshake(JSON.parse(a.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const s=new Error("server error");s.code=a.data,this._onError(s);break;case"message":this.emitReserved("data",a.data),this.emitReserved("message",a.data);break}}onHandshake(a){this.emitReserved("handshake",a),this.id=a.sid,this.transport.query.sid=a.sid,this._pingInterval=a.pingInterval,this._pingTimeout=a.pingTimeout,this._maxPayload=a.maxPayload,this.onOpen(),this.readyState!=="closed"&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const a=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+a,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},a),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const a=this._getWritablePackets();this.transport.send(a),this._prevBufferLen=a.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let s=1;for(let c=0;c<this.writeBuffer.length;c++){const f=this.writeBuffer[c].data;if(f&&(s+=hv(f)),c>0&&s>this._maxPayload)return this.writeBuffer.slice(0,c);s+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const a=Date.now()>this._pingTimeoutTime;return a&&(this._pingTimeoutTime=0,xu(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),a}write(a,s,c){return this._sendPacket("message",a,s,c),this}send(a,s,c){return this._sendPacket("message",a,s,c),this}_sendPacket(a,s,c,f){if(typeof s=="function"&&(f=s,s=void 0),typeof c=="function"&&(f=c,c=null),this.readyState==="closing"||this.readyState==="closed")return;c=c||{},c.compress=c.compress!==!1;const d={type:a,data:s,options:c};this.emitReserved("packetCreate",d),this.writeBuffer.push(d),f&&this.once("flush",f),this.flush()}close(){const a=()=>{this._onClose("forced close"),this.transport.close()},s=()=>{this.off("upgrade",s),this.off("upgradeError",s),a()},c=()=>{this.once("upgrade",s),this.once("upgradeError",s)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?c():a()}):this.upgrading?c():a()),this}_onError(a){if(Dn.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==="opening")return this.transports.shift(),this._open();this.emitReserved("error",a),this._onClose("transport error",a)}_onClose(a,s){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing"){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),mr&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const c=vu.indexOf(this._offlineEventListener);c!==-1&&vu.splice(c,1)}this.readyState="closed",this.id=null,this.emitReserved("close",a,s),this.writeBuffer=[],this._prevBufferLen=0}}}Dn.protocol=zy;class Cv extends Dn{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),this.readyState==="open"&&this.opts.upgrade)for(let a=0;a<this._upgrades.length;a++)this._probe(this._upgrades[a])}_probe(a){let s=this.createTransport(a),c=!1;Dn.priorWebsocketSuccess=!1;const f=()=>{c||(s.send([{type:"ping",data:"probe"}]),s.once("packet",D=>{if(!c)if(D.type==="pong"&&D.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",s),!s)return;Dn.priorWebsocketSuccess=s.name==="websocket",this.transport.pause(()=>{c||this.readyState!=="closed"&&(b(),this.setTransport(s),s.send([{type:"upgrade"}]),this.emitReserved("upgrade",s),s=null,this.upgrading=!1,this.flush())})}else{const z=new Error("probe error");z.transport=s.name,this.emitReserved("upgradeError",z)}}))};function d(){c||(c=!0,b(),s.close(),s=null)}const y=D=>{const z=new Error("probe error: "+D);z.transport=s.name,d(),this.emitReserved("upgradeError",z)};function E(){y("transport closed")}function T(){y("socket closed")}function m(D){s&&D.name!==s.name&&d()}const b=()=>{s.removeListener("open",f),s.removeListener("error",y),s.removeListener("close",E),this.off("close",T),this.off("upgrading",m)};s.once("open",f),s.once("error",y),s.once("close",E),this.once("close",T),this.once("upgrading",m),this._upgrades.indexOf("webtransport")!==-1&&a!=="webtransport"?this.setTimeoutFn(()=>{c||s.open()},200):s.open()}onHandshake(a){this._upgrades=this._filterUpgrades(a.upgrades),super.onHandshake(a)}_filterUpgrades(a){const s=[];for(let c=0;c<a.length;c++)~this.transports.indexOf(a[c])&&s.push(a[c]);return s}}let Uv=class extends Cv{constructor(a,s={}){const c=typeof a=="object"?a:s;(!c.transports||c.transports&&typeof c.transports[0]=="string")&&(c.transports=(c.transports||["polling","websocket","webtransport"]).map(f=>Rv[f]).filter(f=>!!f)),super(a,c)}};function zv(u,a="",s){let c=u;s=s||typeof location<"u"&&location,u==null&&(u=s.protocol+"//"+s.host),typeof u=="string"&&(u.charAt(0)==="/"&&(u.charAt(1)==="/"?u=s.protocol+u:u=s.host+u),/^(https?|wss?):\/\//.test(u)||(typeof s<"u"?u=s.protocol+"//"+u:u="https://"+u),c=pr(u)),c.port||(/^(http|ws)$/.test(c.protocol)?c.port="80":/^(http|ws)s$/.test(c.protocol)&&(c.port="443")),c.path=c.path||"/";const d=c.host.indexOf(":")!==-1?"["+c.host+"]":c.host;return c.id=c.protocol+"://"+d+":"+c.port+a,c.href=c.protocol+"://"+d+(s&&s.port===c.port?"":":"+c.port),c}const Mv=typeof ArrayBuffer=="function",Bv=u=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(u):u.buffer instanceof ArrayBuffer,Hy=Object.prototype.toString,qv=typeof Blob=="function"||typeof Blob<"u"&&Hy.call(Blob)==="[object BlobConstructor]",jv=typeof File=="function"||typeof File<"u"&&Hy.call(File)==="[object FileConstructor]";function Dr(u){return Mv&&(u instanceof ArrayBuffer||Bv(u))||qv&&u instanceof Blob||jv&&u instanceof File}function bu(u,a){if(!u||typeof u!="object")return!1;if(Array.isArray(u)){for(let s=0,c=u.length;s<c;s++)if(bu(u[s]))return!0;return!1}if(Dr(u))return!0;if(u.toJSON&&typeof u.toJSON=="function"&&arguments.length===1)return bu(u.toJSON(),!0);for(const s in u)if(Object.prototype.hasOwnProperty.call(u,s)&&bu(u[s]))return!0;return!1}function Lv(u){const a=[],s=u.data,c=u;return c.data=gr(s,a),c.attachments=a.length,{packet:c,buffers:a}}function gr(u,a){if(!u)return u;if(Dr(u)){const s={_placeholder:!0,num:a.length};return a.push(u),s}else if(Array.isArray(u)){const s=new Array(u.length);for(let c=0;c<u.length;c++)s[c]=gr(u[c],a);return s}else if(typeof u=="object"&&!(u instanceof Date)){const s={};for(const c in u)Object.prototype.hasOwnProperty.call(u,c)&&(s[c]=gr(u[c],a));return s}return u}function Hv(u,a){return u.data=vr(u.data,a),delete u.attachments,u}function vr(u,a){if(!u)return u;if(u&&u._placeholder===!0){if(typeof u.num=="number"&&u.num>=0&&u.num<a.length)return a[u.num];throw new Error("illegal attachments")}else if(Array.isArray(u))for(let s=0;s<u.length;s++)u[s]=vr(u[s],a);else if(typeof u=="object")for(const s in u)Object.prototype.hasOwnProperty.call(u,s)&&(u[s]=vr(u[s],a));return u}const Yv=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],Xv=5;var st;(function(u){u[u.CONNECT=0]="CONNECT",u[u.DISCONNECT=1]="DISCONNECT",u[u.EVENT=2]="EVENT",u[u.ACK=3]="ACK",u[u.CONNECT_ERROR=4]="CONNECT_ERROR",u[u.BINARY_EVENT=5]="BINARY_EVENT",u[u.BINARY_ACK=6]="BINARY_ACK"})(st||(st={}));class Gv{constructor(a){this.replacer=a}encode(a){return(a.type===st.EVENT||a.type===st.ACK)&&bu(a)?this.encodeAsBinary({type:a.type===st.EVENT?st.BINARY_EVENT:st.BINARY_ACK,nsp:a.nsp,data:a.data,id:a.id}):[this.encodeAsString(a)]}encodeAsString(a){let s=""+a.type;return(a.type===st.BINARY_EVENT||a.type===st.BINARY_ACK)&&(s+=a.attachments+"-"),a.nsp&&a.nsp!=="/"&&(s+=a.nsp+","),a.id!=null&&(s+=a.id),a.data!=null&&(s+=JSON.stringify(a.data,this.replacer)),s}encodeAsBinary(a){const s=Lv(a),c=this.encodeAsString(s.packet),f=s.buffers;return f.unshift(c),f}}function Pd(u){return Object.prototype.toString.call(u)==="[object Object]"}class wr extends zt{constructor(a){super(),this.reviver=a}add(a){let s;if(typeof a=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");s=this.decodeString(a);const c=s.type===st.BINARY_EVENT;c||s.type===st.BINARY_ACK?(s.type=c?st.EVENT:st.ACK,this.reconstructor=new Vv(s),s.attachments===0&&super.emitReserved("decoded",s)):super.emitReserved("decoded",s)}else if(Dr(a)||a.base64)if(this.reconstructor)s=this.reconstructor.takeBinaryData(a),s&&(this.reconstructor=null,super.emitReserved("decoded",s));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+a)}decodeString(a){let s=0;const c={type:Number(a.charAt(0))};if(st[c.type]===void 0)throw new Error("unknown packet type "+c.type);if(c.type===st.BINARY_EVENT||c.type===st.BINARY_ACK){const d=s+1;for(;a.charAt(++s)!=="-"&&s!=a.length;);const y=a.substring(d,s);if(y!=Number(y)||a.charAt(s)!=="-")throw new Error("Illegal attachments");c.attachments=Number(y)}if(a.charAt(s+1)==="/"){const d=s+1;for(;++s&&!(a.charAt(s)===","||s===a.length););c.nsp=a.substring(d,s)}else c.nsp="/";const f=a.charAt(s+1);if(f!==""&&Number(f)==f){const d=s+1;for(;++s;){const y=a.charAt(s);if(y==null||Number(y)!=y){--s;break}if(s===a.length)break}c.id=Number(a.substring(d,s+1))}if(a.charAt(++s)){const d=this.tryParse(a.substr(s));if(wr.isPayloadValid(c.type,d))c.data=d;else throw new Error("invalid payload")}return c}tryParse(a){try{return JSON.parse(a,this.reviver)}catch{return!1}}static isPayloadValid(a,s){switch(a){case st.CONNECT:return Pd(s);case st.DISCONNECT:return s===void 0;case st.CONNECT_ERROR:return typeof s=="string"||Pd(s);case st.EVENT:case st.BINARY_EVENT:return Array.isArray(s)&&(typeof s[0]=="number"||typeof s[0]=="string"&&Yv.indexOf(s[0])===-1);case st.ACK:case st.BINARY_ACK:return Array.isArray(s)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class Vv{constructor(a){this.packet=a,this.buffers=[],this.reconPack=a}takeBinaryData(a){if(this.buffers.push(a),this.buffers.length===this.reconPack.attachments){const s=Hv(this.reconPack,this.buffers);return this.finishedReconstruction(),s}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const Qv=Object.freeze(Object.defineProperty({__proto__:null,Decoder:wr,Encoder:Gv,get PacketType(){return st},protocol:Xv},Symbol.toStringTag,{value:"Module"}));function we(u,a,s){return u.on(a,s),function(){u.off(a,s)}}const kv=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class Yy extends zt{constructor(a,s,c){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=a,this.nsp=s,c&&c.auth&&(this.auth=c.auth),this._opts=Object.assign({},c),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const a=this.io;this.subs=[we(a,"open",this.onopen.bind(this)),we(a,"packet",this.onpacket.bind(this)),we(a,"error",this.onerror.bind(this)),we(a,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...a){return a.unshift("message"),this.emit.apply(this,a),this}emit(a,...s){var c,f,d;if(kv.hasOwnProperty(a))throw new Error('"'+a.toString()+'" is a reserved event name');if(s.unshift(a),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(s),this;const y={type:st.EVENT,data:s};if(y.options={},y.options.compress=this.flags.compress!==!1,typeof s[s.length-1]=="function"){const b=this.ids++,D=s.pop();this._registerAckCallback(b,D),y.id=b}const E=(f=(c=this.io.engine)===null||c===void 0?void 0:c.transport)===null||f===void 0?void 0:f.writable,T=this.connected&&!(!((d=this.io.engine)===null||d===void 0)&&d._hasPingExpired());return this.flags.volatile&&!E||(T?(this.notifyOutgoingListeners(y),this.packet(y)):this.sendBuffer.push(y)),this.flags={},this}_registerAckCallback(a,s){var c;const f=(c=this.flags.timeout)!==null&&c!==void 0?c:this._opts.ackTimeout;if(f===void 0){this.acks[a]=s;return}const d=this.io.setTimeoutFn(()=>{delete this.acks[a];for(let E=0;E<this.sendBuffer.length;E++)this.sendBuffer[E].id===a&&this.sendBuffer.splice(E,1);s.call(this,new Error("operation has timed out"))},f),y=(...E)=>{this.io.clearTimeoutFn(d),s.apply(this,E)};y.withError=!0,this.acks[a]=y}emitWithAck(a,...s){return new Promise((c,f)=>{const d=(y,E)=>y?f(y):c(E);d.withError=!0,s.push(d),this.emit(a,...s)})}_addToQueue(a){let s;typeof a[a.length-1]=="function"&&(s=a.pop());const c={id:this._queueSeq++,tryCount:0,pending:!1,args:a,flags:Object.assign({fromQueue:!0},this.flags)};a.push((f,...d)=>c!==this._queue[0]?void 0:(f!==null?c.tryCount>this._opts.retries&&(this._queue.shift(),s&&s(f)):(this._queue.shift(),s&&s(null,...d)),c.pending=!1,this._drainQueue())),this._queue.push(c),this._drainQueue()}_drainQueue(a=!1){if(!this.connected||this._queue.length===0)return;const s=this._queue[0];s.pending&&!a||(s.pending=!0,s.tryCount++,this.flags=s.flags,this.emit.apply(this,s.args))}packet(a){a.nsp=this.nsp,this.io._packet(a)}onopen(){typeof this.auth=="function"?this.auth(a=>{this._sendConnectPacket(a)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(a){this.packet({type:st.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},a):a})}onerror(a){this.connected||this.emitReserved("connect_error",a)}onclose(a,s){this.connected=!1,delete this.id,this.emitReserved("disconnect",a,s),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(a=>{if(!this.sendBuffer.some(c=>String(c.id)===a)){const c=this.acks[a];delete this.acks[a],c.withError&&c.call(this,new Error("socket has been disconnected"))}})}onpacket(a){if(a.nsp===this.nsp)switch(a.type){case st.CONNECT:a.data&&a.data.sid?this.onconnect(a.data.sid,a.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case st.EVENT:case st.BINARY_EVENT:this.onevent(a);break;case st.ACK:case st.BINARY_ACK:this.onack(a);break;case st.DISCONNECT:this.ondisconnect();break;case st.CONNECT_ERROR:this.destroy();const c=new Error(a.data.message);c.data=a.data.data,this.emitReserved("connect_error",c);break}}onevent(a){const s=a.data||[];a.id!=null&&s.push(this.ack(a.id)),this.connected?this.emitEvent(s):this.receiveBuffer.push(Object.freeze(s))}emitEvent(a){if(this._anyListeners&&this._anyListeners.length){const s=this._anyListeners.slice();for(const c of s)c.apply(this,a)}super.emit.apply(this,a),this._pid&&a.length&&typeof a[a.length-1]=="string"&&(this._lastOffset=a[a.length-1])}ack(a){const s=this;let c=!1;return function(...f){c||(c=!0,s.packet({type:st.ACK,id:a,data:f}))}}onack(a){const s=this.acks[a.id];typeof s=="function"&&(delete this.acks[a.id],s.withError&&a.data.unshift(null),s.apply(this,a.data))}onconnect(a,s){this.id=a,this.recovered=s&&this._pid===s,this._pid=s,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(a=>this.emitEvent(a)),this.receiveBuffer=[],this.sendBuffer.forEach(a=>{this.notifyOutgoingListeners(a),this.packet(a)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(a=>a()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:st.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(a){return this.flags.compress=a,this}get volatile(){return this.flags.volatile=!0,this}timeout(a){return this.flags.timeout=a,this}onAny(a){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(a),this}prependAny(a){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(a),this}offAny(a){if(!this._anyListeners)return this;if(a){const s=this._anyListeners;for(let c=0;c<s.length;c++)if(a===s[c])return s.splice(c,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(a){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(a),this}prependAnyOutgoing(a){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(a),this}offAnyOutgoing(a){if(!this._anyOutgoingListeners)return this;if(a){const s=this._anyOutgoingListeners;for(let c=0;c<s.length;c++)if(a===s[c])return s.splice(c,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(a){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const s=this._anyOutgoingListeners.slice();for(const c of s)c.apply(this,a.data)}}}function Gl(u){u=u||{},this.ms=u.min||100,this.max=u.max||1e4,this.factor=u.factor||2,this.jitter=u.jitter>0&&u.jitter<=1?u.jitter:0,this.attempts=0}Gl.prototype.duration=function(){var u=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var a=Math.random(),s=Math.floor(a*this.jitter*u);u=(Math.floor(a*10)&1)==0?u-s:u+s}return Math.min(u,this.max)|0};Gl.prototype.reset=function(){this.attempts=0};Gl.prototype.setMin=function(u){this.ms=u};Gl.prototype.setMax=function(u){this.max=u};Gl.prototype.setJitter=function(u){this.jitter=u};class br extends zt{constructor(a,s){var c;super(),this.nsps={},this.subs=[],a&&typeof a=="object"&&(s=a,a=void 0),s=s||{},s.path=s.path||"/socket.io",this.opts=s,Cu(this,s),this.reconnection(s.reconnection!==!1),this.reconnectionAttempts(s.reconnectionAttempts||1/0),this.reconnectionDelay(s.reconnectionDelay||1e3),this.reconnectionDelayMax(s.reconnectionDelayMax||5e3),this.randomizationFactor((c=s.randomizationFactor)!==null&&c!==void 0?c:.5),this.backoff=new Gl({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(s.timeout==null?2e4:s.timeout),this._readyState="closed",this.uri=a;const f=s.parser||Qv;this.encoder=new f.Encoder,this.decoder=new f.Decoder,this._autoConnect=s.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(a){return arguments.length?(this._reconnection=!!a,a||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(a){return a===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=a,this)}reconnectionDelay(a){var s;return a===void 0?this._reconnectionDelay:(this._reconnectionDelay=a,(s=this.backoff)===null||s===void 0||s.setMin(a),this)}randomizationFactor(a){var s;return a===void 0?this._randomizationFactor:(this._randomizationFactor=a,(s=this.backoff)===null||s===void 0||s.setJitter(a),this)}reconnectionDelayMax(a){var s;return a===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=a,(s=this.backoff)===null||s===void 0||s.setMax(a),this)}timeout(a){return arguments.length?(this._timeout=a,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(a){if(~this._readyState.indexOf("open"))return this;this.engine=new Uv(this.uri,this.opts);const s=this.engine,c=this;this._readyState="opening",this.skipReconnect=!1;const f=we(s,"open",function(){c.onopen(),a&&a()}),d=E=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",E),a?a(E):this.maybeReconnectOnOpen()},y=we(s,"error",d);if(this._timeout!==!1){const E=this._timeout,T=this.setTimeoutFn(()=>{f(),d(new Error("timeout")),s.close()},E);this.opts.autoUnref&&T.unref(),this.subs.push(()=>{this.clearTimeoutFn(T)})}return this.subs.push(f),this.subs.push(y),this}connect(a){return this.open(a)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const a=this.engine;this.subs.push(we(a,"ping",this.onping.bind(this)),we(a,"data",this.ondata.bind(this)),we(a,"error",this.onerror.bind(this)),we(a,"close",this.onclose.bind(this)),we(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(a){try{this.decoder.add(a)}catch(s){this.onclose("parse error",s)}}ondecoded(a){xu(()=>{this.emitReserved("packet",a)},this.setTimeoutFn)}onerror(a){this.emitReserved("error",a)}socket(a,s){let c=this.nsps[a];return c?this._autoConnect&&!c.active&&c.connect():(c=new Yy(this,a,s),this.nsps[a]=c),c}_destroy(a){const s=Object.keys(this.nsps);for(const c of s)if(this.nsps[c].active)return;this._close()}_packet(a){const s=this.encoder.encode(a);for(let c=0;c<s.length;c++)this.engine.write(s[c],a.options)}cleanup(){this.subs.forEach(a=>a()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(a,s){var c;this.cleanup(),(c=this.engine)===null||c===void 0||c.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",a,s),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const a=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const s=this.backoff.duration();this._reconnecting=!0;const c=this.setTimeoutFn(()=>{a.skipReconnect||(this.emitReserved("reconnect_attempt",a.backoff.attempts),!a.skipReconnect&&a.open(f=>{f?(a._reconnecting=!1,a.reconnect(),this.emitReserved("reconnect_error",f)):a.onreconnect()}))},s);this.opts.autoUnref&&c.unref(),this.subs.push(()=>{this.clearTimeoutFn(c)})}}onreconnect(){const a=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",a)}}const Va={};function Su(u,a){typeof u=="object"&&(a=u,u=void 0),a=a||{};const s=zv(u,a.path||"/socket.io"),c=s.source,f=s.id,d=s.path,y=Va[f]&&d in Va[f].nsps,E=a.forceNew||a["force new connection"]||a.multiplex===!1||y;let T;return E?T=new br(c,a):(Va[f]||(Va[f]=new br(c,a)),T=Va[f]),s.query&&!a.query&&(a.query=s.queryKey),T.socket(s.path,a)}Object.assign(Su,{Manager:br,Socket:Yy,io:Su,connect:Su});class Zv{socket=null;url;reconnectAttempts=0;maxReconnectAttempts=5;reconnectDelay=1e3;listeners=new Map;constructor(a="http://localhost:5000"){this.url=a}connect(){return new Promise((a,s)=>{if(this.socket?.connected){a();return}this.socket=Su(this.url,{transports:["websocket","polling"],timeout:1e4,reconnection:!0,reconnectionAttempts:this.maxReconnectAttempts,reconnectionDelay:this.reconnectDelay}),this.socket.on("connect",()=>{console.log("Connected to SynergyAI backend"),this.reconnectAttempts=0,this.reconnectDelay=1e3,this.notifyListeners("connect"),a()}),this.socket.on("disconnect",c=>{console.log("Disconnected from SynergyAI backend:",c),this.notifyListeners("disconnect")}),this.socket.on("connect_error",c=>{console.error("WebSocket connection error:",c),this.handleReconnect(),this.notifyListeners("connect_error",c),s(c)}),this.setupEventListeners()})}setupEventListeners(){this.socket&&(this.socket.on("agent_status_update",a=>{console.log("Agent status changed:",a),this.notifyListeners("agent_status_update",a)}),this.socket.on("workflow_progress",a=>{console.log("Workflow progress:",a),this.notifyListeners("workflow_progress",a)}),this.socket.on("ai_execution_complete",a=>{console.log("AI execution finished:",a),this.notifyListeners("ai_execution_complete",a)}))}handleReconnect(){this.reconnectAttempts++,this.reconnectAttempts<=this.maxReconnectAttempts&&(console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`),this.reconnectDelay=Math.min(this.reconnectDelay*2,3e4))}notifyListeners(a,s){const c=this.listeners.get(a);c&&c.forEach(f=>{try{f(s)}catch(d){console.error(`Error in WebSocket event listener for ${a}:`,d)}})}on(a,s){this.listeners.has(a)||this.listeners.set(a,new Set),this.listeners.get(a).add(s)}off(a,s){const c=this.listeners.get(a);c&&(c.delete(s),c.size===0&&this.listeners.delete(a))}onAgentStatusUpdate(a){return this.on("agent_status_update",a),()=>this.off("agent_status_update",a)}onWorkflowProgress(a){return this.on("workflow_progress",a),()=>this.off("workflow_progress",a)}onAIExecutionComplete(a){return this.on("ai_execution_complete",a),()=>this.off("ai_execution_complete",a)}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null),this.listeners.clear()}isConnected(){return this.socket?.connected??!1}getConnectionState(){return this.socket?this.socket.connected?"connected":this.socket.disconnected?"disconnected":"connecting":"disconnected"}}let Sr=null;const Kv=u=>{const a=new Zv(u);return Sr=a,a},Id=()=>{if(!Sr)throw new Error("WebSocket client not initialized. Call createWebSocketClient() first.");return Sr},ir={api:{timeout:3e4},websocket:{reconnectAttempts:5,reconnectDelay:1e3}},Jv=()=>({api:{baseURL:"http://localhost:5000/api/v1",timeout:ir.api.timeout},websocket:{url:"http://localhost:5000",reconnectAttempts:ir.websocket.reconnectAttempts,reconnectDelay:ir.websocket.reconnectDelay},auth:{token:"my-secret-token"},features:{analytics:!1,errorReporting:!1,debugWebSocket:!1},development:{devMode:!0}}),Hl=Jv(),Xy=()=>{const u=[];try{new URL(Hl.api.baseURL)}catch{u.push("API base URL is not a valid URL")}try{new URL(Hl.websocket.url)}catch{u.push("WebSocket URL is not a valid URL")}return{valid:u.length===0,errors:u}};{console.log("SynergyAI Configuration:",Hl);const u=Xy();u.valid||console.warn("Configuration validation errors:",u.errors)}const ty=u=>{let a;const s=new Set,c=(m,b)=>{const D=typeof m=="function"?m(a):m;if(!Object.is(D,a)){const z=a;a=b??(typeof D!="object"||D===null)?D:Object.assign({},a,D),s.forEach(L=>L(a,z))}},f=()=>a,E={setState:c,getState:f,getInitialState:()=>T,subscribe:m=>(s.add(m),()=>s.delete(m))},T=a=u(c,f,E);return E},Wv=u=>u?ty(u):ty,Fv=u=>u;function $v(u,a=Fv){const s=Dd.useSyncExternalStore(u.subscribe,()=>a(u.getState()),()=>a(u.getInitialState()));return Dd.useDebugValue(s),s}const Pv=u=>{const a=Wv(u),s=c=>$v(a,c);return Object.assign(s,a),s},Iv=u=>Pv,ey={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1,VITE_API_BASE_URL:"http://localhost:5000/api/v1",VITE_AUTH_TOKEN:"my-secret-token",VITE_DEBUG_WEBSOCKET:"false",VITE_DEV_MODE:"true",VITE_ENABLE_ANALYTICS:"false",VITE_ENABLE_ERROR_REPORTING:"false",VITE_WEBSOCKET_URL:"http://localhost:5000"},Za=new Map,hu=u=>{const a=Za.get(u);return a?Object.fromEntries(Object.entries(a.stores).map(([s,c])=>[s,c.getState()])):{}},tb=(u,a,s)=>{if(u===void 0)return{type:"untracked",connection:a.connect(s)};const c=Za.get(s.name);if(c)return{type:"tracked",store:u,...c};const f={connection:a.connect(s),stores:{}};return Za.set(s.name,f),{type:"tracked",store:u,...f}},eb=(u,a)=>{if(a===void 0)return;const s=Za.get(u);s&&(delete s.stores[a],Object.keys(s.stores).length===0&&Za.delete(u))},nb=u=>{var a,s;if(!u)return;const c=u.split(`
`),f=c.findIndex(y=>y.includes("api.setState"));if(f<0)return;const d=((a=c[f+1])==null?void 0:a.trim())||"";return(s=/.+ (.+) .+/.exec(d))==null?void 0:s[1]},lb=(u,a={})=>(s,c,f)=>{const{enabled:d,anonymousActionType:y,store:E,...T}=a;let m;try{m=(d??(ey?"production":void 0)!=="production")&&window.__REDUX_DEVTOOLS_EXTENSION__}catch{}if(!m)return u(s,c,f);const{connection:b,...D}=tb(E,m,T);let z=!0;f.setState=(j,q,Q)=>{const P=s(j,q);if(!z)return P;const et=Q===void 0?{type:y||nb(new Error().stack)||"anonymous"}:typeof Q=="string"?{type:Q}:Q;return E===void 0?(b?.send(et,c()),P):(b?.send({...et,type:`${E}/${et.type}`},{...hu(T.name),[E]:f.getState()}),P)},f.devtools={cleanup:()=>{b&&typeof b.unsubscribe=="function"&&b.unsubscribe(),eb(T.name,E)}};const L=(...j)=>{const q=z;z=!1,s(...j),z=q},B=u(f.setState,c,f);if(D.type==="untracked"?b?.init(B):(D.stores[D.store]=f,b?.init(Object.fromEntries(Object.entries(D.stores).map(([j,q])=>[j,j===D.store?B:q.getState()])))),f.dispatchFromDevtools&&typeof f.dispatch=="function"){let j=!1;const q=f.dispatch;f.dispatch=(...Q)=>{(ey?"production":void 0)!=="production"&&Q[0].type==="__setState"&&!j&&(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),j=!0),q(...Q)}}return b.subscribe(j=>{var q;switch(j.type){case"ACTION":if(typeof j.payload!="string"){console.error("[zustand devtools middleware] Unsupported action format");return}return ur(j.payload,Q=>{if(Q.type==="__setState"){if(E===void 0){L(Q.state);return}Object.keys(Q.state).length!==1&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format.
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);const P=Q.state[E];if(P==null)return;JSON.stringify(f.getState())!==JSON.stringify(P)&&L(P);return}f.dispatchFromDevtools&&typeof f.dispatch=="function"&&f.dispatch(Q)});case"DISPATCH":switch(j.payload.type){case"RESET":return L(B),E===void 0?b?.init(f.getState()):b?.init(hu(T.name));case"COMMIT":if(E===void 0){b?.init(f.getState());return}return b?.init(hu(T.name));case"ROLLBACK":return ur(j.state,Q=>{if(E===void 0){L(Q),b?.init(f.getState());return}L(Q[E]),b?.init(hu(T.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return ur(j.state,Q=>{if(E===void 0){L(Q);return}JSON.stringify(f.getState())!==JSON.stringify(Q[E])&&L(Q[E])});case"IMPORT_STATE":{const{nextLiftedState:Q}=j.payload,P=(q=Q.computedStates.slice(-1)[0])==null?void 0:q.state;if(!P)return;L(E===void 0?P:P[E]),b?.send(null,Q);return}case"PAUSE_RECORDING":return z=!z}return}}),B},ab=lb,ur=(u,a)=>{let s;try{s=JSON.parse(u)}catch(c){console.error("[zustand devtools middleware] Could not parse the received json",c)}s!==void 0&&a(s)},ny={projects:[],currentProject:null,selectedProjectId:null,agents:[],workflowResults:[],currentStrategy:null,currentAnalysis:null,loading:{isLoading:!1},error:{hasError:!1},sidebarCollapsed:!1,activePanel:"navigator",theme:"light",apiConnected:!1,websocketConnected:!1,lastConnectionCheck:null},ib=Iv()(ab(u=>({...ny,setProjects:a=>u({projects:a},!1,"setProjects"),addProject:a=>u(s=>({projects:[...s.projects,a]}),!1,"addProject"),updateProject:(a,s)=>u(c=>({projects:c.projects.map(f=>f.id===a?{...f,...s}:f),currentProject:c.currentProject?.id===a?{...c.currentProject,...s}:c.currentProject}),!1,"updateProject"),deleteProject:a=>u(s=>({projects:s.projects.filter(c=>c.id!==a),currentProject:s.currentProject?.id===a?null:s.currentProject,selectedProjectId:s.selectedProjectId===a?null:s.selectedProjectId}),!1,"deleteProject"),setCurrentProject:a=>u({currentProject:a},!1,"setCurrentProject"),selectProject:a=>u(s=>({selectedProjectId:a,currentProject:a&&s.projects.find(c=>c.id===a)||null}),!1,"selectProject"),setAgents:a=>u({agents:a},!1,"setAgents"),updateAgent:(a,s)=>u(c=>({agents:c.agents.map(f=>f.agent_id===a?{...f,...s}:f)}),!1,"updateAgent"),addWorkflowResult:a=>u(s=>({workflowResults:[...s.workflowResults,a]}),!1,"addWorkflowResult"),clearWorkflowResults:()=>u({workflowResults:[]},!1,"clearWorkflowResults"),setCurrentStrategy:a=>u({currentStrategy:a},!1,"setCurrentStrategy"),setCurrentAnalysis:a=>u({currentAnalysis:a},!1,"setCurrentAnalysis"),setLoading:a=>u(s=>({loading:{...s.loading,...a}}),!1,"setLoading"),setError:a=>u(s=>({error:{...s.error,...a}}),!1,"setError"),clearError:()=>u({error:{hasError:!1}},!1,"clearError"),toggleSidebar:()=>u(a=>({sidebarCollapsed:!a.sidebarCollapsed}),!1,"toggleSidebar"),setSidebarCollapsed:a=>u({sidebarCollapsed:a},!1,"setSidebarCollapsed"),setActivePanel:a=>u({activePanel:a},!1,"setActivePanel"),setTheme:a=>u({theme:a},!1,"setTheme"),setApiConnected:a=>u({apiConnected:a},!1,"setApiConnected"),setWebsocketConnected:a=>u({websocketConnected:a},!1,"setWebsocketConnected"),updateConnectionCheck:()=>u({lastConnectionCheck:Date.now()},!1,"updateConnectionCheck"),reset:()=>u(ny,!1,"reset")}),{name:"synergy-ai-store"})),ub=()=>{const u=ib(),a=ne.useCallback(async()=>{try{u.setLoading({isLoading:!0,message:"Connecting to SynergyAI..."});const z=await Le().checkAuth();u.setApiConnected(z),z&&await Promise.all([s(),y()]);try{await Id().connect(),u.setWebsocketConnected(!0)}catch(L){console.warn("WebSocket connection failed:",L),u.setWebsocketConnected(!1)}u.updateConnectionCheck()}catch(D){u.setError({hasError:!0,message:"Failed to connect to SynergyAI",details:D}),u.setApiConnected(!1)}finally{u.setLoading({isLoading:!1})}},[u]),s=ne.useCallback(async()=>{try{const z=await Le().getProjects();u.setProjects(z)}catch(D){throw u.setError({hasError:!0,message:"Failed to load projects",details:D}),D}},[u]),c=ne.useCallback(async D=>{try{u.setLoading({isLoading:!0,message:"Creating project..."});const L=await Le().createProject(D);return u.addProject(L),L}catch(z){throw u.setError({hasError:!0,message:"Failed to create project",details:z}),z}finally{u.setLoading({isLoading:!1})}},[u]),f=ne.useCallback(async(D,z)=>{try{u.setLoading({isLoading:!0,message:"Updating project..."});const B=await Le().updateProject(D,z);return u.updateProject(D,B),B}catch(L){throw u.setError({hasError:!0,message:"Failed to update project",details:L}),L}finally{u.setLoading({isLoading:!1})}},[u]),d=ne.useCallback(async D=>{try{u.setLoading({isLoading:!0,message:"Deleting project..."}),await Le().deleteProject(D),u.deleteProject(D)}catch(z){throw u.setError({hasError:!0,message:"Failed to delete project",details:z}),z}finally{u.setLoading({isLoading:!1})}},[u]),y=ne.useCallback(async()=>{try{const z=await Le().getAgents();u.setAgents(z)}catch(D){throw u.setError({hasError:!0,message:"Failed to load agents",details:D}),D}},[u]),E=ne.useCallback(async D=>{try{u.setLoading({isLoading:!0,message:"Executing workflow..."});const L=await Le().executeWorkflow(D);return u.addWorkflowResult(L),L}catch(z){throw u.setError({hasError:!0,message:"Failed to execute workflow",details:z}),z}finally{u.setLoading({isLoading:!1})}},[u]),T=ne.useCallback(async D=>{try{u.setLoading({isLoading:!0,message:"Analyzing project..."});const L=await Le().analyzeProject(D);return u.setCurrentAnalysis(L.analysis),L}catch(z){throw u.setError({hasError:!0,message:"Failed to analyze project",details:z}),z}finally{u.setLoading({isLoading:!1})}},[u]),m=ne.useCallback(async D=>{try{u.setLoading({isLoading:!0,message:"Getting strategy recommendation..."});const L=await Le().suggestStrategy(D);return u.setCurrentStrategy(L.strategy),L}catch(z){throw u.setError({hasError:!0,message:"Failed to get strategy recommendation",details:z}),z}finally{u.setLoading({isLoading:!1})}},[u]),b=ne.useCallback(async(D,z)=>{try{u.setLoading({isLoading:!0,message:"Getting AI strategy..."});const B=await Le().getPromptStrategy(D,z);return u.setCurrentAnalysis(B.analysis.analysis),u.setCurrentStrategy(B.strategy.strategy),B}catch(L){throw u.setError({hasError:!0,message:"Failed to get AI strategy",details:L}),L}finally{u.setLoading({isLoading:!1})}},[u]);return ne.useEffect(()=>{const D=Id(),z=D.onAgentStatusUpdate(j=>{u.updateAgent(j.agent_id,{status:j.status})}),L=D.onWorkflowProgress(j=>{console.log("Workflow progress:",j)}),B=D.onAIExecutionComplete(j=>{console.log("AI execution complete:",j)});return()=>{z(),L(),B()}},[u]),{...u,initializeConnections:a,loadProjects:s,createProject:c,updateProject:f,deleteProject:d,loadAgents:y,executeWorkflow:E,analyzeProject:T,suggestStrategy:m,getPromptStrategy:b,clearError:u.clearError,selectProject:u.selectProject,setActivePanel:u.setActivePanel}};function sb(){const{initializeConnections:u,loading:a,error:s,apiConnected:c,websocketConnected:f}=ub();return ne.useEffect(()=>{const d=Xy();if(!d.valid){console.error("Configuration validation failed:",d.errors);return}Pg({token:Hl.auth.token,baseURL:Hl.api.baseURL}),Kv(Hl.websocket.url),u()},[u]),a.isLoading?ut.jsx("div",{className:"min-h-screen bg-secondary-50 flex items-center justify-center",children:ut.jsxs("div",{className:"text-center",children:[ut.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"}),ut.jsx("p",{className:"text-secondary-600",children:a.message||"Loading SynergyAI..."})]})}):s.hasError?ut.jsx("div",{className:"min-h-screen bg-secondary-50 flex items-center justify-center",children:ut.jsx("div",{className:"max-w-md mx-auto text-center",children:ut.jsxs("div",{className:"bg-error-100 border border-error-200 rounded-lg p-6",children:[ut.jsx("h2",{className:"text-lg font-semibold text-error-800 mb-2",children:"Connection Error"}),ut.jsx("p",{className:"text-error-700 mb-4",children:s.message}),ut.jsx("button",{onClick:()=>window.location.reload(),className:"btn-primary",children:"Retry Connection"})]})})}):ut.jsxs("div",{className:"min-h-screen bg-secondary-50",children:[ut.jsx("header",{className:"bg-white border-b border-secondary-200 px-6 py-4",children:ut.jsx("div",{className:"flex items-center justify-between",children:ut.jsxs("div",{className:"flex items-center space-x-4",children:[ut.jsx("h1",{className:"text-2xl font-bold text-secondary-900",children:"SynergyAI"}),ut.jsxs("div",{className:"flex items-center space-x-2",children:[ut.jsx("div",{className:`w-2 h-2 rounded-full ${c?"bg-success-500":"bg-error-500"}`}),ut.jsxs("span",{className:"text-sm text-secondary-600",children:["API ",c?"Connected":"Disconnected"]}),ut.jsx("div",{className:`w-2 h-2 rounded-full ${f?"bg-success-500":"bg-error-500"}`}),ut.jsxs("span",{className:"text-sm text-secondary-600",children:["WebSocket ",f?"Connected":"Disconnected"]})]})]})})}),ut.jsx("main",{className:"flex h-[calc(100vh-80px)]",children:ut.jsx("div",{className:"flex-1 flex items-center justify-center",children:ut.jsxs("div",{className:"text-center",children:[ut.jsx("h2",{className:"text-3xl font-bold text-secondary-900 mb-4",children:"Welcome to SynergyAI"}),ut.jsx("p",{className:"text-secondary-600 mb-8 max-w-md",children:"Your intelligent project workflow system is ready. The three-panel interface will be implemented in the next steps."}),ut.jsxs("div",{className:"grid grid-cols-3 gap-4 max-w-2xl",children:[ut.jsxs("div",{className:"card p-6 text-center",children:[ut.jsx("h3",{className:"font-semibold text-secondary-900 mb-2",children:"Project Navigator"}),ut.jsx("p",{className:"text-sm text-secondary-600",children:'The "Why" - Manage your projects and goals'})]}),ut.jsxs("div",{className:"card p-6 text-center",children:[ut.jsx("h3",{className:"font-semibold text-secondary-900 mb-2",children:"Workflow Log"}),ut.jsx("p",{className:"text-sm text-secondary-600",children:'The "What/How" - Track AI agent execution'})]}),ut.jsxs("div",{className:"card p-6 text-center",children:[ut.jsx("h3",{className:"font-semibold text-secondary-900 mb-2",children:"Context & Controls"}),ut.jsx("p",{className:"text-sm text-secondary-600",children:'The "Tools" - AI strategy and controls'})]})]})]})})})]})}S0.createRoot(document.getElementById("root")).render(ut.jsx(ne.StrictMode,{children:ut.jsx(sb,{})}));

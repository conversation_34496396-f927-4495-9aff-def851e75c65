// Environment configuration for SynergyAI Frontend

export interface AppConfig {
  api: {
    baseURL: string;
    timeout: number;
  };
  websocket: {
    url: string;
    reconnectAttempts: number;
    reconnectDelay: number;
  };
  auth: {
    token: string;
  };
  features: {
    analytics: boolean;
    errorReporting: boolean;
    debugWebSocket: boolean;
  };
  development: {
    devMode: boolean;
  };
}

// Default configuration
const defaultConfig: AppConfig = {
  api: {
    baseURL: 'http://localhost:5000/api/v1',
    timeout: 30000,
  },
  websocket: {
    url: 'http://localhost:5000',
    reconnectAttempts: 5,
    reconnectDelay: 1000,
  },
  auth: {
    token: 'my-secret-token',
  },
  features: {
    analytics: false,
    errorReporting: false,
    debugWebSocket: false,
  },
  development: {
    devMode: import.meta.env.DEV,
  },
};

// Load configuration from environment variables
const loadConfig = (): AppConfig => {
  return {
    api: {
      baseURL: import.meta.env.VITE_API_BASE_URL || defaultConfig.api.baseURL,
      timeout: defaultConfig.api.timeout,
    },
    websocket: {
      url: import.meta.env.VITE_WEBSOCKET_URL || defaultConfig.websocket.url,
      reconnectAttempts: defaultConfig.websocket.reconnectAttempts,
      reconnectDelay: defaultConfig.websocket.reconnectDelay,
    },
    auth: {
      token: import.meta.env.VITE_AUTH_TOKEN || '',
    },
    features: {
      analytics: import.meta.env.VITE_ENABLE_ANALYTICS === 'true',
      errorReporting: import.meta.env.VITE_ENABLE_ERROR_REPORTING === 'true',
      debugWebSocket: import.meta.env.VITE_DEBUG_WEBSOCKET === 'true',
    },
    development: {
      devMode: import.meta.env.VITE_DEV_MODE === 'true' || import.meta.env.DEV,
    },
  };
};

// Export the configuration
export const config = loadConfig();

// Utility functions
export const isDevelopment = () => config.development.devMode;
export const isProduction = () => !config.development.devMode;

// Validation
export const validateConfig = (): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!config.api.baseURL) {
    errors.push('API base URL is required');
  }

  if (!config.websocket.url) {
    errors.push('WebSocket URL is required');
  }

  // Auth token is optional - some backends don't require authentication
  if (config.auth.token && config.auth.token.length < 3) {
    errors.push('Authentication token is too short');
  }

  try {
    new URL(config.api.baseURL);
  } catch {
    errors.push('API base URL is not a valid URL');
  }

  try {
    new URL(config.websocket.url);
  } catch {
    errors.push('WebSocket URL is not a valid URL');
  }

  return {
    valid: errors.length === 0,
    errors,
  };
};

// Debug logging
if (isDevelopment()) {
  console.log('SynergyAI Configuration:', config);
  
  const validation = validateConfig();
  if (!validation.valid) {
    console.warn('Configuration validation errors:', validation.errors);
  }
}

#!/usr/bin/env python3
"""
Simple backend runner for SynergyAI development
"""
import os
import sys
from flask import Flask
from flask_cors import CORS

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

from app import create_app, db
from config import Config

def run_development_server():
    """Run the development server with CORS enabled"""
    print("🚀 Starting SynergyAI Development Server")
    print("=" * 50)
    
    # Create app
    app = create_app()
    
    # Enable CORS for frontend development
    CORS(app, origins=["http://localhost:3000", "http://127.0.0.1:3000", "file://"])
    
    # Create database tables
    with app.app_context():
        db.create_all()
        print("✅ Database tables created")
    
    print("✅ CORS enabled for frontend development")
    print("✅ Backend API available at: http://localhost:5000")
    print("✅ API endpoints:")
    print("   - GET  /api/v1/projects/")
    print("   - POST /api/v1/projects/")
    print("   - GET  /api/v1/agents/")
    print("   - POST /api/v1/agents/execute")
    print("\n🎯 Ready to receive frontend requests!")
    print("=" * 50)
    
    # Run the server
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        use_reloader=True
    )

if __name__ == "__main__":
    run_development_server()
#!/usr/bin/env python3
"""
Test runner script for SynergyAI Phase 6.2 Testing Implementation
"""
import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """Run a command and handle output"""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {command}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            cwd=Path(__file__).parent
        )
        
        if result.stdout:
            print("STDOUT:")
            print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
        else:
            print(f"❌ {description} failed with return code {result.returncode}")
        
        return result.returncode == 0
    
    except Exception as e:
        print(f"❌ Error running {description}: {e}")
        return False


def main():
    """Main test runner function"""
    print("🚀 SynergyAI Phase 6.2 Testing Strategy Implementation")
    print("=" * 60)
    
    # Change to backend directory
    os.chdir(Path(__file__).parent)
    
    # Test categories to run
    test_categories = [
        {
            "name": "Unit Tests",
            "command": "python -m pytest tests/test_openrouter.py tests/test_agents.py tests/test_models.py -v",
            "description": "Core unit tests for services and models"
        },
        {
            "name": "API Tests", 
            "command": "python -m pytest tests/test_api_endpoints.py -v",
            "description": "API endpoint testing"
        },
        {
            "name": "Integration Tests",
            "command": "python -m pytest tests/test_integration.py -v",
            "description": "End-to-end integration testing"
        },
        {
            "name": "Security Tests",
            "command": "python -m pytest tests/test_security.py -v",
            "description": "Security and vulnerability testing"
        },
        {
            "name": "Performance Tests (Quick)",
            "command": "python -m pytest tests/test_performance.py -v",
            "description": "Quick performance tests"
        }
    ]
    
    # Run each test category
    results = []
    for category in test_categories:
        success = run_command(category["command"], category["description"])
        results.append((category["name"], success))
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 TEST RESULTS SUMMARY")
    print(f"{'='*60}")
    
    passed = 0
    total = len(results)
    
    for name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{name:<30} {status}")
        if success:
            passed += 1
    
    print(f"\n📈 Overall Results: {passed}/{total} test categories passed")
    
    if passed == total:
        print("🎉 All test categories completed successfully!")
        print("✅ Phase 6.2 Testing Strategy Implementation: COMPLETE")
    else:
        print("⚠️  Some test categories failed. Check the output above for details.")
        print("🔧 Phase 6.2 Testing Strategy Implementation: NEEDS ATTENTION")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
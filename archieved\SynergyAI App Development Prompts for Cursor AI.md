# SynergyAI App Development Prompts for Cursor AI

## Overview

This document contains comprehensive step-by-step prompts for building the SynergyAI application using Cursor AI. The app implements an iterative AI workflow system with multiple specialized AI agents working together to help users achieve complex project goals.

## Architecture Overview

The SynergyAI app consists of:
- **Frontend**: HTML, CSS, and JavaScript interface
- **Backend**: Python-based API server
- **AI Integration**: OpenRouter API for multiple AI models
- **Core Features**: Project navigation, prompt engineering, AI execution, summarization, and iterative loops

---



# Frontend Development Prompts

## Phase 1: HTML Structure Setup

### Prompt 1.1: Create the Main HTML Structure

Create the main HTML file (`index.html`) for the SynergyAI application with the following requirements:

**Objective**: Build a comprehensive single-page application structure that supports multiple AI agents and iterative workflows.

**HTML Structure Requirements**:

1. **Document Setup**:
   - Use HTML5 doctype with proper meta tags for responsive design
   - Include viewport meta tag for mobile compatibility
   - Set up proper charset and language attributes
   - Include title "SynergyAI - Intelligent Project Workflow System"

2. **Main Layout Containers**:
   - Create a header section with navigation and branding
   - Design a main content area with flexible grid layout
   - Include a sidebar for project navigation and controls
   - Add a footer with status information and credits

3. **Core Application Sections**:
   - **Project Navigator Panel**: Container for project definition and goal management
   - **Prompt Engineering Workspace**: Area for creating and managing prompt strategies
   - **AI Execution Center**: Interface for running AI models and viewing outputs
   - **Summarization Dashboard**: Display area for condensed insights and summaries
   - **Iteration Control Panel**: Controls for managing the iterative loop process
   - **Context Management Area**: Interface for handling AI context and memory

4. **Interactive Elements**:
   - Form inputs for project goals and parameters
   - Text areas for prompt creation and editing
   - Buttons for AI execution and workflow control
   - Dropdown menus for AI model selection
   - Toggle switches for context shielding and governance options
   - Progress indicators for long-running processes

5. **Modal and Overlay Components**:
   - Modal dialogs for detailed AI responses
   - Overlay panels for context editing
   - Confirmation dialogs for critical actions
   - Help and documentation overlays

6. **Accessibility Features**:
   - Proper ARIA labels and roles
   - Keyboard navigation support
   - Screen reader compatibility
   - High contrast mode support

**Implementation Guidelines**:
- Use semantic HTML5 elements (header, nav, main, section, article, aside, footer)
- Implement proper heading hierarchy (h1-h6)
- Add data attributes for JavaScript interaction
- Include placeholder content that reflects the SynergyAI workflow
- Ensure all interactive elements have proper labels and descriptions
- Create a responsive grid system using CSS Grid or Flexbox classes

**Expected Output**: A complete HTML file with all structural elements, proper semantic markup, and placeholder content that demonstrates the SynergyAI workflow interface.

### Prompt 1.2: Create Component Templates

Develop HTML templates for reusable components within the SynergyAI application:

**Objective**: Create modular HTML templates that can be dynamically populated with content from different AI agents and workflow stages.

**Component Templates Required**:

1. **AI Agent Card Template**:
   - Agent name and description
   - Status indicator (active, idle, processing)
   - Input/output preview areas
   - Action buttons (configure, execute, view details)
   - Progress indicators for long-running tasks

2. **Project Summary Card**:
   - Project title and description
   - Goal hierarchy (short-term and long-term)
   - Milestone progress indicators
   - Last updated timestamp
   - Edit and export buttons

3. **Prompt Strategy Template**:
   - Strategy name and description
   - Query structure preview
   - Context requirements list
   - Target AI model selection
   - Execution history summary

4. **AI Response Container**:
   - Response metadata (model, timestamp, tokens used)
   - Content area with formatting support
   - Action buttons (summarize, export, iterate)
   - Tagging interface for categorization
   - Feedback and rating controls

5. **Iteration Loop Tracker**:
   - Loop counter and stage indicator
   - Timeline visualization
   - Decision points and branching paths
   - Performance metrics display
   - Loop control buttons

6. **Context Panel Template**:
   - Context source identification
   - Content preview with expand/collapse
   - Relevance scoring display
   - Include/exclude toggle controls
   - Context editing interface

**Implementation Guidelines**:
- Use template tags or data attributes for dynamic content insertion
- Implement consistent styling classes across all templates
- Add proper event handling attributes for JavaScript interaction
- Include loading states and error handling placeholders
- Ensure templates are accessible and keyboard navigable
- Create responsive layouts that work on all device sizes

**Expected Output**: A set of HTML template components that can be reused throughout the application, with proper structure for dynamic content and consistent styling hooks.

### Prompt 1.3: Create Form Interfaces

Build comprehensive form interfaces for user input and AI interaction:

**Objective**: Develop intuitive forms that capture user requirements and facilitate smooth interaction with AI agents throughout the SynergyAI workflow.

**Form Components Required**:

1. **Project Definition Form**:
   - Project name and description fields
   - Goal setting interface (short-term and long-term)
   - Scope definition with checkboxes and text areas
   - Assumptions and constraints input
   - Success criteria specification
   - Timeline and milestone planning
   - Priority and urgency settings

2. **Prompt Engineering Form**:
   - Prompt title and category selection
   - Multi-line prompt composition area with syntax highlighting
   - Context requirements specification
   - AI model selection dropdown
   - Parameter configuration (temperature, max tokens, etc.)
   - Dependency mapping interface
   - Testing and validation options

3. **AI Execution Configuration**:
   - Model selection with capability descriptions
   - Execution mode selection (internal/external)
   - Context injection controls
   - Output format preferences
   - Timeout and retry settings
   - Cost estimation display
   - Execution scheduling options

4. **Context Management Form**:
   - Context source selection
   - Content filtering and preprocessing options
   - Relevance weighting controls
   - Shielding configuration interface
   - Memory management settings
   - Context sharing permissions
   - Backup and versioning options

5. **Governance and Control Panel**:
   - Human-in-the-loop approval settings
   - Automation level configuration
   - Safety and content filtering options
   - Audit trail preferences
   - Export and sharing controls
   - Session management interface
   - Emergency stop and reset options

6. **Feedback and Rating Forms**:
   - Output quality rating scales
   - Specific feedback categories
   - Improvement suggestions
   - Bug reporting interface
   - Feature request submission
   - User experience feedback
   - Performance evaluation metrics

**Implementation Guidelines**:
- Use proper form validation with HTML5 attributes
- Implement progressive disclosure for complex forms
- Add real-time validation feedback
- Include helpful tooltips and guidance text
- Ensure forms are accessible with proper labels and ARIA attributes
- Create responsive form layouts that work on mobile devices
- Add auto-save functionality for long forms
- Implement form state management for complex workflows

**Expected Output**: A comprehensive set of form interfaces that enable users to configure and control every aspect of the SynergyAI workflow, with proper validation, accessibility, and user experience considerations.

---

## Phase 2: CSS Styling and Design

### Prompt 2.1: Create the Core Stylesheet

Develop a comprehensive CSS stylesheet that implements a modern, professional design for the SynergyAI application:

**Objective**: Create a visually appealing and functional design system that supports the complex workflow interface while maintaining usability and accessibility.

**CSS Architecture Requirements**:

1. **Design System Foundation**:
   - Define a consistent color palette with primary, secondary, and accent colors
   - Establish typography scale with proper font families and sizes
   - Create spacing and sizing variables using CSS custom properties
   - Implement a responsive breakpoint system
   - Define shadow and border radius standards
   - Set up animation and transition timing functions

2. **Layout System**:
   - Implement CSS Grid for main application layout
   - Create Flexbox utilities for component alignment
   - Design responsive sidebar and main content areas
   - Build collapsible and expandable panel systems
   - Implement sticky headers and navigation elements
   - Create fluid and fixed-width container options

3. **Component Styling**:
   - Style AI agent cards with status indicators and hover effects
   - Design form elements with consistent styling and focus states
   - Create button variations for different actions and contexts
   - Style modal dialogs and overlay components
   - Implement progress indicators and loading animations
   - Design data visualization containers and charts

4. **Interactive Elements**:
   - Create hover and focus states for all interactive elements
   - Implement smooth transitions and micro-animations
   - Design active and selected states for navigation and controls
   - Add visual feedback for user actions and system responses
   - Create loading and processing state animations
   - Implement error and success state styling

5. **Responsive Design**:
   - Ensure mobile-first responsive design approach
   - Create tablet and desktop layout variations
   - Implement touch-friendly interface elements
   - Design collapsible navigation for mobile devices
   - Ensure readable typography across all screen sizes
   - Optimize spacing and sizing for different viewports

6. **Accessibility Features**:
   - Implement high contrast mode support
   - Ensure sufficient color contrast ratios
   - Add focus indicators that meet accessibility standards
   - Create screen reader friendly styling
   - Implement reduced motion preferences
   - Design keyboard navigation visual cues

**Color Scheme Suggestions**:
- Primary: Deep blue (#1e3a8a) for trust and intelligence
- Secondary: Emerald green (#059669) for growth and iteration
- Accent: Amber (#f59e0b) for highlights and calls-to-action
- Neutral: Gray scale (#f8fafc to #1e293b) for backgrounds and text
- Status colors: Red for errors, green for success, yellow for warnings

**Typography Recommendations**:
- Headings: Inter or Poppins for modern, clean appearance
- Body text: System fonts or Open Sans for readability
- Code/technical: JetBrains Mono or Fira Code for monospace needs
- Font sizes: 12px to 48px scale with proper line heights

**Implementation Guidelines**:
- Use CSS custom properties for theming and consistency
- Implement BEM methodology for class naming
- Create utility classes for common styling patterns
- Ensure cross-browser compatibility
- Optimize for performance with efficient selectors
- Include print styles for documentation export
- Add dark mode support with CSS custom properties

**Expected Output**: A comprehensive CSS file that provides a complete design system for the SynergyAI application, with modern styling, responsive design, and accessibility features.

### Prompt 2.2: Create Advanced UI Components

Develop sophisticated CSS components for complex interface elements:

**Objective**: Build advanced UI components that support the intricate workflow visualization and interaction patterns required by the SynergyAI system.

**Advanced Component Requirements**:

1. **Workflow Visualization Components**:
   - Timeline visualization with branching paths
   - Process flow diagrams with interactive nodes
   - Progress tracking with multiple stages
   - Dependency mapping with connecting lines
   - Status dashboards with real-time updates
   - Iteration loop visualization with cycle indicators

2. **AI Agent Interface Components**:
   - Agent status cards with animated indicators
   - Communication flow visualization between agents
   - Context sharing visual representations
   - Performance metrics displays with charts
   - Agent configuration panels with tabbed interfaces
   - Collaborative workspace indicators

3. **Data Visualization Elements**:
   - Chart containers with responsive sizing
   - Data table styling with sorting and filtering
   - Metric cards with trend indicators
   - Comparison views with side-by-side layouts
   - Export and sharing interface elements
   - Interactive legend and control components

4. **Advanced Form Components**:
   - Multi-step form wizards with progress indicators
   - Conditional field display based on selections
   - Rich text editing interface styling
   - File upload areas with drag-and-drop support
   - Tag input systems with autocomplete
   - Slider and range input custom styling

5. **Modal and Overlay Systems**:
   - Layered modal system with proper z-index management
   - Slide-out panels and drawers
   - Tooltip and popover positioning
   - Full-screen overlay modes
   - Confirmation dialog variations
   - Context menu styling and positioning

6. **Animation and Transition Systems**:
   - Page transition animations
   - Component state change animations
   - Loading and processing animations
   - Hover and interaction micro-animations
   - Data update transition effects
   - Error and success state animations

**Implementation Guidelines**:
- Use CSS Grid and Flexbox for complex layouts
- Implement CSS animations with proper performance considerations
- Create reusable component classes with modifier patterns
- Ensure components work across different screen sizes
- Add proper focus management for keyboard navigation
- Implement smooth transitions with appropriate timing
- Use CSS transforms for performance-optimized animations
- Create fallbacks for older browsers

**Expected Output**: Advanced CSS components that enable sophisticated user interactions and visualizations, with smooth animations and responsive behavior across all devices.

### Prompt 2.3: Implement Responsive Design System

Create a comprehensive responsive design system that ensures optimal user experience across all devices:

**Objective**: Develop a mobile-first responsive design that adapts the complex SynergyAI interface for smartphones, tablets, and desktop computers while maintaining full functionality.

**Responsive Design Requirements**:

1. **Breakpoint Strategy**:
   - Mobile: 320px - 767px (primary focus)
   - Tablet: 768px - 1023px (secondary adaptation)
   - Desktop: 1024px+ (enhanced experience)
   - Large screens: 1440px+ (optimized layouts)
   - Ultra-wide: 1920px+ (advanced features)

2. **Mobile-First Adaptations**:
   - Collapsible navigation with hamburger menu
   - Stacked layout for AI agent cards
   - Touch-optimized button sizes (minimum 44px)
   - Simplified form layouts with single-column design
   - Swipe gestures for panel navigation
   - Bottom sheet modals for mobile interactions

3. **Tablet Optimizations**:
   - Two-column layouts for better space utilization
   - Sidebar navigation with toggle functionality
   - Optimized form layouts with logical grouping
   - Touch and mouse interaction support
   - Landscape and portrait orientation handling
   - Split-screen friendly interface design

4. **Desktop Enhancements**:
   - Multi-column layouts with advanced grid systems
   - Persistent sidebar navigation
   - Hover states and advanced interactions
   - Keyboard shortcuts and accessibility
   - Multi-window and tab support considerations
   - Advanced data visualization capabilities

5. **Content Adaptation Strategies**:
   - Progressive disclosure for complex information
   - Contextual navigation based on screen size
   - Adaptive typography with fluid scaling
   - Flexible image and media handling
   - Responsive data tables with horizontal scrolling
   - Collapsible sections for information hierarchy

6. **Performance Considerations**:
   - Efficient CSS media queries
   - Optimized image loading for different screen densities
   - Reduced animations on lower-powered devices
   - Efficient layout calculations
   - Minimal reflows and repaints
   - Progressive enhancement approach

**Implementation Guidelines**:
- Use CSS Grid and Flexbox for flexible layouts
- Implement container queries where supported
- Create fluid typography with clamp() functions
- Use relative units (rem, em, %) for scalability
- Implement efficient media query organization
- Test across multiple devices and browsers
- Ensure touch targets meet accessibility guidelines
- Optimize for both portrait and landscape orientations

**Expected Output**: A complete responsive design system that provides an optimal user experience across all device types, with efficient CSS implementation and performance optimization.

---


## Phase 3: JavaScript Functionality

### Prompt 3.1: Core Application Architecture

Develop the main JavaScript architecture for the SynergyAI application:

**Objective**: Create a robust, modular JavaScript architecture that manages the complex interactions between multiple AI agents, handles asynchronous operations, and provides a smooth user experience.

**Architecture Requirements**:

1. **Application Structure**:
   - Implement a modular architecture with clear separation of concerns
   - Create a main application controller that orchestrates all components
   - Design event-driven communication between modules
   - Implement state management for application-wide data
   - Create utility functions for common operations
   - Establish error handling and logging systems

2. **Core Modules**:
   - **ProjectManager**: Handles project creation, updates, and persistence
   - **PromptEngine**: Manages prompt creation, editing, and strategy development
   - **AIOrchestrator**: Coordinates AI model interactions and execution
   - **ContextManager**: Handles context injection, shielding, and memory management
   - **SummarizerService**: Processes AI outputs and creates condensed summaries
   - **IterationController**: Manages the iterative workflow loop
   - **UIController**: Handles user interface updates and interactions

3. **State Management System**:
   - Implement centralized state store for application data
   - Create reactive state updates with observer pattern
   - Handle state persistence to localStorage/sessionStorage
   - Implement state synchronization across browser tabs
   - Create state validation and error recovery mechanisms
   - Design state history and undo/redo functionality

4. **Event System**:
   - Create custom event system for inter-module communication
   - Implement event listeners for user interactions
   - Handle asynchronous event processing
   - Create event logging for debugging and analytics
   - Implement event throttling and debouncing
   - Design error event handling and recovery

5. **API Communication Layer**:
   - Create HTTP client for backend API communication
   - Implement request/response interceptors
   - Handle authentication and authorization
   - Create retry logic for failed requests
   - Implement request caching and optimization
   - Design real-time communication with WebSockets

6. **Utility Functions**:
   - Date and time formatting utilities
   - String manipulation and validation functions
   - Data transformation and serialization helpers
   - DOM manipulation utilities
   - Performance monitoring functions
   - Browser compatibility helpers

**Implementation Guidelines**:
- Use ES6+ features including modules, classes, and async/await
- Implement proper error handling with try-catch blocks
- Create comprehensive logging for debugging and monitoring
- Use TypeScript-style JSDoc comments for better code documentation
- Implement unit testing structure with test hooks
- Follow consistent naming conventions and code style
- Optimize for performance with efficient algorithms and data structures

**Expected Output**: A complete JavaScript architecture with modular design, state management, event handling, and API communication capabilities that serves as the foundation for the SynergyAI application.

### Prompt 3.2: AI Agent Management System

Create a comprehensive system for managing multiple AI agents and their interactions:

**Objective**: Develop JavaScript functionality that handles the complex orchestration of different AI agents (Project Navigator, Prompt Engineer, Summarizer, etc.) and manages their collaborative workflow.

**AI Agent System Requirements**:

1. **Agent Base Class**:
   - Create a base Agent class with common properties and methods
   - Implement agent lifecycle management (initialize, activate, deactivate, destroy)
   - Define standard communication interfaces between agents
   - Create agent status tracking and reporting
   - Implement agent configuration and customization
   - Design agent performance monitoring and metrics

2. **Specific Agent Implementations**:
   - **ProjectNavigatorAgent**: Handles project definition, goal setting, and plan updates
   - **PromptEngineerAgent**: Manages prompt creation, strategy development, and context curation
   - **ExecutorAgent**: Handles AI model execution and response processing
   - **SummarizerAgent**: Processes outputs and creates condensed summaries
   - **IterationAgent**: Manages workflow loops and decision points
   - **GovernanceAgent**: Handles human-in-the-loop controls and safety measures

3. **Agent Communication System**:
   - Implement message passing between agents
   - Create shared memory and context systems
   - Handle agent collaboration and coordination
   - Implement agent priority and scheduling systems
   - Create conflict resolution mechanisms
   - Design agent handoff and delegation processes

4. **AI Model Integration**:
   - Create OpenRouter API integration for external AI models
   - Implement model selection and configuration
   - Handle API authentication and rate limiting
   - Create request formatting and response parsing
   - Implement streaming responses for real-time updates
   - Design fallback and error handling for API failures

5. **Context Management**:
   - Implement context injection and extraction
   - Create context shielding and privacy controls
   - Handle context summarization and compression
   - Implement context versioning and history
   - Create context sharing and permissions
   - Design context optimization for different AI models

6. **Workflow Orchestration**:
   - Create workflow state machines for complex processes
   - Implement conditional logic and branching
   - Handle parallel and sequential agent execution
   - Create workflow monitoring and visualization
   - Implement workflow pause, resume, and cancellation
   - Design workflow templates and reusability

**Implementation Guidelines**:
- Use async/await for all AI API interactions
- Implement proper error handling and retry logic
- Create comprehensive logging for agent activities
- Use event-driven architecture for agent communication
- Implement rate limiting and quota management
- Design for scalability and performance optimization
- Create unit tests for all agent functionality

**Expected Output**: A complete AI agent management system that can orchestrate multiple specialized agents, handle complex workflows, and integrate with external AI models through the OpenRouter API.

### Prompt 3.3: User Interface Interactions

Develop comprehensive JavaScript functionality for user interface interactions and real-time updates:

**Objective**: Create smooth, responsive user interface interactions that provide immediate feedback and handle complex user workflows with multiple AI agents and iterative processes.

**UI Interaction Requirements**:

1. **Form Handling and Validation**:
   - Implement real-time form validation with custom rules
   - Create dynamic form generation based on agent requirements
   - Handle multi-step form wizards with progress tracking
   - Implement auto-save functionality for long forms
   - Create form state management and recovery
   - Design conditional field display and validation

2. **Dynamic Content Management**:
   - Implement dynamic component rendering and updates
   - Create real-time content updates from AI responses
   - Handle large content rendering with virtual scrolling
   - Implement content filtering and search functionality
   - Create content export and sharing features
   - Design content versioning and history tracking

3. **Interactive Visualizations**:
   - Create workflow timeline with interactive elements
   - Implement agent status visualization with real-time updates
   - Design project progress tracking with visual indicators
   - Create context relationship mapping with interactive nodes
   - Implement performance metrics dashboards
   - Design iteration loop visualization with branching paths

4. **Modal and Panel Management**:
   - Create modal dialog system with stacking and focus management
   - Implement slide-out panels and drawers
   - Handle overlay positioning and responsive behavior
   - Create context menus and dropdown interactions
   - Implement tooltip and popover systems
   - Design full-screen modes and split-screen layouts

5. **Real-time Updates and Notifications**:
   - Implement WebSocket connections for real-time updates
   - Create notification system for agent activities
   - Handle progress updates for long-running processes
   - Implement status indicators and activity feeds
   - Create alert and confirmation systems
   - Design system health monitoring and alerts

6. **Keyboard and Accessibility Support**:
   - Implement comprehensive keyboard navigation
   - Create screen reader support with ARIA attributes
   - Handle focus management for complex interactions
   - Implement keyboard shortcuts for power users
   - Create high contrast and reduced motion support
   - Design voice control integration points

7. **Mobile and Touch Interactions**:
   - Implement touch gestures for mobile devices
   - Create swipe navigation for panels and content
   - Handle pinch-to-zoom for visualizations
   - Implement pull-to-refresh functionality
   - Create touch-optimized drag and drop
   - Design haptic feedback integration

8. **Performance Optimization**:
   - Implement virtual scrolling for large datasets
   - Create lazy loading for content and images
   - Handle debouncing and throttling for user inputs
   - Implement efficient DOM manipulation
   - Create memory management for long-running sessions
   - Design caching strategies for improved performance

**Implementation Guidelines**:
- Use modern DOM APIs and avoid jQuery dependencies
- Implement proper event delegation for dynamic content
- Create reusable interaction patterns and components
- Use CSS transitions and animations for smooth interactions
- Implement proper error boundaries and fallback states
- Create comprehensive accessibility testing hooks
- Design for offline functionality where appropriate
- Optimize for both desktop and mobile performance

**Expected Output**: A complete user interface interaction system that provides smooth, accessible, and responsive interactions for all aspects of the SynergyAI workflow, with real-time updates and comprehensive user feedback.

### Prompt 3.4: Data Management and Persistence

Create a robust data management system for handling project data, AI responses, and user preferences:

**Objective**: Develop a comprehensive data management system that handles complex project data, AI interactions, user preferences, and provides reliable persistence across browser sessions.

**Data Management Requirements**:

1. **Data Models and Schemas**:
   - Create comprehensive data models for projects, prompts, and AI responses
   - Implement data validation and sanitization
   - Design schema versioning for data migration
   - Create data relationship mapping and integrity checks
   - Implement data serialization and deserialization
   - Design data compression for large datasets

2. **Local Storage Management**:
   - Implement localStorage and sessionStorage strategies
   - Create data encryption for sensitive information
   - Handle storage quota management and cleanup
   - Implement data backup and recovery systems
   - Create cross-tab data synchronization
   - Design data export and import functionality

3. **Cache Management**:
   - Implement intelligent caching for AI responses
   - Create cache invalidation strategies
   - Handle cache size limits and cleanup
   - Implement cache warming for frequently accessed data
   - Create cache performance monitoring
   - Design cache sharing across application instances

4. **Real-time Data Synchronization**:
   - Implement WebSocket-based data synchronization
   - Create conflict resolution for concurrent edits
   - Handle offline data management and sync
   - Implement optimistic updates with rollback
   - Create data change tracking and history
   - Design collaborative editing support

5. **Data Analytics and Metrics**:
   - Implement user interaction tracking
   - Create performance metrics collection
   - Handle AI usage analytics and cost tracking
   - Implement error tracking and reporting
   - Create user behavior analysis
   - Design privacy-compliant analytics

6. **Data Security and Privacy**:
   - Implement client-side data encryption
   - Create secure data transmission protocols
   - Handle sensitive data masking and redaction
   - Implement data retention policies
   - Create audit trails for data access
   - Design GDPR compliance features

**Implementation Guidelines**:
- Use IndexedDB for complex data storage needs
- Implement proper error handling for storage operations
- Create data migration scripts for schema changes
- Use Web Workers for heavy data processing
- Implement proper data validation and sanitization
- Create comprehensive backup and recovery procedures
- Design for scalability and performance optimization

**Expected Output**: A complete data management system that provides reliable persistence, efficient caching, real-time synchronization, and secure handling of all application data.

---


# Backend Development Prompts

## Phase 4: Python Backend Architecture

### Prompt 4.1: Flask Application Setup and Structure

Create a comprehensive Flask backend application for the SynergyAI system:

**Objective**: Develop a robust, scalable Flask backend that handles AI agent orchestration, data management, and API services for the SynergyAI workflow system.

**Flask Application Requirements**:

1. **Project Structure Setup**:
   - Create a well-organized Flask application structure with blueprints
   - Implement configuration management for different environments (development, production)
   - Set up proper logging and error handling systems
   - Create database models and migration scripts
   - Implement authentication and authorization systems
   - Design API versioning and documentation structure

2. **Core Application Components**:
   - **app.py**: Main Flask application factory and configuration
   - **config.py**: Environment-specific configuration settings
   - **models/**: Database models for projects, prompts, AI responses, and users
   - **api/**: RESTful API endpoints organized by functionality
   - **services/**: Business logic and AI integration services
   - **utils/**: Utility functions and helper classes
   - **middleware/**: Custom middleware for authentication, logging, and CORS

3. **Database Design**:
   - Design comprehensive database schema for SynergyAI workflow data
   - Create models for Projects, Prompts, AI Agents, Responses, and Iterations
   - Implement relationships between entities with proper foreign keys
   - Create indexes for performance optimization
   - Design data versioning and audit trail systems
   - Implement soft delete functionality for data recovery

4. **API Architecture**:
   - Create RESTful API endpoints following OpenAPI/Swagger specifications
   - Implement proper HTTP status codes and error responses
   - Design consistent request/response formats with JSON schemas
   - Create API rate limiting and throttling mechanisms
   - Implement API authentication with JWT tokens
   - Design API versioning strategy for backward compatibility

5. **Security Implementation**:
   - Implement CORS configuration for frontend-backend communication
   - Create input validation and sanitization for all endpoints
   - Implement SQL injection and XSS protection
   - Design secure session management and token handling
   - Create audit logging for security events
   - Implement data encryption for sensitive information

6. **Performance Optimization**:
   - Implement database connection pooling
   - Create caching strategies with Redis or in-memory caching
   - Design asynchronous task processing with Celery
   - Implement database query optimization
   - Create response compression and optimization
   - Design load balancing and scaling considerations

**Implementation Guidelines**:
- Use Flask-SQLAlchemy for database ORM
- Implement Flask-Migrate for database migrations
- Use Flask-JWT-Extended for authentication
- Implement Flask-CORS for cross-origin requests
- Create comprehensive error handling with custom exceptions
- Use environment variables for sensitive configuration
- Implement proper logging with structured log formats
- Create unit and integration tests for all components

**Expected Output**: A complete Flask application structure with proper organization, security, performance optimization, and scalability considerations that serves as the foundation for the SynergyAI backend.

### Prompt 4.2: Database Models and Data Management

Create comprehensive database models and data management systems:

**Objective**: Design and implement robust database models that capture the complex relationships and data requirements of the SynergyAI workflow system.

**Database Model Requirements**:

1. **Core Entity Models**:
   - **User Model**: User authentication, preferences, and profile information
   - **Project Model**: Project definition, goals, scope, and metadata
   - **Prompt Model**: Prompt strategies, content, and configuration
   - **AIAgent Model**: Agent definitions, capabilities, and status
   - **Response Model**: AI responses, metadata, and processing results
   - **Iteration Model**: Workflow iterations, decisions, and branching paths
   - **Context Model**: Context data, sources, and relevance scoring

2. **Relationship Design**:
   - One-to-many relationships between Users and Projects
   - Many-to-many relationships between Projects and AI Agents
   - Complex relationships between Prompts, Responses, and Iterations
   - Hierarchical relationships for nested contexts and summaries
   - Temporal relationships for workflow history and versioning
   - Dependency relationships between prompts and responses

3. **Data Validation and Constraints**:
   - Implement comprehensive field validation with custom validators
   - Create database constraints for data integrity
   - Design enum fields for status and type classifications
   - Implement unique constraints and composite keys
   - Create check constraints for business rules
   - Design cascading delete and update rules

4. **Advanced Features**:
   - Implement soft delete functionality with deleted_at timestamps
   - Create audit trail tracking with created_at and updated_at fields
   - Design data versioning for tracking changes over time
   - Implement full-text search capabilities for content
   - Create data archiving and cleanup mechanisms
   - Design data export and import functionality

5. **Performance Optimization**:
   - Create database indexes for frequently queried fields
   - Implement composite indexes for complex queries
   - Design partitioning strategies for large datasets
   - Create materialized views for complex aggregations
   - Implement query optimization and analysis
   - Design caching strategies for frequently accessed data

6. **Data Migration and Seeding**:
   - Create database migration scripts for schema changes
   - Implement data seeding for development and testing
   - Design rollback strategies for failed migrations
   - Create data transformation scripts for legacy data
   - Implement database backup and recovery procedures
   - Design data consistency checks and validation

**Model Specifications**:

```python
# Example User Model Structure
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    preferences = db.Column(db.JSON)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    
    # Relationships
    projects = db.relationship('Project', backref='owner', lazy='dynamic')
    
# Example Project Model Structure
class Project(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    goals = db.Column(db.JSON)  # Short-term and long-term goals
    scope = db.Column(db.JSON)  # Assumptions, constraints, success criteria
    status = db.Column(db.Enum('active', 'paused', 'completed', 'archived'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Foreign Keys
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    
    # Relationships
    prompts = db.relationship('Prompt', backref='project', lazy='dynamic')
    iterations = db.relationship('Iteration', backref='project', lazy='dynamic')
```

**Implementation Guidelines**:
- Use SQLAlchemy ORM with proper relationship definitions
- Implement model validation with custom validator functions
- Create model serialization methods for API responses
- Use database migrations for all schema changes
- Implement proper indexing strategies for performance
- Create model factories for testing and development
- Design models with future extensibility in mind

**Expected Output**: A complete set of database models with proper relationships, validation, and performance optimization that captures all aspects of the SynergyAI workflow system.

### Prompt 4.3: API Endpoints and Services

Develop comprehensive API endpoints and business logic services:

**Objective**: Create a complete set of RESTful API endpoints and supporting services that enable all SynergyAI workflow operations with proper error handling, validation, and performance optimization.

**API Endpoint Requirements**:

1. **Project Management APIs**:
   - **POST /api/v1/projects**: Create new project with goals and scope
   - **GET /api/v1/projects**: List user projects with filtering and pagination
   - **GET /api/v1/projects/{id}**: Get detailed project information
   - **PUT /api/v1/projects/{id}**: Update project goals, scope, or status
   - **DELETE /api/v1/projects/{id}**: Archive or delete project
   - **POST /api/v1/projects/{id}/duplicate**: Create project copy
   - **GET /api/v1/projects/{id}/analytics**: Get project performance metrics

2. **Prompt Engineering APIs**:
   - **POST /api/v1/prompts**: Create new prompt strategy
   - **GET /api/v1/prompts**: List prompts with filtering by project/type
   - **GET /api/v1/prompts/{id}**: Get detailed prompt information
   - **PUT /api/v1/prompts/{id}**: Update prompt content or configuration
   - **DELETE /api/v1/prompts/{id}**: Remove prompt strategy
   - **POST /api/v1/prompts/{id}/test**: Test prompt with sample data
   - **POST /api/v1/prompts/{id}/optimize**: Optimize prompt for better results

3. **AI Agent Management APIs**:
   - **GET /api/v1/agents**: List available AI agents and capabilities
   - **POST /api/v1/agents/{type}/execute**: Execute specific agent with parameters
   - **GET /api/v1/agents/{id}/status**: Get agent execution status
   - **POST /api/v1/agents/{id}/cancel**: Cancel running agent execution
   - **GET /api/v1/agents/{id}/history**: Get agent execution history
   - **POST /api/v1/agents/orchestrate**: Execute multi-agent workflow
   - **GET /api/v1/agents/performance**: Get agent performance metrics

4. **AI Response Management APIs**:
   - **GET /api/v1/responses**: List AI responses with filtering
   - **GET /api/v1/responses/{id}**: Get detailed response information
   - **POST /api/v1/responses/{id}/summarize**: Create response summary
   - **POST /api/v1/responses/{id}/rate**: Rate response quality
   - **POST /api/v1/responses/{id}/export**: Export response in various formats
   - **POST /api/v1/responses/compare**: Compare multiple responses
   - **GET /api/v1/responses/analytics**: Get response analytics and trends

5. **Iteration and Workflow APIs**:
   - **POST /api/v1/iterations**: Start new workflow iteration
   - **GET /api/v1/iterations/{id}**: Get iteration status and results
   - **POST /api/v1/iterations/{id}/continue**: Continue iteration with new parameters
   - **POST /api/v1/iterations/{id}/branch**: Create iteration branch
   - **GET /api/v1/iterations/{id}/timeline**: Get iteration timeline
   - **POST /api/v1/workflows/automate**: Set up automated workflow
   - **GET /api/v1/workflows/templates**: Get workflow templates

6. **Context Management APIs**:
   - **POST /api/v1/context**: Create or update context data
   - **GET /api/v1/context/{id}**: Get context information
   - **POST /api/v1/context/{id}/shield**: Configure context shielding
   - **POST /api/v1/context/inject**: Inject context into AI execution
   - **GET /api/v1/context/relevance**: Calculate context relevance scores
   - **POST /api/v1/context/compress**: Compress context for efficiency
   - **GET /api/v1/context/history**: Get context usage history

**Service Layer Requirements**:

1. **Project Service**:
   - Implement project lifecycle management
   - Create goal tracking and milestone management
   - Handle project analytics and reporting
   - Implement project templates and duplication
   - Create project collaboration features
   - Design project archiving and cleanup

2. **AI Orchestration Service**:
   - Implement multi-agent coordination logic
   - Create agent scheduling and priority management
   - Handle agent communication and data sharing
   - Implement workflow state management
   - Create agent performance monitoring
   - Design agent failure recovery mechanisms

3. **Context Management Service**:
   - Implement context injection and extraction
   - Create context relevance scoring algorithms
   - Handle context compression and optimization
   - Implement context shielding and privacy controls
   - Create context versioning and history
   - Design context sharing and permissions

4. **Response Processing Service**:
   - Implement response parsing and validation
   - Create summarization and insight extraction
   - Handle response formatting and export
   - Implement response quality assessment
   - Create response comparison and analysis
   - Design response caching and optimization

5. **Analytics and Monitoring Service**:
   - Implement usage tracking and metrics collection
   - Create performance monitoring and alerting
   - Handle cost tracking and optimization
   - Implement user behavior analysis
   - Create system health monitoring
   - Design reporting and dashboard data

**Implementation Guidelines**:
- Use Flask-RESTful for API endpoint organization
- Implement proper HTTP status codes and error responses
- Create comprehensive input validation with marshmallow
- Use async processing for long-running operations
- Implement proper logging and monitoring
- Create API documentation with Flask-RESTX
- Design for horizontal scaling and load balancing
- Implement proper caching strategies

**Expected Output**: A complete set of API endpoints and services that provide all functionality required for the SynergyAI workflow system, with proper error handling, validation, and performance optimization.

### Prompt 4.4: Authentication and Security

Implement comprehensive authentication and security systems:

**Objective**: Create robust security measures that protect user data, secure API endpoints, and ensure safe AI interactions while maintaining usability and performance.

**Security Implementation Requirements**:

1. **Authentication System**:
   - Implement JWT-based authentication with refresh tokens
   - Create user registration and login endpoints
   - Design password hashing with bcrypt or Argon2
   - Implement multi-factor authentication (MFA) support
   - Create social login integration (Google, GitHub, etc.)
   - Design session management and token lifecycle
   - Implement account verification and password reset

2. **Authorization and Access Control**:
   - Create role-based access control (RBAC) system
   - Implement resource-level permissions
   - Design API endpoint authorization decorators
   - Create user group and team management
   - Implement project-level access controls
   - Design fine-grained permission systems
   - Create audit logging for access events

3. **API Security**:
   - Implement rate limiting and throttling
   - Create API key management for external integrations
   - Design request validation and sanitization
   - Implement CORS configuration for frontend access
   - Create API versioning and deprecation strategies
   - Design secure error handling without information leakage
   - Implement request/response encryption for sensitive data

4. **Data Protection**:
   - Implement encryption at rest for sensitive data
   - Create secure data transmission with HTTPS
   - Design data anonymization and pseudonymization
   - Implement secure data deletion and cleanup
   - Create data backup encryption and security
   - Design compliance with GDPR and privacy regulations
   - Implement data loss prevention measures

5. **AI Security and Safety**:
   - Create content filtering and safety checks
   - Implement prompt injection protection
   - Design AI response validation and sanitization
   - Create usage monitoring and anomaly detection
   - Implement cost controls and budget limits
   - Design AI model access controls and permissions
   - Create audit trails for AI interactions

6. **Infrastructure Security**:
   - Implement secure configuration management
   - Create environment variable protection
   - Design secure deployment practices
   - Implement monitoring and alerting systems
   - Create incident response procedures
   - Design backup and disaster recovery plans
   - Implement security scanning and vulnerability assessment

**Security Service Components**:

```python
# Example Authentication Service
class AuthService:
    @staticmethod
    def authenticate_user(username, password):
        """Authenticate user with username and password"""
        user = User.query.filter_by(username=username).first()
        if user and user.check_password(password):
            return user
        return None
    
    @staticmethod
    def generate_tokens(user):
        """Generate access and refresh tokens"""
        access_token = create_access_token(identity=user.id)
        refresh_token = create_refresh_token(identity=user.id)
        return access_token, refresh_token
    
    @staticmethod
    def validate_token(token):
        """Validate JWT token and return user"""
        try:
            decoded_token = decode_token(token)
            user_id = decoded_token['sub']
            return User.query.get(user_id)
        except:
            return None

# Example Authorization Decorator
def require_permission(permission):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            current_user = get_current_user()
            if not current_user.has_permission(permission):
                return jsonify({'error': 'Insufficient permissions'}), 403
            return f(*args, **kwargs)
        return decorated_function
    return decorator
```

**Implementation Guidelines**:
- Use Flask-JWT-Extended for token management
- Implement proper password hashing with werkzeug.security
- Create comprehensive input validation and sanitization
- Use environment variables for sensitive configuration
- Implement proper error handling without information disclosure
- Create security middleware for common protections
- Design for security by default with minimal privileges
- Implement comprehensive security testing

**Expected Output**: A complete security system that provides robust authentication, authorization, data protection, and AI safety measures while maintaining usability and performance for the SynergyAI application.

---


# AI Model Integration Prompts

## Phase 5: OpenRouter API Integration and AI Agent System

### Prompt 5.1: OpenRouter API Integration Setup

Create comprehensive integration with OpenRouter API for accessing multiple AI models:

**Objective**: Develop a robust integration system that connects the SynergyAI application with OpenRouter's API, enabling access to multiple AI models with proper error handling, rate limiting, and cost management.

**OpenRouter Integration Requirements**:

1. **API Client Configuration**:
   - Create a dedicated OpenRouter API client class with proper initialization
   - Implement API key management and authentication handling
   - Design configuration for different AI models and their capabilities
   - Create model selection logic based on task requirements
   - Implement API endpoint management and versioning
   - Design fallback mechanisms for API failures

2. **Model Management System**:
   - Create a comprehensive model registry with capabilities and pricing
   - Implement model selection algorithms based on task complexity
   - Design model performance tracking and optimization
   - Create model availability monitoring and health checks
   - Implement model cost estimation and budget controls
   - Design model comparison and benchmarking systems

3. **Request Processing**:
   - Implement request formatting for different AI models
   - Create prompt optimization for specific model requirements
   - Design context window management for large inputs
   - Implement streaming response handling for real-time updates
   - Create request queuing and batch processing
   - Design request retry logic with exponential backoff

4. **Response Handling**:
   - Create comprehensive response parsing and validation
   - Implement response formatting and standardization
   - Design error handling for different types of API failures
   - Create response caching and optimization strategies
   - Implement response quality assessment and filtering
   - Design response post-processing and enhancement

5. **Rate Limiting and Cost Management**:
   - Implement intelligent rate limiting based on API quotas
   - Create cost tracking and budget management systems
   - Design usage optimization and efficiency monitoring
   - Implement priority queuing for different request types
   - Create cost prediction and alerting mechanisms
   - Design usage analytics and reporting

6. **Security and Privacy**:
   - Implement secure API key storage and rotation
   - Create data privacy controls for sensitive information
   - Design request/response encryption for additional security
   - Implement audit logging for all API interactions
   - Create compliance features for data protection regulations
   - Design secure error handling without data leakage

**OpenRouter Client Implementation**:

```python
import requests
import json
import time
from typing import Dict, List, Optional, Generator
from dataclasses import dataclass
from enum import Enum

class ModelType(Enum):
    GPT_4 = "openai/gpt-4"
    GPT_3_5_TURBO = "openai/gpt-3.5-turbo"
    CLAUDE_3 = "anthropic/claude-3-opus"
    LLAMA_2 = "meta-llama/llama-2-70b-chat"
    MISTRAL = "mistralai/mistral-7b-instruct"

@dataclass
class ModelCapabilities:
    max_tokens: int
    supports_streaming: bool
    cost_per_token: float
    specialties: List[str]
    context_window: int

class OpenRouterClient:
    def __init__(self, api_key: str, base_url: str = "https://openrouter.ai/api/v1"):
        self.api_key = api_key
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://synergyai.app",
            "X-Title": "SynergyAI"
        })
        self.model_registry = self._initialize_model_registry()
    
    def _initialize_model_registry(self) -> Dict[ModelType, ModelCapabilities]:
        """Initialize model registry with capabilities and pricing"""
        return {
            ModelType.GPT_4: ModelCapabilities(
                max_tokens=8192,
                supports_streaming=True,
                cost_per_token=0.00003,
                specialties=["reasoning", "analysis", "complex_tasks"],
                context_window=32768
            ),
            ModelType.CLAUDE_3: ModelCapabilities(
                max_tokens=4096,
                supports_streaming=True,
                cost_per_token=0.000015,
                specialties=["writing", "analysis", "safety"],
                context_window=200000
            ),
            # Add more models...
        }
    
    async def generate_completion(
        self,
        prompt: str,
        model: ModelType,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        stream: bool = False,
        **kwargs
    ) -> Dict:
        """Generate AI completion with specified parameters"""
        
        payload = {
            "model": model.value,
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": stream,
            **kwargs
        }
        
        try:
            if stream:
                return self._handle_streaming_response(payload)
            else:
                return self._handle_standard_response(payload)
        except Exception as e:
            return self._handle_api_error(e)
    
    def _handle_streaming_response(self, payload: Dict) -> Generator:
        """Handle streaming response from OpenRouter API"""
        response = self.session.post(
            f"{self.base_url}/chat/completions",
            json=payload,
            stream=True
        )
        
        for line in response.iter_lines():
            if line:
                try:
                    data = json.loads(line.decode('utf-8').replace('data: ', ''))
                    if data.get('choices'):
                        yield data['choices'][0]['delta'].get('content', '')
                except json.JSONDecodeError:
                    continue
    
    def _handle_standard_response(self, payload: Dict) -> Dict:
        """Handle standard response from OpenRouter API"""
        response = self.session.post(
            f"{self.base_url}/chat/completions",
            json=payload
        )
        response.raise_for_status()
        return response.json()
    
    def select_optimal_model(self, task_type: str, complexity: str, budget: float) -> ModelType:
        """Select optimal model based on task requirements"""
        # Implement model selection logic
        pass
    
    def estimate_cost(self, prompt: str, model: ModelType, max_tokens: int) -> float:
        """Estimate cost for API request"""
        # Implement cost estimation logic
        pass
```

**Implementation Guidelines**:
- Use async/await for all API interactions to prevent blocking
- Implement comprehensive error handling with specific exception types
- Create detailed logging for debugging and monitoring
- Use environment variables for API keys and sensitive configuration
- Implement proper timeout handling for long-running requests
- Create unit tests for all API interaction methods
- Design for horizontal scaling with connection pooling

**Expected Output**: A complete OpenRouter API integration system that provides reliable access to multiple AI models with proper error handling, cost management, and performance optimization.

### Prompt 5.2: Multi-Agent AI System Implementation

Develop the core multi-agent system that orchestrates different AI agents according to the SynergyAI workflow:

**Objective**: Create a sophisticated multi-agent system that implements the SynergyAI workflow with specialized agents for project navigation, prompt engineering, execution, summarization, and iteration management.

**Multi-Agent System Requirements**:

1. **Agent Base Architecture**:
   - Create a base Agent class with common functionality and interfaces
   - Implement agent lifecycle management (initialization, execution, cleanup)
   - Design agent communication protocols and message passing
   - Create agent state management and persistence
   - Implement agent performance monitoring and metrics
   - Design agent error handling and recovery mechanisms

2. **Specialized Agent Implementations**:

   **Project Navigator AI Agent**:
   - Implement project definition and goal clarification logic
   - Create project summary generation and updating
   - Design milestone tracking and progress assessment
   - Implement scope analysis and constraint identification
   - Create success criteria evaluation and refinement
   - Design project pivot and adaptation recommendations

   **Prompt/Context Engineering AI Agent**:
   - Implement prompt strategy development and optimization
   - Create context curation and relevance assessment
   - Design prompt structure analysis and improvement
   - Implement dependency mapping and context injection
   - Create prompt testing and validation systems
   - Design context compression and summarization

   **Advanced AI Execution Agent**:
   - Implement AI model selection and configuration
   - Create execution orchestration and monitoring
   - Design response processing and validation
   - Implement context-aware execution with memory
   - Create execution optimization and performance tuning
   - Design fallback and error recovery mechanisms

   **Summarizer AI Agent**:
   - Implement intelligent summarization algorithms
   - Create insight extraction and theme identification
   - Design condensed summary generation with key points
   - Implement tagging and categorization systems
   - Create summary quality assessment and refinement
   - Design summary versioning and history tracking

   **Iteration Controller Agent**:
   - Implement workflow loop management and control
   - Create decision point analysis and branching logic
   - Design iteration optimization and efficiency improvement
   - Implement convergence detection and completion criteria
   - Create iteration history tracking and analysis
   - Design adaptive iteration strategies

3. **Agent Communication System**:
   - Create message bus for inter-agent communication
   - Implement event-driven agent coordination
   - Design shared memory and context systems
   - Create agent synchronization and coordination protocols
   - Implement conflict resolution and priority management
   - Design agent collaboration and handoff mechanisms

4. **Workflow Orchestration**:
   - Implement workflow state machines for complex processes
   - Create conditional logic and branching strategies
   - Design parallel and sequential agent execution
   - Implement workflow monitoring and visualization
   - Create workflow optimization and performance tuning
   - Design workflow templates and reusability

5. **Context Management and Shielding**:
   - Implement context injection and extraction systems
   - Create context shielding and privacy controls
   - Design context relevance scoring and filtering
   - Implement context compression and optimization
   - Create context versioning and history management
   - Design context sharing and permission systems

6. **Human-in-the-Loop Integration**:
   - Implement approval workflows and decision points
   - Create user intervention and override mechanisms
   - Design feedback collection and integration
   - Implement manual control and automation balance
   - Create transparency and explainability features
   - Design user preference learning and adaptation

**Agent Implementation Example**:

```python
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import asyncio
import logging

class AgentStatus(Enum):
    IDLE = "idle"
    PROCESSING = "processing"
    WAITING = "waiting"
    COMPLETED = "completed"
    ERROR = "error"

@dataclass
class AgentMessage:
    sender: str
    recipient: str
    message_type: str
    content: Dict[str, Any]
    timestamp: float

class BaseAgent(ABC):
    def __init__(self, agent_id: str, name: str, capabilities: List[str]):
        self.agent_id = agent_id
        self.name = name
        self.capabilities = capabilities
        self.status = AgentStatus.IDLE
        self.context = {}
        self.message_queue = asyncio.Queue()
        self.logger = logging.getLogger(f"agent.{agent_id}")
    
    @abstractmethod
    async def process_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Process a task and return results"""
        pass
    
    async def send_message(self, recipient: str, message_type: str, content: Dict[str, Any]):
        """Send message to another agent"""
        message = AgentMessage(
            sender=self.agent_id,
            recipient=recipient,
            message_type=message_type,
            content=content,
            timestamp=time.time()
        )
        await self.message_bus.send_message(message)
    
    async def receive_message(self) -> Optional[AgentMessage]:
        """Receive message from message queue"""
        try:
            return await asyncio.wait_for(self.message_queue.get(), timeout=1.0)
        except asyncio.TimeoutError:
            return None
    
    def update_context(self, context_update: Dict[str, Any]):
        """Update agent context with new information"""
        self.context.update(context_update)
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get agent performance metrics"""
        return {
            "agent_id": self.agent_id,
            "status": self.status.value,
            "tasks_completed": getattr(self, 'tasks_completed', 0),
            "average_processing_time": getattr(self, 'avg_processing_time', 0),
            "error_rate": getattr(self, 'error_rate', 0)
        }

class ProjectNavigatorAgent(BaseAgent):
    def __init__(self):
        super().__init__(
            agent_id="project_navigator",
            name="Project Navigator AI",
            capabilities=["project_definition", "goal_clarification", "milestone_tracking"]
        )
    
    async def process_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Process project navigation tasks"""
        task_type = task.get("type")
        
        if task_type == "define_project":
            return await self._define_project(task["data"])
        elif task_type == "update_goals":
            return await self._update_goals(task["data"])
        elif task_type == "assess_progress":
            return await self._assess_progress(task["data"])
        else:
            raise ValueError(f"Unknown task type: {task_type}")
    
    async def _define_project(self, project_data: Dict[str, Any]) -> Dict[str, Any]:
        """Define project with goals and scope"""
        # Implement project definition logic
        prompt = self._create_project_definition_prompt(project_data)
        response = await self.ai_client.generate_completion(
            prompt=prompt,
            model=ModelType.GPT_4,
            temperature=0.3
        )
        
        return {
            "project_summary": self._parse_project_summary(response),
            "goals": self._extract_goals(response),
            "scope": self._extract_scope(response),
            "milestones": self._extract_milestones(response)
        }
    
    def _create_project_definition_prompt(self, project_data: Dict[str, Any]) -> str:
        """Create prompt for project definition"""
        return f"""
        As a Project Navigator AI, help define a comprehensive project based on the following information:
        
        Initial Description: {project_data.get('description', '')}
        User Goals: {project_data.get('user_goals', '')}
        Constraints: {project_data.get('constraints', '')}
        
        Please provide:
        1. A clear project summary
        2. Short-term and long-term goals
        3. Project scope with assumptions and constraints
        4. Key milestones and success criteria
        5. Potential risks and mitigation strategies
        
        Format your response as structured JSON for easy parsing.
        """

class PromptEngineerAgent(BaseAgent):
    def __init__(self):
        super().__init__(
            agent_id="prompt_engineer",
            name="Prompt/Context Engineering AI",
            capabilities=["prompt_optimization", "context_curation", "strategy_development"]
        )
    
    async def process_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Process prompt engineering tasks"""
        # Implement prompt engineering logic
        pass

class AgentOrchestrator:
    def __init__(self):
        self.agents = {}
        self.message_bus = MessageBus()
        self.workflow_engine = WorkflowEngine()
        self.context_manager = ContextManager()
    
    def register_agent(self, agent: BaseAgent):
        """Register an agent with the orchestrator"""
        self.agents[agent.agent_id] = agent
        agent.message_bus = self.message_bus
        agent.ai_client = OpenRouterClient(api_key=os.getenv("OPENROUTER_API_KEY"))
    
    async def execute_workflow(self, workflow_config: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a complete SynergyAI workflow"""
        workflow_id = workflow_config["workflow_id"]
        steps = workflow_config["steps"]
        
        results = {}
        for step in steps:
            agent_id = step["agent"]
            task = step["task"]
            
            if agent_id in self.agents:
                agent = self.agents[agent_id]
                result = await agent.process_task(task)
                results[step["step_id"]] = result
                
                # Update context for subsequent steps
                self.context_manager.update_context(workflow_id, result)
        
        return results
```

**Implementation Guidelines**:
- Use async/await for all agent operations to enable concurrent execution
- Implement proper error handling and recovery mechanisms
- Create comprehensive logging for agent activities and decisions
- Use message queues for reliable inter-agent communication
- Implement agent performance monitoring and optimization
- Create unit tests for all agent functionality
- Design for scalability with agent load balancing

**Expected Output**: A complete multi-agent system that implements the SynergyAI workflow with specialized agents, proper orchestration, and human-in-the-loop controls.

### Prompt 5.3: Context Management and Memory System

Develop sophisticated context management and memory systems for AI agents:

**Objective**: Create intelligent context management that handles information flow between agents, implements context shielding, and maintains workflow memory while optimizing for performance and relevance.

**Context Management Requirements**:

1. **Context Data Structures**:
   - Design hierarchical context storage with different levels of detail
   - Create context metadata with relevance scores and timestamps
   - Implement context relationships and dependency mapping
   - Design context compression and summarization algorithms
   - Create context versioning and history tracking
   - Implement context tagging and categorization systems

2. **Context Injection and Extraction**:
   - Implement intelligent context selection based on task requirements
   - Create context relevance scoring and ranking algorithms
   - Design context window optimization for different AI models
   - Implement context preprocessing and formatting
   - Create context validation and quality assessment
   - Design context caching and optimization strategies

3. **Context Shielding and Privacy**:
   - Implement configurable context shielding mechanisms
   - Create privacy controls for sensitive information
   - Design context access permissions and restrictions
   - Implement context anonymization and redaction
   - Create context audit trails and compliance features
   - Design context sharing and collaboration controls

4. **Memory Management System**:
   - Implement short-term and long-term memory systems
   - Create memory consolidation and optimization algorithms
   - Design memory retrieval and search capabilities
   - Implement memory forgetting and cleanup mechanisms
   - Create memory performance monitoring and optimization
   - Design memory backup and recovery systems

5. **Context Optimization**:
   - Implement context compression for large datasets
   - Create context summarization with key information preservation
   - Design context deduplication and redundancy removal
   - Implement context prioritization and filtering
   - Create context performance monitoring and optimization
   - Design context cost optimization for API usage

6. **Integration with AI Models**:
   - Create model-specific context formatting and optimization
   - Implement context window management for different models
   - Design context injection strategies for optimal performance
   - Create context-aware prompt generation and optimization
   - Implement context feedback and learning systems
   - Design context adaptation for different AI capabilities

**Context Management Implementation**:

```python
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import json
import hashlib
from enum import Enum

class ContextType(Enum):
    PROJECT_SUMMARY = "project_summary"
    PROMPT_STRATEGY = "prompt_strategy"
    AI_RESPONSE = "ai_response"
    USER_INPUT = "user_input"
    SYSTEM_STATE = "system_state"
    ITERATION_RESULT = "iteration_result"

class ContextRelevance(Enum):
    CRITICAL = 1.0
    HIGH = 0.8
    MEDIUM = 0.6
    LOW = 0.4
    MINIMAL = 0.2

@dataclass
class ContextItem:
    id: str
    type: ContextType
    content: str
    metadata: Dict[str, Any]
    relevance_score: float
    created_at: datetime
    updated_at: datetime
    tags: List[str] = field(default_factory=list)
    relationships: List[str] = field(default_factory=list)
    access_level: str = "public"
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "type": self.type.value,
            "content": self.content,
            "metadata": self.metadata,
            "relevance_score": self.relevance_score,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "tags": self.tags,
            "relationships": self.relationships,
            "access_level": self.access_level
        }

class ContextManager:
    def __init__(self, max_context_size: int = 100000):
        self.contexts: Dict[str, List[ContextItem]] = {}
        self.max_context_size = max_context_size
        self.shielding_rules: Dict[str, Dict[str, Any]] = {}
        self.compression_threshold = 0.8
    
    def add_context(self, workflow_id: str, context_item: ContextItem):
        """Add context item to workflow"""
        if workflow_id not in self.contexts:
            self.contexts[workflow_id] = []
        
        self.contexts[workflow_id].append(context_item)
        self._optimize_context_size(workflow_id)
    
    def get_relevant_context(
        self,
        workflow_id: str,
        task_type: str,
        max_tokens: int = 4000,
        min_relevance: float = 0.4
    ) -> List[ContextItem]:
        """Get relevant context for a specific task"""
        if workflow_id not in self.contexts:
            return []
        
        # Filter by relevance and access permissions
        relevant_items = [
            item for item in self.contexts[workflow_id]
            if item.relevance_score >= min_relevance
            and self._check_access_permission(workflow_id, item, task_type)
        ]
        
        # Sort by relevance and recency
        relevant_items.sort(
            key=lambda x: (x.relevance_score, x.updated_at),
            reverse=True
        )
        
        # Fit within token limit
        selected_items = []
        total_tokens = 0
        
        for item in relevant_items:
            item_tokens = self._estimate_tokens(item.content)
            if total_tokens + item_tokens <= max_tokens:
                selected_items.append(item)
                total_tokens += item_tokens
            else:
                break
        
        return selected_items
    
    def apply_context_shielding(
        self,
        workflow_id: str,
        agent_id: str,
        context_items: List[ContextItem]
    ) -> List[ContextItem]:
        """Apply context shielding rules for specific agent"""
        shielding_config = self.shielding_rules.get(workflow_id, {})
        agent_rules = shielding_config.get(agent_id, {})
        
        if not agent_rules.get("enabled", False):
            return context_items
        
        shielded_items = []
        for item in context_items:
            if self._should_shield_context(item, agent_rules):
                # Create redacted version
                shielded_item = self._create_shielded_context(item, agent_rules)
                shielded_items.append(shielded_item)
            else:
                shielded_items.append(item)
        
        return shielded_items
    
    def compress_context(self, workflow_id: str) -> Dict[str, Any]:
        """Compress context to reduce size while preserving key information"""
        if workflow_id not in self.contexts:
            return {"compressed": False, "reason": "No context found"}
        
        context_items = self.contexts[workflow_id]
        total_size = sum(len(item.content) for item in context_items)
        
        if total_size < self.max_context_size * self.compression_threshold:
            return {"compressed": False, "reason": "Below compression threshold"}
        
        # Group similar contexts
        grouped_contexts = self._group_similar_contexts(context_items)
        
        # Compress each group
        compressed_items = []
        for group in grouped_contexts:
            if len(group) > 1:
                compressed_item = self._compress_context_group(group)
                compressed_items.append(compressed_item)
            else:
                compressed_items.extend(group)
        
        # Update context storage
        self.contexts[workflow_id] = compressed_items
        
        new_size = sum(len(item.content) for item in compressed_items)
        compression_ratio = new_size / total_size
        
        return {
            "compressed": True,
            "original_size": total_size,
            "compressed_size": new_size,
            "compression_ratio": compression_ratio,
            "items_before": len(context_items),
            "items_after": len(compressed_items)
        }
    
    def create_context_summary(
        self,
        workflow_id: str,
        summary_type: str = "comprehensive"
    ) -> ContextItem:
        """Create a summary of all context for the workflow"""
        if workflow_id not in self.contexts:
            return None
        
        context_items = self.contexts[workflow_id]
        
        # Organize context by type and relevance
        organized_context = self._organize_context_for_summary(context_items)
        
        # Generate summary based on type
        if summary_type == "comprehensive":
            summary_content = self._create_comprehensive_summary(organized_context)
        elif summary_type == "key_points":
            summary_content = self._create_key_points_summary(organized_context)
        elif summary_type == "timeline":
            summary_content = self._create_timeline_summary(organized_context)
        else:
            summary_content = self._create_default_summary(organized_context)
        
        # Create summary context item
        summary_item = ContextItem(
            id=f"summary_{workflow_id}_{datetime.now().isoformat()}",
            type=ContextType.SYSTEM_STATE,
            content=summary_content,
            metadata={
                "summary_type": summary_type,
                "source_items": len(context_items),
                "generated_at": datetime.now().isoformat()
            },
            relevance_score=ContextRelevance.HIGH.value,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            tags=["summary", summary_type],
            access_level="public"
        )
        
        return summary_item
    
    def _optimize_context_size(self, workflow_id: str):
        """Optimize context size by removing low-relevance items"""
        if workflow_id not in self.contexts:
            return
        
        context_items = self.contexts[workflow_id]
        total_size = sum(len(item.content) for item in context_items)
        
        if total_size > self.max_context_size:
            # Sort by relevance and recency
            context_items.sort(
                key=lambda x: (x.relevance_score, x.updated_at),
                reverse=True
            )
            
            # Keep items within size limit
            optimized_items = []
            current_size = 0
            
            for item in context_items:
                item_size = len(item.content)
                if current_size + item_size <= self.max_context_size:
                    optimized_items.append(item)
                    current_size += item_size
                else:
                    break
            
            self.contexts[workflow_id] = optimized_items
    
    def _estimate_tokens(self, text: str) -> int:
        """Estimate token count for text"""
        # Simple estimation: ~4 characters per token
        return len(text) // 4
    
    def _check_access_permission(
        self,
        workflow_id: str,
        context_item: ContextItem,
        task_type: str
    ) -> bool:
        """Check if context item can be accessed for task type"""
        # Implement access control logic
        return True  # Simplified for example
    
    def _should_shield_context(
        self,
        context_item: ContextItem,
        shielding_rules: Dict[str, Any]
    ) -> bool:
        """Determine if context should be shielded"""
        # Implement shielding logic based on rules
        return False  # Simplified for example
    
    def _create_shielded_context(
        self,
        context_item: ContextItem,
        shielding_rules: Dict[str, Any]
    ) -> ContextItem:
        """Create shielded version of context item"""
        # Implement context redaction/masking
        return context_item  # Simplified for example
```

**Implementation Guidelines**:
- Use efficient data structures for large context storage
- Implement proper indexing for fast context retrieval
- Create comprehensive caching strategies for performance
- Use async operations for context processing
- Implement proper error handling and recovery
- Create monitoring and analytics for context usage
- Design for horizontal scaling with distributed storage

**Expected Output**: A complete context management system that provides intelligent context handling, shielding capabilities, memory optimization, and seamless integration with the multi-agent AI system.

---


# Deployment and Testing Prompts

## Phase 6: Development Environment and Testing

### Prompt 6.1: Local Development Environment Setup

Create comprehensive development environment setup for the SynergyAI application:

**Objective**: Establish a complete local development environment that supports frontend development, backend API development, AI integration testing, and database management with proper tooling and automation.

**Development Environment Requirements**:

1. **Project Structure Organization**:
   - Create a well-organized monorepo or multi-repo structure
   - Implement proper separation between frontend and backend code
   - Design configuration management for different environments
   - Create shared utilities and common libraries
   - Implement proper version control with Git workflows
   - Design documentation structure and maintenance

2. **Frontend Development Setup**:
   - Configure modern JavaScript development environment
   - Set up build tools and bundlers (Webpack, Vite, or Parcel)
   - Implement hot module replacement for rapid development
   - Configure CSS preprocessing and PostCSS
   - Set up linting and formatting with ESLint and Prettier
   - Implement testing framework setup (Jest, Cypress, or Playwright)

3. **Backend Development Setup**:
   - Configure Python virtual environment with proper dependency management
   - Set up Flask development server with auto-reload
   - Configure database setup with SQLite for development
   - Implement database migration and seeding scripts
   - Set up API documentation with Swagger/OpenAPI
   - Configure logging and debugging tools

4. **AI Integration Development**:
   - Set up OpenRouter API key management and testing
   - Create mock AI responses for development and testing
   - Implement AI model testing and validation tools
   - Configure cost tracking and usage monitoring
   - Set up AI response caching for development efficiency
   - Create AI model comparison and benchmarking tools

5. **Database Development Setup**:
   - Configure local database with proper schema
   - Set up database migration and version control
   - Implement data seeding for development and testing
   - Create database backup and restore procedures
   - Set up database performance monitoring
   - Configure database testing with test fixtures

6. **Development Tools and Automation**:
   - Set up package managers and dependency management
   - Configure build automation and task runners
   - Implement code quality tools and pre-commit hooks
   - Set up continuous integration for development branches
   - Configure development server orchestration
   - Create development environment documentation

**Development Setup Script**:

```bash
#!/bin/bash
# SynergyAI Development Environment Setup Script

echo "Setting up SynergyAI Development Environment..."

# Check prerequisites
check_prerequisites() {
    echo "Checking prerequisites..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        echo "Node.js is required. Please install Node.js 16+ and try again."
        exit 1
    fi
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        echo "Python 3.8+ is required. Please install Python and try again."
        exit 1
    fi
    
    # Check Git
    if ! command -v git &> /dev/null; then
        echo "Git is required. Please install Git and try again."
        exit 1
    fi
    
    echo "Prerequisites check passed!"
}

# Setup project structure
setup_project_structure() {
    echo "Setting up project structure..."
    
    mkdir -p synergyai-app/{frontend,backend,shared,docs,scripts,tests}
    mkdir -p synergyai-app/frontend/{src,public,tests}
    mkdir -p synergyai-app/frontend/src/{components,services,utils,styles}
    mkdir -p synergyai-app/backend/{app,tests,migrations,config}
    mkdir -p synergyai-app/backend/app/{models,api,services,utils}
    mkdir -p synergyai-app/shared/{types,constants,utils}
    mkdir -p synergyai-app/docs/{api,development,deployment}
    
    echo "Project structure created!"
}

# Setup frontend development
setup_frontend() {
    echo "Setting up frontend development environment..."
    
    cd synergyai-app/frontend
    
    # Initialize package.json
    cat > package.json << EOF
{
  "name": "synergyai-frontend",
  "version": "1.0.0",
  "description": "SynergyAI Frontend Application",
  "main": "src/index.js",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview",
    "test": "jest",
    "test:watch": "jest --watch",
    "lint": "eslint src --ext .js,.jsx,.ts,.tsx",
    "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix",
    "format": "prettier --write src/**/*.{js,jsx,ts,tsx,css,md}"
  },
  "dependencies": {
    "axios": "^1.6.0",
    "chart.js": "^4.4.0",
    "date-fns": "^2.30.0",
    "lodash": "^4.17.21",
    "uuid": "^9.0.1"
  },
  "devDependencies": {
    "vite": "^5.0.0",
    "@vitejs/plugin-legacy": "^5.0.0",
    "eslint": "^8.55.0",
    "prettier": "^3.1.0",
    "jest": "^29.7.0",
    "@testing-library/jest-dom": "^6.1.0",
    "@testing-library/dom": "^9.3.0"
  }
}
EOF
    
    # Create Vite config
    cat > vite.config.js << EOF
import { defineConfig } from 'vite'
import legacy from '@vitejs/plugin-legacy'

export default defineConfig({
  plugins: [
    legacy({
      targets: ['defaults', 'not IE 11']
    })
  ],
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:5000',
        changeOrigin: true
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: true
  }
})
EOF
    
    # Install dependencies
    npm install
    
    cd ../..
    echo "Frontend setup completed!"
}

# Setup backend development
setup_backend() {
    echo "Setting up backend development environment..."
    
    cd synergyai-app/backend
    
    # Create virtual environment
    python3 -m venv venv
    source venv/bin/activate
    
    # Create requirements.txt
    cat > requirements.txt << EOF
Flask==3.0.0
Flask-SQLAlchemy==3.1.1
Flask-Migrate==4.0.5
Flask-JWT-Extended==4.6.0
Flask-CORS==4.0.0
Flask-RESTful==0.3.10
requests==2.31.0
python-dotenv==1.0.0
marshmallow==3.20.1
celery==5.3.4
redis==5.0.1
pytest==7.4.3
pytest-flask==1.3.0
black==23.11.0
flake8==6.1.0
EOF
    
    # Install dependencies
    pip install -r requirements.txt
    
    # Create Flask app structure
    cat > app/__init__.py << EOF
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_jwt_extended import JWTManager
from flask_cors import CORS
from config import Config

db = SQLAlchemy()
migrate = Migrate()
jwt = JWTManager()

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    db.init_app(app)
    migrate.init_app(app, db)
    jwt.init_app(app)
    CORS(app)
    
    from app.api import bp as api_bp
    app.register_blueprint(api_bp, url_prefix='/api/v1')
    
    return app
EOF
    
    # Create config file
    cat > config.py << EOF
import os
from dotenv import load_dotenv

basedir = os.path.abspath(os.path.dirname(__file__))
load_dotenv(os.path.join(basedir, '.env'))

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'sqlite:///' + os.path.join(basedir, 'app.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-secret-string'
    OPENROUTER_API_KEY = os.environ.get('OPENROUTER_API_KEY')
EOF
    
    # Create .env template
    cat > .env.example << EOF
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-here
OPENROUTER_API_KEY=your-openrouter-api-key-here
DATABASE_URL=sqlite:///app.db
FLASK_ENV=development
FLASK_DEBUG=1
EOF
    
    cd ../..
    echo "Backend setup completed!"
}

# Setup development scripts
setup_scripts() {
    echo "Setting up development scripts..."
    
    cd synergyai-app/scripts
    
    # Create development server script
    cat > dev-server.sh << EOF
#!/bin/bash
# Start development servers

echo "Starting SynergyAI development servers..."

# Start backend
cd ../backend
source venv/bin/activate
export FLASK_APP=app
export FLASK_ENV=development
flask run --host=0.0.0.0 --port=5000 &
BACKEND_PID=\$!

# Start frontend
cd ../frontend
npm run dev &
FRONTEND_PID=\$!

echo "Backend running on http://localhost:5000"
echo "Frontend running on http://localhost:3000"
echo "Press Ctrl+C to stop all servers"

# Wait for interrupt
trap "kill \$BACKEND_PID \$FRONTEND_PID" INT
wait
EOF
    
    chmod +x dev-server.sh
    
    cd ../..
    echo "Development scripts created!"
}

# Main setup function
main() {
    check_prerequisites
    setup_project_structure
    setup_frontend
    setup_backend
    setup_scripts
    
    echo ""
    echo "🎉 SynergyAI development environment setup completed!"
    echo ""
    echo "Next steps:"
    echo "1. Copy backend/.env.example to backend/.env and configure your API keys"
    echo "2. Run 'cd synergyai-app && scripts/dev-server.sh' to start development servers"
    echo "3. Open http://localhost:3000 to view the application"
    echo ""
    echo "Happy coding! 🚀"
}

main
```

**Implementation Guidelines**:
- Use containerization with Docker for consistent environments
- Implement proper environment variable management
- Create comprehensive documentation for setup procedures
- Use automation scripts for repetitive tasks
- Implement proper error handling in setup scripts
- Create development environment health checks
- Design for easy onboarding of new developers

**Expected Output**: A complete development environment setup that enables efficient development, testing, and debugging of the SynergyAI application with proper tooling and automation.

### Prompt 6.2: Testing Strategy and Implementation

Develop comprehensive testing strategies for all components of the SynergyAI application:

**Objective**: Create a robust testing framework that covers unit testing, integration testing, end-to-end testing, AI model testing, and performance testing with proper automation and reporting.

**Testing Strategy Requirements**:

1. **Frontend Testing Framework**:
   - Implement unit testing for JavaScript components and utilities
   - Create integration testing for API interactions and data flow
   - Set up end-to-end testing for complete user workflows
   - Design visual regression testing for UI consistency
   - Implement accessibility testing for compliance
   - Create performance testing for frontend optimization

2. **Backend Testing Framework**:
   - Implement unit testing for all API endpoints and services
   - Create integration testing for database operations
   - Set up testing for AI agent interactions and workflows
   - Design security testing for authentication and authorization
   - Implement load testing for performance optimization
   - Create contract testing for API consistency

3. **AI Integration Testing**:
   - Implement testing for OpenRouter API integration
   - Create mock AI responses for consistent testing
   - Set up testing for AI agent coordination and communication
   - Design testing for context management and shielding
   - Implement testing for AI response processing and validation
   - Create cost and performance testing for AI operations

4. **Database Testing**:
   - Implement testing for database models and relationships
   - Create testing for database migrations and schema changes
   - Set up testing for data integrity and constraints
   - Design testing for database performance and optimization
   - Implement testing for backup and recovery procedures
   - Create testing for data privacy and security

5. **End-to-End Workflow Testing**:
   - Implement testing for complete SynergyAI workflows
   - Create testing for multi-agent coordination scenarios
   - Set up testing for iterative loop functionality
   - Design testing for human-in-the-loop interactions
   - Implement testing for error handling and recovery
   - Create testing for edge cases and boundary conditions

6. **Performance and Load Testing**:
   - Implement performance testing for API endpoints
   - Create load testing for concurrent user scenarios
   - Set up testing for AI model performance and optimization
   - Design testing for database performance under load
   - Implement testing for memory usage and resource optimization
   - Create testing for scalability and bottleneck identification

**Testing Implementation Framework**:

```javascript
// Frontend Testing Setup (Jest + Testing Library)

// tests/setup.js
import '@testing-library/jest-dom';
import { TextEncoder, TextDecoder } from 'util';

// Polyfills for Node.js environment
global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

// Mock fetch for API testing
global.fetch = jest.fn();

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// tests/components/ProjectNavigator.test.js
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ProjectNavigator } from '../../src/components/ProjectNavigator';
import { APIService } from '../../src/services/APIService';

// Mock API service
jest.mock('../../src/services/APIService');

describe('ProjectNavigator Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders project creation form', () => {
    render(<ProjectNavigator />);
    
    expect(screen.getByText('Create New Project')).toBeInTheDocument();
    expect(screen.getByLabelText('Project Title')).toBeInTheDocument();
    expect(screen.getByLabelText('Project Description')).toBeInTheDocument();
  });

  test('creates project with valid input', async () => {
    const mockCreateProject = jest.fn().mockResolvedValue({
      id: 1,
      title: 'Test Project',
      description: 'Test Description'
    });
    APIService.createProject = mockCreateProject;

    render(<ProjectNavigator />);
    
    fireEvent.change(screen.getByLabelText('Project Title'), {
      target: { value: 'Test Project' }
    });
    fireEvent.change(screen.getByLabelText('Project Description'), {
      target: { value: 'Test Description' }
    });
    
    fireEvent.click(screen.getByText('Create Project'));
    
    await waitFor(() => {
      expect(mockCreateProject).toHaveBeenCalledWith({
        title: 'Test Project',
        description: 'Test Description'
      });
    });
  });

  test('displays error message on API failure', async () => {
    const mockCreateProject = jest.fn().mockRejectedValue(
      new Error('API Error')
    );
    APIService.createProject = mockCreateProject;

    render(<ProjectNavigator />);
    
    fireEvent.change(screen.getByLabelText('Project Title'), {
      target: { value: 'Test Project' }
    });
    fireEvent.click(screen.getByText('Create Project'));
    
    await waitFor(() => {
      expect(screen.getByText('Failed to create project')).toBeInTheDocument();
    });
  });
});

// tests/services/AIOrchestrator.test.js
import { AIOrchestrator } from '../../src/services/AIOrchestrator';
import { OpenRouterClient } from '../../src/services/OpenRouterClient';

jest.mock('../../src/services/OpenRouterClient');

describe('AIOrchestrator Service', () => {
  let orchestrator;
  let mockOpenRouterClient;

  beforeEach(() => {
    mockOpenRouterClient = {
      generateCompletion: jest.fn(),
      selectOptimalModel: jest.fn(),
      estimateCost: jest.fn()
    };
    OpenRouterClient.mockImplementation(() => mockOpenRouterClient);
    orchestrator = new AIOrchestrator();
  });

  test('executes single agent workflow', async () => {
    const mockResponse = {
      choices: [{ message: { content: 'Test response' } }],
      usage: { total_tokens: 100 }
    };
    mockOpenRouterClient.generateCompletion.mockResolvedValue(mockResponse);

    const result = await orchestrator.executeAgent('project_navigator', {
      type: 'define_project',
      data: { description: 'Test project' }
    });

    expect(result).toBeDefined();
    expect(result.response).toBe('Test response');
    expect(mockOpenRouterClient.generateCompletion).toHaveBeenCalled();
  });

  test('handles multi-agent workflow coordination', async () => {
    const workflow = {
      steps: [
        { agent: 'project_navigator', task: { type: 'define_project' } },
        { agent: 'prompt_engineer', task: { type: 'create_strategy' } }
      ]
    };

    mockOpenRouterClient.generateCompletion
      .mockResolvedValueOnce({ choices: [{ message: { content: 'Project defined' } }] })
      .mockResolvedValueOnce({ choices: [{ message: { content: 'Strategy created' } }] });

    const result = await orchestrator.executeWorkflow(workflow);

    expect(result.steps).toHaveLength(2);
    expect(mockOpenRouterClient.generateCompletion).toHaveBeenCalledTimes(2);
  });
});
```

```python
# Backend Testing Setup (pytest + Flask-Testing)

# tests/conftest.py
import pytest
import tempfile
import os
from app import create_app, db
from app.models import User, Project, Prompt
from config import Config

class TestConfig(Config):
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False
    JWT_SECRET_KEY = 'test-jwt-secret'

@pytest.fixture
def app():
    app = create_app(TestConfig)
    
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()

@pytest.fixture
def client(app):
    return app.test_client()

@pytest.fixture
def auth_headers(client):
    # Create test user and get auth token
    user_data = {
        'username': 'testuser',
        'email': '<EMAIL>',
        'password': 'testpassword'
    }
    client.post('/api/v1/auth/register', json=user_data)
    
    response = client.post('/api/v1/auth/login', json={
        'username': 'testuser',
        'password': 'testpassword'
    })
    
    token = response.json['access_token']
    return {'Authorization': f'Bearer {token}'}

# tests/test_api_projects.py
import pytest
from app.models import Project

def test_create_project(client, auth_headers):
    """Test project creation endpoint"""
    project_data = {
        'title': 'Test Project',
        'description': 'Test project description',
        'goals': {
            'short_term': ['Goal 1', 'Goal 2'],
            'long_term': ['Long term goal']
        }
    }
    
    response = client.post(
        '/api/v1/projects',
        json=project_data,
        headers=auth_headers
    )
    
    assert response.status_code == 201
    assert response.json['title'] == 'Test Project'
    assert 'id' in response.json

def test_get_projects(client, auth_headers):
    """Test project listing endpoint"""
    # Create test projects
    for i in range(3):
        client.post(
            '/api/v1/projects',
            json={'title': f'Project {i}', 'description': f'Description {i}'},
            headers=auth_headers
        )
    
    response = client.get('/api/v1/projects', headers=auth_headers)
    
    assert response.status_code == 200
    assert len(response.json['projects']) == 3

def test_update_project(client, auth_headers):
    """Test project update endpoint"""
    # Create project
    create_response = client.post(
        '/api/v1/projects',
        json={'title': 'Original Title', 'description': 'Original Description'},
        headers=auth_headers
    )
    project_id = create_response.json['id']
    
    # Update project
    update_data = {'title': 'Updated Title'}
    response = client.put(
        f'/api/v1/projects/{project_id}',
        json=update_data,
        headers=auth_headers
    )
    
    assert response.status_code == 200
    assert response.json['title'] == 'Updated Title'

# tests/test_ai_integration.py
import pytest
from unittest.mock import Mock, patch
from app.services.ai_orchestrator import AIOrchestrator
from app.services.openrouter_client import OpenRouterClient

@pytest.fixture
def mock_openrouter_client():
    client = Mock(spec=OpenRouterClient)
    client.generate_completion.return_value = {
        'choices': [{'message': {'content': 'Test AI response'}}],
        'usage': {'total_tokens': 100}
    }
    return client

def test_project_navigator_agent(mock_openrouter_client):
    """Test Project Navigator AI agent"""
    orchestrator = AIOrchestrator()
    orchestrator.openrouter_client = mock_openrouter_client
    
    task = {
        'type': 'define_project',
        'data': {
            'description': 'Build a web application',
            'user_goals': 'Create an efficient workflow system'
        }
    }
    
    result = orchestrator.execute_agent('project_navigator', task)
    
    assert result is not None
    assert 'project_summary' in result
    mock_openrouter_client.generate_completion.assert_called_once()

def test_multi_agent_workflow(mock_openrouter_client):
    """Test multi-agent workflow execution"""
    orchestrator = AIOrchestrator()
    orchestrator.openrouter_client = mock_openrouter_client
    
    workflow = {
        'workflow_id': 'test_workflow',
        'steps': [
            {
                'step_id': 'define_project',
                'agent': 'project_navigator',
                'task': {'type': 'define_project', 'data': {}}
            },
            {
                'step_id': 'create_prompts',
                'agent': 'prompt_engineer',
                'task': {'type': 'create_strategy', 'data': {}}
            }
        ]
    }
    
    result = orchestrator.execute_workflow(workflow)
    
    assert len(result) == 2
    assert 'define_project' in result
    assert 'create_prompts' in result
    assert mock_openrouter_client.generate_completion.call_count == 2

# tests/test_context_management.py
import pytest
from app.services.context_manager import ContextManager, ContextItem, ContextType

def test_context_addition_and_retrieval():
    """Test context addition and retrieval"""
    manager = ContextManager()
    
    context_item = ContextItem(
        id='test_context_1',
        type=ContextType.PROJECT_SUMMARY,
        content='Test project summary content',
        metadata={'source': 'test'},
        relevance_score=0.8,
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    manager.add_context('workflow_1', context_item)
    
    relevant_context = manager.get_relevant_context(
        'workflow_1',
        'project_definition',
        max_tokens=1000,
        min_relevance=0.5
    )
    
    assert len(relevant_context) == 1
    assert relevant_context[0].id == 'test_context_1'

def test_context_shielding():
    """Test context shielding functionality"""
    manager = ContextManager()
    
    # Set up shielding rules
    manager.shielding_rules['workflow_1'] = {
        'prompt_engineer': {
            'enabled': True,
            'shield_types': ['PROJECT_SUMMARY']
        }
    }
    
    context_items = [
        ContextItem(
            id='summary_1',
            type=ContextType.PROJECT_SUMMARY,
            content='Sensitive project information',
            metadata={},
            relevance_score=0.9,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    ]
    
    shielded_context = manager.apply_context_shielding(
        'workflow_1',
        'prompt_engineer',
        context_items
    )
    
    # Verify shielding was applied
    assert len(shielded_context) == 1
    # Additional assertions based on shielding implementation
```

**End-to-End Testing with Playwright**:

```javascript
// e2e/tests/complete-workflow.spec.js
import { test, expect } from '@playwright/test';

test.describe('Complete SynergyAI Workflow', () => {
  test.beforeEach(async ({ page }) => {
    // Set up test environment
    await page.goto('http://localhost:3000');
    
    // Login or create test user
    await page.click('[data-testid="login-button"]');
    await page.fill('[data-testid="username-input"]', 'testuser');
    await page.fill('[data-testid="password-input"]', 'testpassword');
    await page.click('[data-testid="submit-login"]');
    
    await expect(page.locator('[data-testid="dashboard"]')).toBeVisible();
  });

  test('complete project workflow from creation to completion', async ({ page }) => {
    // Step 1: Create new project
    await page.click('[data-testid="create-project-button"]');
    await page.fill('[data-testid="project-title"]', 'E2E Test Project');
    await page.fill('[data-testid="project-description"]', 'End-to-end test project description');
    await page.click('[data-testid="submit-project"]');
    
    await expect(page.locator('[data-testid="project-created-message"]')).toBeVisible();
    
    // Step 2: Define project goals with Project Navigator AI
    await page.click('[data-testid="define-goals-button"]');
    await page.fill('[data-testid="goals-input"]', 'Create an efficient task management system');
    await page.click('[data-testid="generate-project-summary"]');
    
    // Wait for AI response
    await expect(page.locator('[data-testid="project-summary"]')).toBeVisible({ timeout: 30000 });
    
    // Step 3: Create prompt strategy
    await page.click('[data-testid="create-prompt-strategy"]');
    await page.fill('[data-testid="prompt-content"]', 'Design a user-friendly interface for task management');
    await page.selectOption('[data-testid="ai-model-select"]', 'gpt-4');
    await page.click('[data-testid="save-prompt-strategy"]');
    
    await expect(page.locator('[data-testid="strategy-saved-message"]')).toBeVisible();
    
    // Step 4: Execute AI workflow
    await page.click('[data-testid="execute-workflow"]');
    
    // Wait for workflow completion
    await expect(page.locator('[data-testid="workflow-completed"]')).toBeVisible({ timeout: 60000 });
    
    // Step 5: Review and iterate
    await page.click('[data-testid="review-results"]');
    await expect(page.locator('[data-testid="ai-response-content"]')).toBeVisible();
    
    // Verify iteration controls are available
    await expect(page.locator('[data-testid="iterate-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="export-results"]')).toBeVisible();
  });

  test('multi-agent coordination workflow', async ({ page }) => {
    // Test complex multi-agent workflow
    await page.click('[data-testid="advanced-workflow"]');
    
    // Configure multiple agents
    await page.check('[data-testid="enable-project-navigator"]');
    await page.check('[data-testid="enable-prompt-engineer"]');
    await page.check('[data-testid="enable-summarizer"]');
    
    // Set workflow parameters
    await page.fill('[data-testid="max-iterations"]', '3');
    await page.selectOption('[data-testid="workflow-mode"]', 'collaborative');
    
    // Start workflow
    await page.click('[data-testid="start-advanced-workflow"]');
    
    // Monitor workflow progress
    await expect(page.locator('[data-testid="workflow-timeline"]')).toBeVisible();
    
    // Verify agent coordination
    await expect(page.locator('[data-testid="agent-communication-log"]')).toBeVisible();
    
    // Wait for completion
    await expect(page.locator('[data-testid="workflow-summary"]')).toBeVisible({ timeout: 120000 });
  });
});
```

**Implementation Guidelines**:
- Use test-driven development (TDD) approach where appropriate
- Implement comprehensive test coverage with minimum 80% coverage
- Create automated test execution in CI/CD pipeline
- Use proper test data management and cleanup
- Implement performance benchmarking in tests
- Create visual regression testing for UI components
- Design tests for maintainability and readability

**Expected Output**: A comprehensive testing framework that ensures reliability, performance, and correctness of all SynergyAI application components with automated execution and reporting.

### Prompt 6.3: Production Deployment Configuration

Create comprehensive production deployment configuration and procedures:

**Objective**: Develop robust production deployment strategies that ensure scalability, security, reliability, and performance for the SynergyAI application in cloud environments.

**Production Deployment Requirements**:

1. **Infrastructure as Code**:
   - Create Docker containerization for frontend and backend
   - Implement Kubernetes deployment configurations
   - Design cloud infrastructure with Terraform or CloudFormation
   - Set up load balancing and auto-scaling configurations
   - Implement database clustering and replication
   - Create monitoring and logging infrastructure

2. **Security Configuration**:
   - Implement HTTPS/TLS encryption for all communications
   - Set up Web Application Firewall (WAF) protection
   - Configure API rate limiting and DDoS protection
   - Implement secure secrets management
   - Set up vulnerability scanning and security monitoring
   - Create backup encryption and secure storage

3. **Performance Optimization**:
   - Configure CDN for static asset delivery
   - Implement database query optimization and indexing
   - Set up caching layers (Redis, Memcached)
   - Configure application performance monitoring
   - Implement database connection pooling
   - Create performance testing and benchmarking

4. **Scalability Architecture**:
   - Design horizontal scaling for application servers
   - Implement database sharding and read replicas
   - Set up message queues for asynchronous processing
   - Configure auto-scaling based on metrics
   - Implement microservices architecture considerations
   - Create load testing and capacity planning

5. **Monitoring and Observability**:
   - Set up comprehensive application monitoring
   - Implement centralized logging and log analysis
   - Create performance metrics and alerting
   - Set up error tracking and reporting
   - Implement health checks and uptime monitoring
   - Create dashboards and reporting systems

6. **Disaster Recovery and Backup**:
   - Implement automated backup procedures
   - Create disaster recovery plans and procedures
   - Set up cross-region replication
   - Implement point-in-time recovery capabilities
   - Create backup testing and validation
   - Design business continuity procedures

**Docker Configuration**:

```dockerfile
# Frontend Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY . .

# Build application
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built application
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/health || exit 1

CMD ["nginx", "-g", "daemon off;"]
```

```dockerfile
# Backend Dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd --create-home --shell /bin/bash app
RUN chown -R app:app /app
USER app

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:5000/health || exit 1

# Run application
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "4", "--timeout", "120", "app:create_app()"]
```

**Kubernetes Deployment Configuration**:

```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: synergyai
  labels:
    name: synergyai

---
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: synergyai-config
  namespace: synergyai
data:
  FLASK_ENV: "production"
  DATABASE_URL: "****************************************/synergyai"
  REDIS_URL: "redis://redis:6379/0"

---
# k8s/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: synergyai-secrets
  namespace: synergyai
type: Opaque
data:
  SECRET_KEY: <base64-encoded-secret>
  JWT_SECRET_KEY: <base64-encoded-jwt-secret>
  OPENROUTER_API_KEY: <base64-encoded-api-key>

---
# k8s/backend-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: synergyai-backend
  namespace: synergyai
spec:
  replicas: 3
  selector:
    matchLabels:
      app: synergyai-backend
  template:
    metadata:
      labels:
        app: synergyai-backend
    spec:
      containers:
      - name: backend
        image: synergyai/backend:latest
        ports:
        - containerPort: 5000
        env:
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: synergyai-secrets
              key: SECRET_KEY
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: synergyai-secrets
              key: JWT_SECRET_KEY
        - name: OPENROUTER_API_KEY
          valueFrom:
            secretKeyRef:
              name: synergyai-secrets
              key: OPENROUTER_API_KEY
        envFrom:
        - configMapRef:
            name: synergyai-config
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 5000
          initialDelaySeconds: 5
          periodSeconds: 5

---
# k8s/backend-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: synergyai-backend-service
  namespace: synergyai
spec:
  selector:
    app: synergyai-backend
  ports:
  - protocol: TCP
    port: 80
    targetPort: 5000
  type: ClusterIP

---
# k8s/frontend-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: synergyai-frontend
  namespace: synergyai
spec:
  replicas: 2
  selector:
    matchLabels:
      app: synergyai-frontend
  template:
    metadata:
      labels:
        app: synergyai-frontend
    spec:
      containers:
      - name: frontend
        image: synergyai/frontend:latest
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5

---
# k8s/frontend-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: synergyai-frontend-service
  namespace: synergyai
spec:
  selector:
    app: synergyai-frontend
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80
  type: ClusterIP

---
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: synergyai-ingress
  namespace: synergyai
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - synergyai.example.com
    secretName: synergyai-tls
  rules:
  - host: synergyai.example.com
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: synergyai-backend-service
            port:
              number: 80
      - path: /
        pathType: Prefix
        backend:
          service:
            name: synergyai-frontend-service
            port:
              number: 80

---
# k8s/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: synergyai-backend-hpa
  namespace: synergyai
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: synergyai-backend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

**CI/CD Pipeline Configuration**:

```yaml
# .github/workflows/deploy.yml
name: Deploy SynergyAI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install frontend dependencies
      run: |
        cd frontend
        npm ci
    
    - name: Install backend dependencies
      run: |
        cd backend
        pip install -r requirements.txt
    
    - name: Run frontend tests
      run: |
        cd frontend
        npm run test:ci
    
    - name: Run backend tests
      run: |
        cd backend
        pytest --cov=app --cov-report=xml
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        files: ./backend/coverage.xml,./frontend/coverage/lcov.info

  build-and-push:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Build and push frontend image
      uses: docker/build-push-action@v5
      with:
        context: ./frontend
        push: true
        tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/frontend:latest
    
    - name: Build and push backend image
      uses: docker/build-push-action@v5
      with:
        context: ./backend
        push: true
        tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/backend:latest

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG }}
    
    - name: Deploy to Kubernetes
      run: |
        kubectl apply -f k8s/
        kubectl rollout status deployment/synergyai-backend -n synergyai
        kubectl rollout status deployment/synergyai-frontend -n synergyai
    
    - name: Run smoke tests
      run: |
        kubectl wait --for=condition=ready pod -l app=synergyai-backend -n synergyai --timeout=300s
        kubectl wait --for=condition=ready pod -l app=synergyai-frontend -n synergyai --timeout=300s
        
        # Run basic health checks
        kubectl exec -n synergyai deployment/synergyai-backend -- curl -f http://localhost:5000/health
```

**Monitoring and Observability Configuration**:

```yaml
# monitoring/prometheus-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
    
    rule_files:
      - "synergyai_rules.yml"
    
    scrape_configs:
      - job_name: 'synergyai-backend'
        kubernetes_sd_configs:
          - role: pod
            namespaces:
              names:
                - synergyai
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_label_app]
            action: keep
            regex: synergyai-backend
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)

  synergyai_rules.yml: |
    groups:
      - name: synergyai.rules
        rules:
          - alert: HighErrorRate
            expr: rate(flask_http_request_exceptions_total[5m]) > 0.1
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: High error rate detected
              description: "Error rate is {{ $value }} errors per second"
          
          - alert: HighResponseTime
            expr: histogram_quantile(0.95, rate(flask_http_request_duration_seconds_bucket[5m])) > 2
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: High response time detected
              description: "95th percentile response time is {{ $value }} seconds"
          
          - alert: AIAPIQuotaExceeded
            expr: openrouter_api_quota_remaining < 1000
            for: 1m
            labels:
              severity: critical
            annotations:
              summary: OpenRouter API quota nearly exceeded
              description: "Remaining quota: {{ $value }} tokens"
```

**Implementation Guidelines**:
- Use infrastructure as code for reproducible deployments
- Implement blue-green or canary deployment strategies
- Create comprehensive monitoring and alerting
- Use secrets management for sensitive configuration
- Implement proper backup and disaster recovery procedures
- Create deployment rollback procedures
- Design for high availability and fault tolerance

**Expected Output**: A complete production deployment configuration that provides scalable, secure, and reliable hosting for the SynergyAI application with proper monitoring, backup, and disaster recovery capabilities.

---


# Implementation Roadmap and Best Practices

## Phase 7: Implementation Strategy

### Prompt 7.1: Development Phases and Timeline

Create a structured implementation approach for building the SynergyAI application:

**Objective**: Provide a clear roadmap for implementing the SynergyAI application using Cursor AI, with realistic timelines, milestones, and success criteria.

**Implementation Phases**:

**Phase 1: Foundation Setup (Week 1-2)**
- Set up development environment using the provided setup scripts
- Create basic project structure with frontend and backend scaffolding
- Implement core database models and basic API endpoints
- Set up authentication and authorization systems
- Create basic frontend components and routing
- Establish CI/CD pipeline and testing framework

**Phase 2: Core AI Integration (Week 3-4)**
- Implement OpenRouter API client and model management
- Create base agent architecture and communication systems
- Develop Project Navigator AI agent with basic functionality
- Implement context management and basic shielding
- Create prompt engineering interface and basic strategies
- Set up AI response processing and validation

**Phase 3: Multi-Agent System (Week 5-6)**
- Implement all specialized AI agents (Prompt Engineer, Summarizer, etc.)
- Create agent orchestration and workflow management
- Develop context injection and optimization systems
- Implement iterative loop functionality
- Create human-in-the-loop controls and governance
- Set up agent performance monitoring and optimization

**Phase 4: Advanced Features (Week 7-8)**
- Implement advanced context management and compression
- Create sophisticated workflow visualization and tracking
- Develop advanced prompt optimization and testing
- Implement cost management and usage analytics
- Create export and sharing functionality
- Set up advanced security and privacy controls

**Phase 5: Testing and Optimization (Week 9-10)**
- Conduct comprehensive testing across all components
- Perform load testing and performance optimization
- Implement security testing and vulnerability assessment
- Create user acceptance testing and feedback collection
- Optimize AI model selection and cost efficiency
- Finalize documentation and user guides

**Phase 6: Deployment and Launch (Week 11-12)**
- Set up production infrastructure and deployment
- Conduct final security and performance audits
- Implement monitoring and alerting systems
- Create backup and disaster recovery procedures
- Launch beta version with limited user group
- Monitor performance and collect user feedback

**Success Criteria for Each Phase**:
- All tests passing with minimum 80% code coverage
- Performance benchmarks meeting specified requirements
- Security audits passing without critical vulnerabilities
- User acceptance criteria met for implemented features
- Documentation complete and up-to-date
- Deployment procedures tested and validated

### Prompt 7.2: Quality Assurance and Best Practices

Implement comprehensive quality assurance measures throughout development:

**Objective**: Ensure high-quality code, robust functionality, and excellent user experience through systematic quality assurance practices.

**Code Quality Standards**:

1. **Frontend Code Quality**:
   - Use ESLint and Prettier for consistent code formatting
   - Implement TypeScript for type safety and better development experience
   - Follow React best practices and component design patterns
   - Use semantic HTML and accessibility best practices
   - Implement responsive design and cross-browser compatibility
   - Create reusable components and maintain design system consistency

2. **Backend Code Quality**:
   - Follow PEP 8 style guidelines for Python code
   - Use type hints and docstrings for better code documentation
   - Implement proper error handling and logging
   - Follow REST API design principles and conventions
   - Use database best practices and query optimization
   - Implement proper security measures and input validation

3. **AI Integration Quality**:
   - Implement proper error handling for AI API failures
   - Create fallback mechanisms for service unavailability
   - Use appropriate AI models for specific tasks
   - Implement cost optimization and usage monitoring
   - Create comprehensive testing for AI interactions
   - Document AI model capabilities and limitations

**Testing Standards**:

1. **Unit Testing Requirements**:
   - Minimum 80% code coverage for all components
   - Test all public methods and API endpoints
   - Mock external dependencies and AI services
   - Test error conditions and edge cases
   - Use descriptive test names and clear assertions
   - Maintain fast test execution times

2. **Integration Testing Requirements**:
   - Test API endpoint interactions and data flow
   - Verify database operations and data integrity
   - Test AI agent coordination and communication
   - Validate authentication and authorization flows
   - Test file upload and export functionality
   - Verify email and notification systems

3. **End-to-End Testing Requirements**:
   - Test complete user workflows from start to finish
   - Verify multi-agent coordination scenarios
   - Test error recovery and fallback mechanisms
   - Validate performance under realistic load conditions
   - Test across different browsers and devices
   - Verify accessibility and usability requirements

**Performance Standards**:

1. **Frontend Performance**:
   - Page load times under 3 seconds on 3G connections
   - First contentful paint under 1.5 seconds
   - Interactive elements responsive within 100ms
   - Bundle sizes optimized and code splitting implemented
   - Images optimized and lazy loading implemented
   - Accessibility score of 95+ on Lighthouse audits

2. **Backend Performance**:
   - API response times under 500ms for 95% of requests
   - Database queries optimized with proper indexing
   - Concurrent user support for 1000+ simultaneous users
   - Memory usage optimized and leak-free
   - CPU usage efficient under normal load
   - Proper caching implemented for frequently accessed data

3. **AI Integration Performance**:
   - AI response times optimized based on model capabilities
   - Context processing efficient and scalable
   - Cost per interaction optimized and monitored
   - Rate limiting implemented to prevent quota exhaustion
   - Caching implemented for repeated similar requests
   - Fallback mechanisms for performance degradation

### Prompt 7.3: Security and Privacy Implementation

Implement comprehensive security and privacy measures:

**Objective**: Ensure robust security and privacy protection for user data, AI interactions, and system operations.

**Security Implementation Checklist**:

1. **Authentication and Authorization**:
   - Implement secure password hashing with bcrypt or Argon2
   - Use JWT tokens with proper expiration and refresh mechanisms
   - Implement multi-factor authentication for enhanced security
   - Create role-based access control with granular permissions
   - Implement session management with secure cookie handling
   - Set up account lockout and brute force protection

2. **Data Protection**:
   - Encrypt sensitive data at rest using AES-256 encryption
   - Implement TLS 1.3 for all data transmission
   - Use secure key management and rotation procedures
   - Implement data anonymization and pseudonymization
   - Create secure data deletion and cleanup procedures
   - Set up audit logging for all data access and modifications

3. **API Security**:
   - Implement rate limiting and throttling for all endpoints
   - Use input validation and sanitization for all user inputs
   - Implement CORS policies for cross-origin requests
   - Set up API versioning and deprecation strategies
   - Use secure error handling without information leakage
   - Implement request/response logging for security monitoring

4. **AI Security**:
   - Implement prompt injection protection and validation
   - Create content filtering and safety checks for AI responses
   - Set up usage monitoring and anomaly detection
   - Implement cost controls and budget limits
   - Create audit trails for all AI interactions
   - Set up secure API key management and rotation

**Privacy Implementation Requirements**:

1. **Data Minimization**:
   - Collect only necessary data for application functionality
   - Implement data retention policies and automatic cleanup
   - Create user consent management for data collection
   - Provide clear privacy policies and data usage explanations
   - Implement user data export and deletion capabilities
   - Set up data processing transparency and user notifications

2. **GDPR Compliance**:
   - Implement right to access user data
   - Create right to rectification for data corrections
   - Set up right to erasure (right to be forgotten)
   - Implement right to data portability
   - Create consent management and withdrawal mechanisms
   - Set up data protection impact assessments

3. **Context Privacy**:
   - Implement configurable context shielding mechanisms
   - Create privacy controls for sensitive information sharing
   - Set up context access permissions and restrictions
   - Implement context anonymization and redaction
   - Create context audit trails and compliance features
   - Design context sharing and collaboration controls

### Prompt 7.4: Maintenance and Support Strategy

Create comprehensive maintenance and support procedures:

**Objective**: Establish sustainable maintenance practices and user support systems for long-term application success.

**Maintenance Procedures**:

1. **Regular Maintenance Tasks**:
   - Database optimization and index maintenance
   - Log rotation and cleanup procedures
   - Security patch management and updates
   - Performance monitoring and optimization
   - Backup verification and disaster recovery testing
   - Documentation updates and maintenance

2. **Monitoring and Alerting**:
   - Set up comprehensive application monitoring
   - Create performance metrics and alerting thresholds
   - Implement error tracking and notification systems
   - Monitor AI usage and cost optimization
   - Set up security monitoring and threat detection
   - Create uptime monitoring and availability tracking

3. **Update and Upgrade Procedures**:
   - Create systematic update testing and validation
   - Implement blue-green deployment for zero-downtime updates
   - Set up database migration and rollback procedures
   - Create feature flag management for gradual rollouts
   - Implement automated security scanning and updates
   - Design backward compatibility and API versioning

**User Support Systems**:

1. **Documentation and Help Systems**:
   - Create comprehensive user documentation and guides
   - Implement in-app help and tutorial systems
   - Set up FAQ and knowledge base systems
   - Create video tutorials and training materials
   - Implement contextual help and tooltips
   - Design user onboarding and training programs

2. **Support Channels**:
   - Set up email support with ticketing system
   - Create community forums and user groups
   - Implement live chat support for critical issues
   - Set up feedback collection and feature request systems
   - Create bug reporting and issue tracking
   - Design user feedback integration and prioritization

3. **Performance Analytics**:
   - Implement user behavior tracking and analysis
   - Create usage analytics and reporting dashboards
   - Set up A/B testing for feature optimization
   - Monitor user satisfaction and engagement metrics
   - Track feature adoption and usage patterns
   - Create data-driven improvement recommendations

---

# Conclusion and Final Recommendations

## Summary

This comprehensive set of Cursor AI prompts provides a complete roadmap for building the SynergyAI application, a sophisticated multi-agent AI workflow system. The prompts are organized into logical phases that cover every aspect of development, from initial setup to production deployment.

## Key Features Implemented

The SynergyAI application, when built using these prompts, will include:

1. **Multi-Agent AI System**: Specialized AI agents for project navigation, prompt engineering, execution, summarization, and iteration management
2. **OpenRouter Integration**: Seamless integration with multiple AI models through the OpenRouter API
3. **Context Management**: Sophisticated context handling with shielding, compression, and optimization
4. **Iterative Workflows**: Intelligent workflow loops with human-in-the-loop controls
5. **Comprehensive Security**: Robust authentication, authorization, and data protection
6. **Scalable Architecture**: Production-ready deployment with monitoring and optimization
7. **User-Friendly Interface**: Responsive, accessible frontend with real-time updates

## Implementation Best Practices

When using these prompts with Cursor AI:

1. **Sequential Implementation**: Follow the prompts in order, as each phase builds upon previous work
2. **Iterative Development**: Use the testing prompts throughout development, not just at the end
3. **Security First**: Implement security measures from the beginning, not as an afterthought
4. **Performance Optimization**: Monitor and optimize performance throughout development
5. **Documentation**: Maintain comprehensive documentation as you build
6. **User Feedback**: Collect and integrate user feedback early and often

## Technical Considerations

1. **AI Model Selection**: Choose appropriate AI models based on task complexity and cost considerations
2. **Context Optimization**: Implement efficient context management to minimize API costs
3. **Scalability Planning**: Design for growth from the beginning with proper architecture
4. **Error Handling**: Implement comprehensive error handling and recovery mechanisms
5. **Monitoring**: Set up monitoring and alerting from day one
6. **Backup and Recovery**: Implement robust backup and disaster recovery procedures

## Future Enhancements

Consider these potential enhancements for future versions:

1. **Advanced AI Capabilities**: Integration with newer AI models and capabilities
2. **Enhanced Collaboration**: Multi-user collaboration and team management features
3. **Advanced Analytics**: Deeper insights and analytics for workflow optimization
4. **Mobile Applications**: Native mobile apps for on-the-go access
5. **API Ecosystem**: Public APIs for third-party integrations
6. **Enterprise Features**: Advanced enterprise security and compliance features

## Support and Resources

For successful implementation:

1. **Community**: Engage with the development community for support and best practices
2. **Documentation**: Maintain up-to-date documentation for all components
3. **Training**: Provide comprehensive training for users and administrators
4. **Feedback Loops**: Establish feedback mechanisms for continuous improvement
5. **Monitoring**: Implement comprehensive monitoring and alerting systems
6. **Updates**: Stay current with security updates and feature enhancements

## Final Notes

The SynergyAI application represents a sophisticated approach to AI-powered workflow management. By following these comprehensive prompts and implementing the recommended best practices, you will create a robust, scalable, and user-friendly application that harnesses the power of multiple AI agents to help users achieve complex project goals through iterative, intelligent workflows.

Remember that building such a comprehensive application is a significant undertaking that requires careful planning, systematic implementation, and ongoing maintenance. The prompts provided here give you a solid foundation, but success will depend on careful execution, thorough testing, and continuous improvement based on user feedback and changing requirements.

Good luck with your SynergyAI implementation!

---

**Document Information**
- **Title**: SynergyAI App Development Prompts for Cursor AI
- **Version**: 1.0
- **Created**: 2025
- **Author**: Manus AI
- **Purpose**: Comprehensive development guide for building the SynergyAI multi-agent workflow application

---


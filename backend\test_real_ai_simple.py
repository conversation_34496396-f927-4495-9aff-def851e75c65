#!/usr/bin/env python3
"""
Simple real AI test for SynergyAI - Direct testing without complex imports
"""
import os
import requests
import json
import time


def test_openrouter_direct():
    """Test OpenRouter API directly"""
    print("🚀 Testing OpenRouter API Directly")
    print("=" * 50)
    
    # Get API key from environment
    api_key = os.getenv("OPENROUTER_API_KEY")
    if not api_key:
        print("❌ No OPENROUTER_API_KEY found in environment")
        return False
    
    print(f"✅ API Key found: ***{api_key[-10:]}")
    
    # Test API call
    url = "https://openrouter.ai/api/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "HTTP-Referer": "https://synergyai.app",
        "X-Title": "SynergyAI"
    }
    
    payload = {
        "model": "openrouter/cypher-alpha:free",
        "messages": [
            {
                "role": "user", 
                "content": "Hello! Please respond with a brief greeting and confirm you're the cypher-alpha model working correctly."
            }
        ],
        "max_tokens": 100,
        "temperature": 0.7
    }
    
    print("\n📡 Making API request...")
    try:
        start_time = time.time()
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        end_time = time.time()
        
        print(f"✅ Response received in {end_time - start_time:.2f} seconds")
        print(f"   - Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   - Model: {data.get('model', 'Unknown')}")
            
            if "choices" in data and len(data["choices"]) > 0:
                content = data["choices"][0]["message"]["content"]
                print(f"   - Response: {content}")
                
            if "usage" in data:
                usage = data["usage"]
                print(f"   - Tokens: {usage.get('total_tokens', 'Unknown')}")
                
            return True
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"   - Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False


def test_project_analysis():
    """Test AI for project analysis"""
    print("\n🤖 Testing AI Project Analysis")
    print("=" * 50)
    
    api_key = os.getenv("OPENROUTER_API_KEY")
    if not api_key:
        print("❌ No API key available")
        return False
    
    url = "https://openrouter.ai/api/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "HTTP-Referer": "https://synergyai.app",
        "X-Title": "SynergyAI"
    }
    
    project_prompt = """
    Analyze this project and provide a brief summary:
    
    Project: SynergyAI Testing Implementation
    Description: A comprehensive AI-powered project management system with multiple specialized agents
    Goals:
    - Implement OpenRouter API integration
    - Create multi-agent workflows
    - Provide intelligent project analysis
    - Enable iterative AI-driven development
    
    Please provide a concise analysis of this project's scope, potential challenges, and recommendations.
    """
    
    payload = {
        "model": "openrouter/cypher-alpha:free",
        "messages": [{"role": "user", "content": project_prompt}],
        "max_tokens": 200,
        "temperature": 0.7
    }
    
    print("📋 Analyzing project with AI...")
    try:
        start_time = time.time()
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        end_time = time.time()
        
        if response.status_code == 200:
            data = response.json()
            content = data["choices"][0]["message"]["content"]
            
            print(f"✅ Analysis completed in {end_time - start_time:.2f} seconds")
            print(f"\n📊 AI Project Analysis:")
            print("-" * 40)
            print(content)
            print("-" * 40)
            
            return True
        else:
            print(f"❌ Analysis failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Analysis error: {e}")
        return False


def test_streaming():
    """Test streaming response"""
    print("\n🌊 Testing Streaming Response")
    print("=" * 50)
    
    api_key = os.getenv("OPENROUTER_API_KEY")
    if not api_key:
        print("❌ No API key available")
        return False
    
    url = "https://openrouter.ai/api/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "HTTP-Referer": "https://synergyai.app",
        "X-Title": "SynergyAI"
    }
    
    payload = {
        "model": "openrouter/cypher-alpha:free",
        "messages": [
            {
                "role": "user", 
                "content": "Write a short paragraph about the benefits of AI in software testing."
            }
        ],
        "max_tokens": 150,
        "temperature": 0.7,
        "stream": True
    }
    
    print("📡 Starting streaming request...")
    try:
        response = requests.post(url, headers=headers, json=payload, stream=True, timeout=30)
        
        if response.status_code == 200:
            print("✅ Stream started, receiving content:")
            print("   ", end="", flush=True)
            
            content_parts = []
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        data_str = line_str[6:]
                        if data_str.strip() == '[DONE]':
                            break
                        try:
                            data = json.loads(data_str)
                            if 'choices' in data and len(data['choices']) > 0:
                                delta = data['choices'][0].get('delta', {})
                                content = delta.get('content', '')
                                if content:
                                    print(content, end="", flush=True)
                                    content_parts.append(content)
                        except json.JSONDecodeError:
                            continue
            
            print(f"\n\n✅ Streaming completed!")
            print(f"   - Total content: {len(''.join(content_parts))} characters")
            return True
        else:
            print(f"❌ Streaming failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Streaming error: {e}")
        return False


def main():
    """Run all real AI tests"""
    print("🚀 SynergyAI Real AI Functionality Test")
    print("Testing OpenRouter cypher-alpha:free model")
    print("=" * 60)
    
    tests = [
        ("Direct API Test", test_openrouter_direct),
        ("Project Analysis", test_project_analysis),
        ("Streaming Response", test_streaming),
    ]
    
    results = []
    start_time = time.time()
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {e}")
            results.append((test_name, False))
    
    end_time = time.time()
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 REAL AI TEST RESULTS")
    print(f"{'='*60}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name:<20} {status}")
    
    print(f"\n📈 Results: {passed}/{total} tests passed")
    print(f"⏱️  Total time: {end_time - start_time:.2f} seconds")
    
    if passed == total:
        print("\n🎉 ALL REAL AI TESTS PASSED!")
        print("✅ SynergyAI is working perfectly with OpenRouter!")
        print("✅ cypher-alpha:free model is responding correctly!")
        print("✅ Ready for production AI workflows!")
    else:
        print("\n⚠️  Some tests failed - check API key and connectivity")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
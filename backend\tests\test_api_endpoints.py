# backend/tests/test_api_endpoints.py
import pytest
import json
from unittest.mock import patch, AsyncMock
from backend.app import create_app, db
from backend.models.models import User, Project
from backend.config import TestingConfig


class TestProjectsAPI:
    """Test suite for Projects API endpoints"""
    
    @pytest.fixture
    def app(self):
        """Create test application"""
        app = create_app(TestingConfig)
        with app.app_context():
            db.create_all()
            yield app
            db.drop_all()
    
    @pytest.fixture
    def client(self, app):
        """Create test client"""
        return app.test_client()
    
    @pytest.fixture
    def sample_user(self, app):
        """Create a sample user for testing"""
        with app.app_context():
            user = User(
                username="testuser",
                email="<EMAIL>",
                password_hash="hashed_password"
            )
            db.session.add(user)
            db.session.commit()
            return user
    
    @pytest.fixture
    def sample_project(self, app, sample_user):
        """Create a sample project for testing"""
        with app.app_context():
            # Create a fresh user in this context
            user = User(
                username="testuser2",
                email="<EMAIL>", 
                password_hash="hashed_password"
            )
            db.session.add(user)
            db.session.commit()
            
            project = Project(
                title="Test Project",
                description="A test project",
                goals=["Goal 1", "Goal 2"],
                user_id=user.id
            )
            db.session.add(project)
            db.session.commit()
            # Store the ID before returning
            project_id = project.id
            db.session.expunge(project)
            project.id = project_id
            return project
    
    def test_get_projects_empty(self, client, app):
        """Test getting projects when none exist"""
        with app.app_context():
            response = client.get('/api/v1/projects/')
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['projects'] == []
    
    def test_get_projects_with_data(self, client, app, sample_project):
        """Test getting projects when data exists"""
        with app.app_context():
            response = client.get('/api/v1/projects/')
            assert response.status_code == 200
            data = json.loads(response.data)
            assert len(data['projects']) == 1
            assert data['projects'][0]['title'] == "Test Project"
    
    def test_create_project_success(self, client, app, sample_user):
        """Test successful project creation"""
        with app.app_context():
            # Create a user in this test context
            user = User(username="newuser", email="<EMAIL>", password_hash="hash")
            db.session.add(user)
            db.session.commit()
            
            project_data = {
                'title': 'New Project',
                'description': 'A new test project',
                'goals': ['New Goal 1', 'New Goal 2'],
                'user_id': user.id
            }
            
            response = client.post(
                '/api/v1/projects/',
                data=json.dumps(project_data),
                content_type='application/json'
            )
            
            assert response.status_code == 201
            data = json.loads(response.data)
            assert data['title'] == 'New Project'
            assert data['description'] == 'A new test project'
    
    def test_create_project_missing_title(self, client, app):
        """Test project creation with missing title"""
        project_data = {
            'description': 'A project without title',
            'user_id': 1
        }
        
        response = client.post(
            '/api/v1/projects/',
            data=json.dumps(project_data),
            content_type='application/json'
        )
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
    
    def test_get_project_by_id(self, client, app, sample_project):
        """Test getting a specific project by ID"""
        with app.app_context():
            response = client.get(f'/api/v1/projects/{sample_project.id}')
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['title'] == sample_project.title
    
    def test_get_project_not_found(self, client, app):
        """Test getting a non-existent project"""
        with app.app_context():
            response = client.get('/api/v1/projects/999')
            assert response.status_code == 404
    
    def test_update_project_success(self, client, app, sample_project):
        """Test successful project update"""
        with app.app_context():
            update_data = {
                'title': 'Updated Project Title',
                'description': 'Updated description'
            }
            
            response = client.put(
                f'/api/v1/projects/{sample_project.id}',
                data=json.dumps(update_data),
                content_type='application/json'
            )
            
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['title'] == 'Updated Project Title'
    
    def test_delete_project_success(self, client, app, sample_project):
        """Test successful project deletion"""
        with app.app_context():
            response = client.delete(f'/api/v1/projects/{sample_project.id}')
            assert response.status_code == 204
            
            # Verify project is deleted
            get_response = client.get(f'/api/v1/projects/{sample_project.id}')
            assert get_response.status_code == 404


class TestAgentsAPI:
    """Test suite for Agents API endpoints"""
    
    @pytest.fixture
    def app(self):
        """Create test application"""
        app = create_app(TestingConfig)
        with app.app_context():
            yield app
    
    @pytest.fixture
    def client(self, app):
        """Create test client"""
        return app.test_client()
    
    def test_get_agents_list(self, client, app):
        """Test getting list of available agents"""
        with app.app_context():
            response = client.get('/api/v1/agents/')
            assert response.status_code == 200
            data = json.loads(response.data)
            assert 'agents' in data
            assert len(data['agents']) > 0
    
    @patch('backend.services.agents.AgentOrchestrator.execute_workflow')
    def test_execute_workflow_success(self, mock_execute, client, app):
        """Test successful workflow execution"""
        with app.app_context():
            # Mock the workflow execution
            mock_execute.return_value = {
                'step1': {'result': 'success', 'output': 'Workflow completed'}
            }
            
            workflow_data = {
                'steps': [
                    {
                        'step_id': 'step1',
                        'agent': 'project_navigator',
                        'task': {
                            'type': 'define_project',
                            'data': {
                                'title': 'Test Project',
                                'description': 'Test Description'
                            }
                        }
                    }
                ]
            }
            
            response = client.post(
                '/api/v1/agents/execute',
                data=json.dumps(workflow_data),
                content_type='application/json'
            )
            
            assert response.status_code == 200
            data = json.loads(response.data)
            assert 'results' in data
            assert 'step1' in data['results']
    
    def test_execute_workflow_invalid_data(self, client, app):
        """Test workflow execution with invalid data"""
        with app.app_context():
            invalid_data = {
                'invalid_field': 'invalid_value'
            }
            
            response = client.post(
                '/api/v1/agents/execute',
                data=json.dumps(invalid_data),
                content_type='application/json'
            )
            
            assert response.status_code == 400
    
    def test_get_agent_status(self, client, app):
        """Test getting agent status"""
        with app.app_context():
            response = client.get('/api/v1/agents/project_navigator/status')
            assert response.status_code == 200
            data = json.loads(response.data)
            assert 'status' in data
            assert 'agent_id' in data
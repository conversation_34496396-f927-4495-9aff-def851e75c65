import { io, Socket } from 'socket.io-client';
import type {
  AgentStatusUpdate,
  WorkflowProgress,
  AIExecutionComplete,
} from '../types/api';

export interface WebSocketEvents {
  agent_status_update: (data: AgentStatusUpdate) => void;
  workflow_progress: (data: WorkflowProgress) => void;
  ai_execution_complete: (data: AIExecutionComplete) => void;
  connect: () => void;
  disconnect: () => void;
  connect_error: (error: Error) => void;
}

export class WebSocketClient {
  private socket: Socket | null = null;
  private url: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second
  private listeners: Map<string, Set<Function>> = new Map();

  constructor(url = 'http://localhost:5000') {
    this.url = url;
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve();
        return;
      }

      this.socket = io(this.url, {
        transports: ['websocket', 'polling'],
        timeout: 10000,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: this.reconnectDelay,
      });

      this.socket.on('connect', () => {
        console.log('Connected to SynergyAI backend');
        this.reconnectAttempts = 0;
        this.reconnectDelay = 1000;
        this.notifyListeners('connect');
        resolve();
      });

      this.socket.on('disconnect', (reason) => {
        console.log('Disconnected from SynergyAI backend:', reason);
        this.notifyListeners('disconnect');
      });

      this.socket.on('connect_error', (error) => {
        console.error('WebSocket connection error:', error);
        this.handleReconnect();
        this.notifyListeners('connect_error', error);
        reject(error);
      });

      // Set up event listeners
      this.setupEventListeners();
    });
  }

  private setupEventListeners(): void {
    if (!this.socket) return;

    this.socket.on('agent_status_update', (data: AgentStatusUpdate) => {
      console.log('Agent status changed:', data);
      this.notifyListeners('agent_status_update', data);
    });

    this.socket.on('workflow_progress', (data: WorkflowProgress) => {
      console.log('Workflow progress:', data);
      this.notifyListeners('workflow_progress', data);
    });

    this.socket.on('ai_execution_complete', (data: AIExecutionComplete) => {
      console.log('AI execution finished:', data);
      this.notifyListeners('ai_execution_complete', data);
    });
  }

  private handleReconnect(): void {
    this.reconnectAttempts++;
    if (this.reconnectAttempts <= this.maxReconnectAttempts) {
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      this.reconnectDelay = Math.min(this.reconnectDelay * 2, 30000); // Max 30 seconds
    }
  }

  private notifyListeners(event: string, data?: any): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error(`Error in WebSocket event listener for ${event}:`, error);
        }
      });
    }
  }

  // Event listener management
  on<K extends keyof WebSocketEvents>(event: K, listener: WebSocketEvents[K]): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(listener);
  }

  off<K extends keyof WebSocketEvents>(event: K, listener: WebSocketEvents[K]): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.delete(listener);
      if (eventListeners.size === 0) {
        this.listeners.delete(event);
      }
    }
  }

  // Convenience methods for specific events
  onAgentStatusUpdate(callback: (data: AgentStatusUpdate) => void): () => void {
    this.on('agent_status_update', callback);
    return () => this.off('agent_status_update', callback);
  }

  onWorkflowProgress(callback: (data: WorkflowProgress) => void): () => void {
    this.on('workflow_progress', callback);
    return () => this.off('workflow_progress', callback);
  }

  onAIExecutionComplete(callback: (data: AIExecutionComplete) => void): () => void {
    this.on('ai_execution_complete', callback);
    return () => this.off('ai_execution_complete', callback);
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.listeners.clear();
  }

  isConnected(): boolean {
    return this.socket?.connected ?? false;
  }

  getConnectionState(): 'connected' | 'disconnected' | 'connecting' | 'error' {
    if (!this.socket) return 'disconnected';
    if (this.socket.connected) return 'connected';
    if (this.socket.disconnected) return 'disconnected';
    return 'connecting';
  }
}

// Default WebSocket client instance
let defaultWebSocketClient: WebSocketClient | null = null;

export const createWebSocketClient = (url?: string): WebSocketClient => {
  const client = new WebSocketClient(url);
  defaultWebSocketClient = client;
  return client;
};

export const getWebSocketClient = (): WebSocketClient => {
  if (!defaultWebSocketClient) {
    console.warn('WebSocket client not initialized, creating default client');
    defaultWebSocketClient = new WebSocketClient();
  }
  return defaultWebSocketClient;
};

# backend/tests/test_openrouter.py
import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from backend.services.openrouter import OpenRouterClient


class TestOpenRouterClient:
    """Test suite for OpenRouter API integration"""
    
    def setup_method(self):
        """Set up test fixtures before each test method"""
        self.api_key = "test-api-key"
        self.client = OpenRouterClient(api_key=self.api_key)
    
    def test_client_initialization(self):
        """Test OpenRouter client initialization"""
        assert self.client.api_key == self.api_key
        assert self.client.base_url == "https://openrouter.ai/api/v1"
        assert "Authorization" in self.client.session.headers
        assert self.client.session.headers["Authorization"] == f"Bearer {self.api_key}"
    
    @patch('backend.services.openrouter.requests.Session.post')
    def test_generate_completion_success(self, mock_post):
        """Test successful AI completion generation"""
        # Mock successful API response
        mock_response = Mock()
        mock_response.json.return_value = {
            "choices": [
                {
                    "message": {
                        "content": "This is a test response from the AI model."
                    },
                    "finish_reason": "stop"
                }
            ],
            "usage": {
                "prompt_tokens": 10,
                "completion_tokens": 15,
                "total_tokens": 25
            }
        }
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        # Test the completion
        result = self.client.generate_completion(
            prompt="Test prompt",
            model="openrouter/cypher-alpha:free",
            max_tokens=100,
            temperature=0.7
        )
        
        # Assertions
        assert "choices" in result
        assert len(result["choices"]) == 1
        assert "usage" in result
        mock_post.assert_called_once()
    
    @patch('backend.services.openrouter.requests.Session.post')
    def test_generate_completion_api_error(self, mock_post):
        """Test API error handling"""
        # Mock API error
        mock_post.side_effect = Exception("API Error")
        
        result = self.client.generate_completion(
            prompt="Test prompt",
            model="openrouter/cypher-alpha:free"
        )
        
        # Should return error response
        assert "error" in result
        assert "API Error" in str(result["error"])
    
    @patch('backend.services.openrouter.requests.Session.post')
    def test_streaming_response(self, mock_post):
        """Test streaming response handling"""
        # Mock streaming response
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.iter_lines.return_value = [
            b'data: {"choices":[{"delta":{"content":"Hello"}}]}',
            b'data: {"choices":[{"delta":{"content":" world"}}]}',
            b'data: [DONE]'
        ]
        mock_post.return_value = mock_response
        
        # Test streaming
        result = self.client.generate_completion(
            prompt="Test prompt",
            model="openrouter/cypher-alpha:free",
            stream=True
        )
        
        # Collect streamed content
        content = list(result)
        assert "Hello" in content
        assert " world" in content
    
    def test_handle_api_error(self):
        """Test error handling method"""
        test_error = Exception("Test error message")
        result = self.client._handle_api_error(test_error)
        
        assert "error" in result
        assert "Test error message" in result["error"]
    
    @patch('backend.services.openrouter.requests.Session.post')
    def test_custom_parameters(self, mock_post):
        """Test custom parameter passing"""
        mock_response = Mock()
        mock_response.json.return_value = {"choices": [{"message": {"content": "test"}}]}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        # Test with custom parameters
        self.client.generate_completion(
            prompt="Test",
            model="openrouter/cypher-alpha:free",
            max_tokens=500,
            temperature=0.9,
            top_p=0.95,
            frequency_penalty=0.1
        )
        
        # Verify the call was made with correct parameters
        call_args = mock_post.call_args
        payload = call_args[1]['json']
        
        assert payload['max_tokens'] == 500
        assert payload['temperature'] == 0.9
        assert payload['top_p'] == 0.95
        assert payload['frequency_penalty'] == 0.1
#!/usr/bin/env python3
"""
Simple Flask app runner without complex imports
"""
import os
from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import asyncio
import sys

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

# Import services directly
from services.openrouter import OpenRouterClient
from services.agents import AgentOrchestrator

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'dev-secret-key'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///synergyai.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db = SQLAlchemy(app)
CORS(app)

# Define models directly here to avoid import issues
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    preferences = db.Column(db.JSON)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    
    projects = db.relationship('Project', backref='owner', lazy='dynamic')

class Project(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    goals = db.Column(db.JSON)
    scope = db.Column(db.JSON)
    status = db.Column(db.String(50), default='active')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

# API Routes
@app.route('/')
def index():
    return jsonify({
        'message': 'SynergyAI Backend API',
        'status': 'running',
        'endpoints': {
            'projects': '/api/v1/projects/',
            'agents': '/api/v1/agents/',
            'execute': '/api/v1/agents/execute'
        }
    })

@app.route('/api/v1/projects/', methods=['GET'])
def get_projects():
    try:
        projects = Project.query.all()
        projects_data = []
        for project in projects:
            projects_data.append({
                'id': project.id,
                'title': project.title,
                'description': project.description,
                'goals': project.goals,
                'scope': project.scope,
                'status': project.status,
                'created_at': project.created_at.isoformat() if project.created_at else None,
                'user_id': project.user_id
            })
        return jsonify({'projects': projects_data})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/v1/projects/', methods=['POST'])
def create_project():
    try:
        data = request.get_json()
        if not data or 'title' not in data:
            return jsonify({'error': 'Title is required'}), 400
        
        # Create a default user if none exists
        user = User.query.first()
        if not user:
            user = User(
                username='default',
                email='<EMAIL>',
                password_hash='default'
            )
            db.session.add(user)
            db.session.commit()
        
        project = Project(
            title=data['title'],
            description=data.get('description'),
            goals=data.get('goals'),
            scope=data.get('scope'),
            user_id=user.id
        )
        
        db.session.add(project)
        db.session.commit()
        
        return jsonify({
            'id': project.id,
            'title': project.title,
            'description': project.description,
            'goals': project.goals,
            'status': project.status,
            'created_at': project.created_at.isoformat()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/v1/agents/', methods=['GET'])
def get_agents():
    try:
        orchestrator = AgentOrchestrator()
        agents_info = []
        for agent_id, agent in orchestrator.agents.items():
            agents_info.append({
                'agent_id': agent.agent_id,
                'name': agent.name,
                'status': agent.status
            })
        return jsonify({'agents': agents_info})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/v1/agents/execute', methods=['POST'])
def execute_workflow():
    try:
        data = request.get_json()
        if not data or 'steps' not in data:
            return jsonify({'error': 'Workflow steps are required'}), 400
        
        orchestrator = AgentOrchestrator()
        
        # Run the async workflow
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            results = loop.run_until_complete(orchestrator.execute_workflow(data))
            loop.close()
        except Exception as async_error:
            return jsonify({'error': f'Workflow execution failed: {str(async_error)}'}), 500
        
        return jsonify({'results': results})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("🚀 Starting SynergyAI Simple Backend Server")
    print("=" * 60)
    
    # Create database tables
    with app.app_context():
        db.create_all()
        print("✅ Database tables created")
    
    print("✅ CORS enabled for frontend development")
    print("✅ Backend API available at: http://localhost:5000")
    print("✅ API endpoints:")
    print("   - GET  /api/v1/projects/")
    print("   - POST /api/v1/projects/")
    print("   - GET  /api/v1/agents/")
    print("   - POST /api/v1/agents/execute")
    print("\n🎯 Ready to receive frontend requests!")
    print("=" * 60)
    
    # Run the server
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        use_reloader=True
    )
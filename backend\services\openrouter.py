# backend/services/openrouter.py
import os
import requests
import json
from typing import Dict, Generator

class OpenRouterClient:
    def __init__(self, api_key: str = None, base_url: str = "https://openrouter.ai/api/v1"):
        self.api_key = api_key or os.getenv("OPENROUTER_API_KEY")
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://synergyai.app", # Replace with your app's URL
            "X-Title": "SynergyAI" # Replace with your app's name
        })

    def generate_completion(
        self,
        prompt: str,
        model: str = "openrouter/cypher-alpha:free",
        max_tokens: int = 1000,
        temperature: float = 0.7,
        stream: bool = False,
        **kwargs
    ) -> Dict:
        payload = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": stream,
            **kwargs
        }
        
        try:
            if stream:
                # Streaming response is handled differently, returning a generator
                return self._handle_streaming_response(payload)
            else:
                return self._handle_standard_response(payload)
        except Exception as e:
            return self._handle_api_error(e)

    def _handle_standard_response(self, payload: Dict) -> Dict:
        response = self.session.post(
            f"{self.base_url}/chat/completions",
            json=payload
        )
        response.raise_for_status()
        return response.json()

    def _handle_streaming_response(self, payload: Dict) -> Generator:
        response = self.session.post(
            f"{self.base_url}/chat/completions",
            json=payload,
            stream=True
        )
        response.raise_for_status()
        for line in response.iter_lines():
            if line:
                try:
                    decoded_line = line.decode('utf-8')
                    if decoded_line.startswith('data: '):
                        data_str = decoded_line[6:]
                        if data_str.strip() == '[DONE]':
                            break
                        data = json.loads(data_str)
                        if data.get('choices'):
                            yield data['choices'][0].get('delta', {}).get('content', '')
                except json.JSONDecodeError:
                    continue

    def _handle_api_error(self, e: Exception) -> Dict:
        # In a real app, you'd have more robust logging and error handling
        print(f"An API error occurred: {e}")
        return {"error": str(e)}

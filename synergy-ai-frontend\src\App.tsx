import { useEffect, useState } from 'react';
import { createClient } from './lib/api-client';
import { createWebSocketClient } from './lib/websocket-client';
import { config, validateConfig } from './lib/config';
import { useSynergyAI } from './hooks/use-synergy-ai';
import { TestComponent } from './components/TestComponent';
import { ConnectionTest } from './components/ConnectionTest';

function App() {
  const [isInitialized, setIsInitialized] = useState(false);
  const { initializeConnections, loading, error, apiConnected, websocketConnected } = useSynergyAI();

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Validate configuration
        const validation = validateConfig();
        if (!validation.valid) {
          console.error('Configuration validation failed:', validation.errors);
          setIsInitialized(true); // Still show the UI
          return;
        }

        // Initialize clients first, before the hook tries to use them
        createClient({
          token: config.auth.token,
          baseURL: config.api.baseURL,
        });

        createWebSocketClient(config.websocket.url);

        // Small delay to ensure clients are ready
        await new Promise(resolve => setTimeout(resolve, 100));

        // Try to initialize connections, but don't block the UI
        try {
          await initializeConnections();
        } catch (error) {
          console.warn('Failed to connect to backend, running in offline mode:', error);
        }
      } catch (error) {
        console.error('App initialization error:', error);
      } finally {
        setIsInitialized(true);
      }
    };

    initializeApp();
  }, []);

  if (!isInitialized || loading.isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{loading.message || 'Loading SynergyAI...'}</p>
        </div>
      </div>
    );
  }

  // Show error as a banner instead of blocking the entire UI
  const showErrorBanner = error.hasError;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Error Banner */}
      {showErrorBanner && (
        <div className="bg-red-100 border-b border-red-200 px-6 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-4 h-4 bg-red-500 rounded-full mr-3"></div>
              <span className="text-red-800 text-sm">
                Backend connection failed: {error.message}
              </span>
            </div>
            <button
              onClick={() => window.location.reload()}
              className="text-red-600 hover:text-red-800 text-sm underline"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold text-gray-900">SynergyAI</h1>
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${apiConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-sm text-gray-600">
                API {apiConnected ? 'Connected' : 'Disconnected'}
              </span>
              <div className={`w-2 h-2 rounded-full ${websocketConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-sm text-gray-600">
                WebSocket {websocketConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className={`flex ${showErrorBanner ? 'h-[calc(100vh-140px)]' : 'h-[calc(100vh-80px)]'}`}>
        {/* Three-panel layout will be implemented here */}
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Welcome to SynergyAI
            </h2>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              Your intelligent project workflow system is ready. The three-panel interface will be implemented in the next steps.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-4xl mx-auto mb-8">
              <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6 text-center">
                <h3 className="font-semibold text-gray-900 mb-2">Project Navigator</h3>
                <p className="text-sm text-gray-600">The "Why" - Manage your projects and goals</p>
              </div>
              <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6 text-center">
                <h3 className="font-semibold text-gray-900 mb-2">Workflow Log</h3>
                <p className="text-sm text-gray-600">The "What/How" - Track AI agent execution</p>
              </div>
              <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6 text-center">
                <h3 className="font-semibold text-gray-900 mb-2">Context & Controls</h3>
                <p className="text-sm text-gray-600">The "Tools" - AI strategy and controls</p>
              </div>
            </div>

            {/* Connection Test */}
            <div className="max-w-md mx-auto mb-6">
              <ConnectionTest />
            </div>

            {/* Test Component */}
            <div className="mt-8 max-w-md mx-auto">
              <TestComponent />
            </div>

            {/* Status Information */}
            <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200 max-w-md mx-auto">
              <h4 className="font-medium text-blue-900 mb-2">Connection Status</h4>
              <div className="text-sm text-blue-700 space-y-1">
                <div>✅ Frontend: Running</div>
                <div>✅ TypeScript: Configured</div>
                <div>✅ TailwindCSS: Active</div>
                <div>✅ State Management: Ready</div>
                <div className={apiConnected ? 'text-green-700' : 'text-orange-700'}>
                  {apiConnected ? '✅' : '⚠️'} Backend API: {apiConnected ? 'Connected' : 'Offline'}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

export default App;

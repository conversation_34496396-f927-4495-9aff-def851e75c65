Here are the prompts, designed to be used sequentially. Each prompt represents a logical step in the development process.


> **Initial Context Prompt:**
>
> "I am building a React frontend for an application called **SynergyAI**. The tech stack will be **React with TypeScript**, using **Vite** for the build tool and **TailwindCSS** for styling. We will also use `axios` for API calls and `socket.io-client` for real-time updates.
>
> The UI design follows a minimalist, three-panel layout:
> 1.  **Left Panel:** Project Navigator (the "Why").
> 2.  **Center Panel:** Main Workflow Log (the "What/How").
> 3.  **Right Panel:** Context & Controls (the "Tools").
>
> I am providing the complete backend API documentation. Please use this document as the single source of truth for all API endpoints, data models, and WebSocket events.
>
> **[Now, paste the entire content of the `backend_api_documentation.md` file here]**
>
> My goal is to build out the frontend step-by-step. I will provide you with a series of prompts to generate the necessary code."

After <PERSON><PERSON><PERSON> has processed this initial context, you can proceed with the following prompts.

---

### **Prompt 1: Project Setup & Foundation**

> "Let's start by setting up the project structure and core utilities.
>
> 1.  Generate the command to create a new **Vite** project named `synergy-ai-frontend` using the `react-ts` template.
> 2.  Provide the commands to install **TailwindCSS**, `axios`, `socket.io-client`, `zustand` (for state management), and `lucide-react` (for icons).
> 3.  Generate the initial `tailwind.config.js` and `postcss.config.js` files.
> 4.  Update `index.css` with the standard Tailwind directives.
> 5.  Create a directory structure with `src/components`, `src/lib`, `src/hooks`, and `src/store`."

---

### **Prompt 2: API Client and TypeScript Definitions**

> "Based on the API documentation I provided, create the foundational code for interacting with the backend.
>
> 1.  In `src/lib/api.ts`, create an `axios` API client instance. It should be pre-configured with the `baseURL` (`http://localhost:5000/api/v1`) and the required `Authorization` header. Use a placeholder 'my-secret-token' for now, but pull it from an environment variable (`VITE_API_TOKEN`). Also, include the error handling logic from the documentation's `SynergyAIClient` example.
> 2.  In `src/lib/types.ts`, create TypeScript interfaces for all the data models specified in the API documentation: `Project`, `Agent`, `WorkflowStep`, `AIStrategy`, and `ContextItem`."

---

### **Prompt 3: Global State Management (Zustand & WebSockets)**

> "Now, set up the global state management using Zustand and integrate the WebSocket client.
>
> 1.  In `src/store/workflow-store.ts`, create a Zustand store. The store should manage the following state:
>     -   `activeProject: Project | null`
>     -   `workflowLog: any[]` (we'll use `any` for now to accommodate different log item types)
>     -   `contextLibrary: ContextItem[]`
>     -   `agentStatus: Record<string, Agent['status']>`
>     -   `isLoading: boolean`
> 2.  Add actions to the store for:
>     -   `fetchProject(projectId: number)`: To get a project and set it as active.
>     -   `addToWorkflowLog(item: any)`: To append a new event/card to the central log.
>     -   `updateAgentStatus(agentId: string, status: Agent['status'])`.
> 3.  In `src/hooks/use-socket-manager.ts`, create a custom hook that initializes a `socket.io-client` connection. This hook should:
>     -   Connect to the WebSocket server.
>     -   Listen for the `agent_status_update`, `workflow_progress`, and `ai_execution_complete` events.
>     -   When an event is received, it should call the appropriate action from the Zustand store to update the global state (e.g., on `ai_execution_complete`, it calls `addToWorkflowLog` with the result)."

---

### **Prompt 4: Core UI Layout Components**

> "Let's build the main three-panel application layout.
>
> 1.  In `App.tsx`, create the main container using Flexbox or CSS Grid to establish the three-panel layout.
> 2.  Create three new stub components:
>     -   `src/components/panels/ProjectNavigatorPanel.tsx` (Left Panel)
>     -   `src/components/panels/MainWorkflowPanel.tsx` (Center Panel, should take up the most space)
>     -   `src/components/panels/ControlsPanel.tsx` (Right Panel)
> 3.  Import and render these three components inside `App.tsx` in the correct layout order. Ensure `App.tsx` also calls the `useSocketManager` hook once to establish the connection."

---

### **Prompt 5: Building the Left Panel (Project Navigator)**

> "Flesh out the `ProjectNavigatorPanel.tsx` component.
>
> 1.  It should use the `useAppStore` hook to get the `activeProject` from the Zustand store.
> 2.  If `activeProject` is null, display a loading message or a 'Select a project' prompt.
> 3.  If a project is active, render its details:
>     -   `🎯 Goals` (Short-term and Long-term)
>     -   `📍 Scope & Constraints`
>     -   `🏁 Milestones`
> 4.  Use `lucide-react` icons for the headers.
> 5.  At the bottom, add a text input field styled as a chat prompt with the placeholder `[ Chat with Navigator to refine goals... ]`. For now, it doesn't need to be functional."

---

### **Prompt 6: Building the Center Panel (Workflow Log)**

> "This is the most dynamic component. Let's build `MainWorkflowPanel.tsx`.
>
> 1.  It should get the `workflowLog` array from the Zustand store.
> 2.  Map over the `workflowLog` array. For each item, you'll need to render a different "card" component based on an `item.type` property.
> 3.  Create a new directory `src/components/cards`.
> 4.  Inside this directory, create stub components for the different workflow events:
>     -   `PromptApprovalCard.tsx`: This is an 'ACTION REQUIRED' card. It should display the prompt, context, and model, along with 'Approve', 'Revise', and 'Discard' buttons.
>     -   `ExecutionStatusCard.tsx`: A simple card showing an icon and 'Executing...' text, perhaps with a subtle pulse animation.
>     -   `SummaryCard.tsx`: To display the summary and tags from the `Summarizer AI`. Should include 'View Full Output' and 'Add to Context' links.
>     -   `IterationSuggestionCard.tsx`: To show the `Project Navigator AI`'s suggestions with 'Proceed' and 'Discuss' buttons.
> 5.  In `MainWorkflowPanel.tsx`, use a switch statement or a component map on `item.type` to render the correct card component for each item in the log, passing the item data as props."

---

### **Prompt 7: Implementing the "Approve Prompt" Interactive Loop**

> "Now let's make the workflow interactive. We will implement the user action of approving a prompt.
>
> 1.  In `PromptApprovalCard.tsx`, make the '✅ Approve' button functional.
> 2.  The `onClick` handler for this button should:
>     -   Get the `executeWorkflow` function from your API client in `src/lib/api.ts`.
>     -   Call `executeWorkflow` with the required payload (you can construct this from the card's props, which hold the prompt strategy data).
>     -   While waiting for the API call, it should call the `addToWorkflowLog` action from the Zustand store to add a new item of `{ type: 'execution_status' }` to the log. This will automatically show the `ExecutionStatusCard`.
>
> The beauty of this setup is that once the execution is complete, the `useSocketManager` hook will receive the `ai_execution_complete` event and automatically add the `SummaryCard` to the log, completing the loop."

---

### **Prompt 8: Building the Right Panel (Context & Controls)**

> "Finally, let's build the `ControlsPanel.tsx`.
>
> 1.  The panel should have a main header `🧰 Context & Controls`.
> 2.  Create a `📚 Context Library` section. It should get the `contextLibrary` from the Zustand store and render a list of items. Each item should have an on/off toggle (you can use a Shadcn/ui Switch or a simple checkbox).
> 3.  Add an `[📥 Import Context]` button.
> 4.  Create a `🏷️ Tag Cloud` section. For now, just display a few hardcoded tag-like badges.
> 5.  Create a `🎛️ Governance Overrides` section with toggles for 'Shield Ultimate Goal' and 'Require Prompt Approval'.
> 6.  Implement a state within this component to manage whether it's collapsed or expanded, with a button to trigger the change."
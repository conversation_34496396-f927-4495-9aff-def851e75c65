# backend/tests/test_agents.py
import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from backend.services.agents import BaseAgent, ProjectNavigatorAgent, AgentOrchestrator


class TestBaseAgent:
    """Test suite for BaseAgent abstract class"""
    
    def test_base_agent_initialization(self):
        """Test BaseAgent initialization"""
        # Create a concrete implementation for testing
        class TestAgent(BaseAgent):
            async def process_task(self, task):
                return {"result": "test"}
        
        agent = TestAgent("test_id", "Test Agent")
        assert agent.agent_id == "test_id"
        assert agent.name == "Test Agent"
        assert agent.status == "idle"
        assert agent.ai_client is not None


class TestProjectNavigatorAgent:
    """Test suite for ProjectNavigatorAgent"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.agent = ProjectNavigatorAgent()
    
    def test_agent_initialization(self):
        """Test ProjectNavigatorAgent initialization"""
        assert self.agent.agent_id == "project_navigator"
        assert self.agent.name == "Project Navigator AI"
        assert self.agent.status == "idle"
    
    @pytest.mark.asyncio
    async def test_process_task_define_project(self):
        """Test project definition task processing"""
        # Mock the AI client response
        with patch.object(self.agent.ai_client, 'generate_completion') as mock_completion:
            mock_completion.return_value = {
                "choices": [{"text": "Generated project summary"}]
            }
            
            task = {
                "type": "define_project",
                "data": {
                    "title": "Test Project",
                    "description": "A test project description",
                    "goals": ["Goal 1", "Goal 2"]
                }
            }
            
            result = await self.agent.process_task(task)
            
            assert "summary" in result
            assert "Generated project summary" in result["summary"]
            mock_completion.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_task_unknown_type(self):
        """Test handling of unknown task types"""
        task = {
            "type": "unknown_task",
            "data": {}
        }
        
        with pytest.raises(ValueError, match="Unknown task type"):
            await self.agent.process_task(task)
    
    def test_create_project_definition_prompt(self):
        """Test prompt creation for project definition"""
        project_data = {
            "title": "Test Project",
            "description": "Test Description",
            "goals": ["Goal 1", "Goal 2"]
        }
        
        prompt = self.agent._create_project_definition_prompt(project_data)
        
        assert "Test Project" in prompt
        assert "Test Description" in prompt
        assert "Goal 1" in prompt
    
    @pytest.mark.asyncio
    async def test_define_project_with_ai_error(self):
        """Test project definition when AI client returns error"""
        with patch.object(self.agent.ai_client, 'generate_completion') as mock_completion:
            mock_completion.return_value = {"error": "API Error"}
            
            task = {
                "type": "define_project",
                "data": {
                    "title": "Test Project",
                    "description": "Test Description",
                    "goals": []
                }
            }
            
            result = await self.agent.process_task(task)
            
            # Should handle gracefully and return default message
            assert "summary" in result
            assert "Could not generate summary" in result["summary"]


class TestAgentOrchestrator:
    """Test suite for AgentOrchestrator"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.orchestrator = AgentOrchestrator()
    
    def test_orchestrator_initialization(self):
        """Test AgentOrchestrator initialization"""
        assert "project_navigator" in self.orchestrator.agents
        assert isinstance(self.orchestrator.agents["project_navigator"], ProjectNavigatorAgent)
    
    @pytest.mark.asyncio
    async def test_execute_workflow_single_step(self):
        """Test workflow execution with single step"""
        # Mock the agent's process_task method
        mock_agent = AsyncMock()
        mock_agent.process_task.return_value = {"result": "success"}
        self.orchestrator.agents["test_agent"] = mock_agent
        
        workflow_config = {
            "steps": [
                {
                    "step_id": "step1",
                    "agent": "test_agent",
                    "task": {"type": "test_task", "data": {}}
                }
            ]
        }
        
        results = await self.orchestrator.execute_workflow(workflow_config)
        
        assert "step1" in results
        assert results["step1"]["result"] == "success"
        mock_agent.process_task.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_execute_workflow_multiple_steps(self):
        """Test workflow execution with multiple steps"""
        # Mock multiple agents
        mock_agent1 = AsyncMock()
        mock_agent1.process_task.return_value = {"result": "step1_success"}
        mock_agent2 = AsyncMock()
        mock_agent2.process_task.return_value = {"result": "step2_success"}
        
        self.orchestrator.agents["agent1"] = mock_agent1
        self.orchestrator.agents["agent2"] = mock_agent2
        
        workflow_config = {
            "steps": [
                {
                    "step_id": "step1",
                    "agent": "agent1",
                    "task": {"type": "task1", "data": {}}
                },
                {
                    "step_id": "step2",
                    "agent": "agent2",
                    "task": {"type": "task2", "data": {}}
                }
            ]
        }
        
        results = await self.orchestrator.execute_workflow(workflow_config)
        
        assert len(results) == 2
        assert results["step1"]["result"] == "step1_success"
        assert results["step2"]["result"] == "step2_success"
    
    @pytest.mark.asyncio
    async def test_execute_workflow_unknown_agent(self):
        """Test workflow execution with unknown agent"""
        workflow_config = {
            "steps": [
                {
                    "step_id": "step1",
                    "agent": "unknown_agent",
                    "task": {"type": "test_task", "data": {}}
                }
            ]
        }
        
        results = await self.orchestrator.execute_workflow(workflow_config)
        
        # Should skip unknown agents
        assert len(results) == 0
    
    @pytest.mark.asyncio
    async def test_execute_workflow_empty_steps(self):
        """Test workflow execution with empty steps"""
        workflow_config = {"steps": []}
        
        results = await self.orchestrator.execute_workflow(workflow_config)
        
        assert len(results) == 0
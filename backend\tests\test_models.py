# backend/tests/test_models.py
import pytest
from datetime import datetime
from backend.app import create_app, db
from backend.models.models import User, Project
from backend.config import TestingConfig


class TestModels:
    """Test suite for database models"""
    
    @pytest.fixture
    def app(self):
        """Create test application"""
        app = create_app(TestingConfig)
        with app.app_context():
            db.create_all()
            yield app
            db.drop_all()
    
    @pytest.fixture
    def client(self, app):
        """Create test client"""
        return app.test_client()
    
    def test_user_model_creation(self, app):
        """Test User model creation and basic properties"""
        with app.app_context():
            user = User(
                username="testuser",
                email="<EMAIL>",
                password_hash="hashed_password",
                preferences={"theme": "dark", "notifications": True}
            )
            
            db.session.add(user)
            db.session.commit()
            
            # Test user properties
            assert user.id is not None
            assert user.username == "testuser"
            assert user.email == "<EMAIL>"
            assert user.password_hash == "hashed_password"
            assert user.preferences["theme"] == "dark"
            assert user.is_active is True
            assert isinstance(user.created_at, datetime)
            assert isinstance(user.updated_at, datetime)
    
    def test_user_model_repr(self, app):
        """Test User model string representation"""
        with app.app_context():
            user = User(username="testuser", email="<EMAIL>", password_hash="hash")
            assert repr(user) == '<User testuser>'
    
    def test_user_unique_constraints(self, app):
        """Test User model unique constraints"""
        with app.app_context():
            # Create first user
            user1 = User(username="testuser", email="<EMAIL>", password_hash="hash1")
            db.session.add(user1)
            db.session.commit()
            
            # Try to create user with same username
            user2 = User(username="testuser", email="<EMAIL>", password_hash="hash2")
            db.session.add(user2)
            
            with pytest.raises(Exception):  # Should raise integrity error
                db.session.commit()
            
            db.session.rollback()
            
            # Try to create user with same email
            user3 = User(username="different", email="<EMAIL>", password_hash="hash3")
            db.session.add(user3)
            
            with pytest.raises(Exception):  # Should raise integrity error
                db.session.commit()
    
    def test_project_model_creation(self, app):
        """Test Project model creation and basic properties"""
        with app.app_context():
            # Create user first
            user = User(username="testuser", email="<EMAIL>", password_hash="hash")
            db.session.add(user)
            db.session.commit()
            
            # Create project
            project = Project(
                title="Test Project",
                description="A test project description",
                goals=["Goal 1", "Goal 2"],
                scope={"features": ["feature1", "feature2"], "timeline": "3 months"},
                status="active",
                user_id=user.id
            )
            
            db.session.add(project)
            db.session.commit()
            
            # Test project properties
            assert project.id is not None
            assert project.title == "Test Project"
            assert project.description == "A test project description"
            assert project.goals == ["Goal 1", "Goal 2"]
            assert project.scope["features"] == ["feature1", "feature2"]
            assert project.status == "active"
            assert project.user_id == user.id
            assert isinstance(project.created_at, datetime)
            assert isinstance(project.updated_at, datetime)
    
    def test_project_model_repr(self, app):
        """Test Project model string representation"""
        with app.app_context():
            user = User(username="testuser", email="<EMAIL>", password_hash="hash")
            db.session.add(user)
            db.session.commit()
            
            project = Project(title="Test Project", user_id=user.id)
            assert repr(project) == '<Project Test Project>'
    
    def test_user_project_relationship(self, app):
        """Test relationship between User and Project models"""
        with app.app_context():
            # Create user
            user = User(username="testuser", email="<EMAIL>", password_hash="hash")
            db.session.add(user)
            db.session.commit()
            
            # Create multiple projects for the user
            project1 = Project(title="Project 1", user_id=user.id)
            project2 = Project(title="Project 2", user_id=user.id)
            
            db.session.add_all([project1, project2])
            db.session.commit()
            
            # Test relationship
            assert len(user.projects.all()) == 2
            assert project1.owner == user
            assert project2.owner == user
            assert project1 in user.projects.all()
            assert project2 in user.projects.all()
    
    def test_project_default_values(self, app):
        """Test Project model default values"""
        with app.app_context():
            user = User(username="testuser", email="<EMAIL>", password_hash="hash")
            db.session.add(user)
            db.session.commit()
            
            project = Project(title="Minimal Project", user_id=user.id)
            db.session.add(project)
            db.session.commit()
            
            # Test defaults
            assert project.status == "active"
            assert project.description is None
            assert project.goals is None
            assert project.scope is None
    
    def test_user_default_values(self, app):
        """Test User model default values"""
        with app.app_context():
            user = User(username="testuser", email="<EMAIL>", password_hash="hash")
            db.session.add(user)
            db.session.commit()
            
            # Test defaults
            assert user.is_active is True
            assert user.preferences is None
            assert user.created_at is not None
            assert user.updated_at is not None
    
    def test_project_json_fields(self, app):
        """Test JSON field handling in Project model"""
        with app.app_context():
            user = User(username="testuser", email="<EMAIL>", password_hash="hash")
            db.session.add(user)
            db.session.commit()
            
            complex_goals = [
                {"type": "short_term", "description": "Complete MVP", "deadline": "2024-03-01"},
                {"type": "long_term", "description": "Scale to 1000 users", "deadline": "2024-12-01"}
            ]
            
            complex_scope = {
                "features": ["auth", "dashboard", "reporting"],
                "constraints": {"budget": 50000, "team_size": 5},
                "assumptions": ["Users have modern browsers", "API availability"]
            }
            
            project = Project(
                title="Complex Project",
                goals=complex_goals,
                scope=complex_scope,
                user_id=user.id
            )
            
            db.session.add(project)
            db.session.commit()
            
            # Retrieve and verify JSON data
            retrieved_project = Project.query.filter_by(title="Complex Project").first()
            assert len(retrieved_project.goals) == 2
            assert retrieved_project.goals[0]["type"] == "short_term"
            assert retrieved_project.scope["constraints"]["budget"] == 50000
# backend/app.py
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from config import Config

db = SQLAlchemy()
migrate = Migrate()

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)

    db.init_app(app)
    migrate.init_app(app, db)

    # Register blueprints here
    from api.projects import projects_bp
    app.register_blueprint(projects_bp, url_prefix='/api/v1/projects')

    from api.agents import agents_bp
    app.register_blueprint(agents_bp, url_prefix='/api/v1/agents')

    @app.route('/')
    def index():
        return 'SynergyAI Backend is running!'

    return app

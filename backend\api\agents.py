# backend/api/agents.py
from flask import Blueprint, request, jsonify
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from services.agents import AgentOrchestrator

agents_bp = Blueprint('agents', __name__)

# Create a global orchestrator instance
orchestrator = AgentOrchestrator()

@agents_bp.route('/', methods=['GET'])
def get_agents():
    """Get list of available agents"""
    try:
        agents_info = []
        for agent_id, agent in orchestrator.agents.items():
            agents_info.append({
                'agent_id': agent.agent_id,
                'name': agent.name,
                'status': agent.status
            })
        
        return jsonify({'agents': agents_info}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agents_bp.route('/execute', methods=['POST'])
def execute_workflow():
    """Execute a workflow with multiple agents"""
    try:
        data = request.get_json()
        
        if not data or 'steps' not in data:
            return jsonify({'error': 'Workflow steps are required'}), 400
        
        # Run the async workflow in a new event loop
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            results = loop.run_until_complete(orchestrator.execute_workflow(data))
            loop.close()
        except Exception as async_error:
            return jsonify({'error': f'Workflow execution failed: {str(async_error)}'}), 500
        
        return jsonify({'results': results}), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@agents_bp.route('/<agent_id>/status', methods=['GET'])
def get_agent_status(agent_id):
    """Get status of a specific agent"""
    try:
        if agent_id not in orchestrator.agents:
            return jsonify({'error': 'Agent not found'}), 404
        
        agent = orchestrator.agents[agent_id]
        return jsonify({
            'agent_id': agent.agent_id,
            'name': agent.name,
            'status': agent.status
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
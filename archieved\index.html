<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SynergyAI - Intelligent Project Workflow System</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>

    <header>
        <nav>
            <h1>SynergyAI</h1>
            <ul>
                <li><a href="#project-navigator">Project</a></li>
                <li><a href="#prompt-engineering">Prompts</a></li>
                <li><a href="#ai-execution">Execution</a></li>
                <li><a href="#summarization">Summarization</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <aside class="sidebar">
            <div id="project-navigator" class="panel">
                <h2>Project Navigator</h2>
                <!-- Project Definition Form will be loaded here -->
            </div>
            <div id="iteration-control" class="panel">
                <h2>Iteration Control</h2>
                <!-- Iteration Control Panel will be loaded here -->
            </div>
            <div id="context-management" class="panel">
                <h2>Context Management</h2>
                <!-- Context Management Area will be loaded here -->
            </div>
        </aside>

        <section class="main-content">
            <div id="prompt-engineering" class="workspace">
                <h2>Prompt Engineering Workspace</h2>
                <!-- Prompt Engineering Form will be loaded here -->
            </div>
            <div id="ai-execution" class="workspace">
                <h2>AI Execution Center</h2>
                <!-- AI Execution Configuration will be loaded here -->
            </div>
            <div id="summarization" class="dashboard">
                <h2>Summarization Dashboard</h2>
                <!-- Summarization Dashboard will be loaded here -->
            </div>
        </section>
    </main>

    <footer>
        <p>Status: Idle | &copy; 2025 SynergyAI</p>
    </footer>

    <!-- Modal and Overlay Components -->
    <div id="modal-container" class="modal-hidden"></div>
    <div id="overlay-container" class="overlay-hidden"></div>

    <!-- Component Templates -->
    <template id="ai-agent-card-template">
        <div class="ai-agent-card">
            <h3 class="agent-name">Agent Name</h3>
            <p class="agent-description">Agent description.</p>
            <div class="status-indicator idle">Idle</div>
            <div class="io-preview">
                <div class="input-preview"><h4>Input</h4><p>...</p></div>
                <div class="output-preview"><h4>Output</h4><p>...</p></div>
            </div>
            <div class="actions">
                <button data-action="configure">Configure</button>
                <button data-action="execute">Execute</button>
                <button data-action="view-details">View Details</button>
            </div>
            <div class="progress-indicator">
                <div class="progress-bar"></div>
            </div>
        </div>
    </template>

    <template id="project-summary-card-template">
        <div class="project-summary-card">
            <h3 class="project-title">Project Title</h3>
            <p class="project-description">Project description.</p>
            <div class="goal-hierarchy">
                <h4>Goals</h4>
                <p><strong>Short-term:</strong> ...</p>
                <p><strong>Long-term:</strong> ...</p>
            </div>
            <div class="milestone-progress">
                <p>Milestone 1: <progress value="70" max="100"></progress></p>
            </div>
            <p class="timestamp">Last updated: ...</p>
            <div class="actions">
                <button data-action="edit">Edit</button>
                <button data-action="export">Export</button>
            </div>
        </div>
    </template>

    <template id="prompt-strategy-template">
        <div class="prompt-strategy">
            <h3 class="strategy-name">Strategy Name</h3>
            <p class="strategy-description">Strategy description.</p>
            <div class="query-structure">
                <h4>Query Structure</h4>
                <pre>...</pre>
            </div>
            <div class="context-requirements">
                <h4>Context Requirements</h4>
                <ul><li>...</li></ul>
            </div>
            <p><strong>Target AI Model:</strong> ...</p>
            <div class="execution-history">
                <h4>Execution History</h4>
                <p>...</p>
            </div>
        </div>
    </template>

    <template id="ai-response-container-template">
        <div class="ai-response-container">
            <div class="response-metadata">
                <p><strong>Model:</strong> ...</p>
                <p><strong>Timestamp:</strong> ...</p>
                <p><strong>Tokens used:</strong> ...</p>
            </div>
            <div class="response-content">
                <p>AI response content goes here.</p>
            </div>
            <div class="actions">
                <button data-action="summarize">Summarize</button>
                <button data-action="export">Export</button>
                <button data-action="iterate">Iterate</button>
            </div>
            <div class="tagging-interface">
                <input type="text" placeholder="Add tags...">
            </div>
            <div class="feedback-controls">
                <button>👍</button>
                <button>👎</button>
            </div>
        </div>
    </template>

    <template id="iteration-loop-tracker-template">
        <div class="iteration-loop-tracker">
            <h4>Loop: <span class="loop-counter">1</span>, Stage: <span class="stage-indicator">Analysis</span></h4>
            <div class="timeline">...</div>
            <div class="decision-points">...</div>
            <div class="performance-metrics">...</div>
            <div class="loop-controls">
                <button>Pause</button>
                <button>Resume</button>
                <button>Stop</button>
            </div>
        </div>
    </template>

    <template id="context-panel-template">
        <div class="context-panel">
            <h4>Context Source: <span class="context-source">...</span></h4>
            <div class="context-content">
                <p>...</p>
                <button>Expand</button>
            </div>
            <p><strong>Relevance:</strong> <span class="relevance-score">...</span></p>
            <div class="context-controls">
                <label><input type="checkbox" checked> Include</label>
                <button>Edit</button>
            </div>
        </div>
    </template>

    <!-- Form Interfaces -->
    <template id="project-definition-form-template">
        <form id="project-definition-form">
            <label for="project-name">Project Name:</label>
            <input type="text" id="project-name" name="project-name" required>

            <label for="project-description">Project Description:</label>
            <textarea id="project-description" name="project-description" required></textarea>

            <fieldset>
                <legend>Goals</legend>
                <label for="short-term-goals">Short-term:</label>
                <textarea id="short-term-goals" name="short-term-goals"></textarea>
                <label for="long-term-goals">Long-term:</label>
                <textarea id="long-term-goals" name="long-term-goals"></textarea>
            </fieldset>

            <button type="submit">Define Project</button>
        </form>
    </template>

    <template id="prompt-engineering-form-template">
        <form id="prompt-engineering-form">
            <label for="prompt-title">Prompt Title:</label>
            <input type="text" id="prompt-title" name="prompt-title" required>

            <label for="prompt-content">Prompt:</label>
            <textarea id="prompt-content" name="prompt-content" rows="10" required></textarea>

            <label for="ai-model">AI Model:</label>
            <select id="ai-model" name="ai-model">
                <option value="gpt-4">GPT-4</option>
                <option value="claude-3">Claude 3</option>
            </select>

            <button type="submit">Save Prompt</button>
        </form>
    </template>

    <template id="ai-execution-config-template">
        <form id="ai-execution-config-form">
            <label for="execution-model">Model:</label>
            <select id="execution-model" name="execution-model">
                <option value="gpt-4">GPT-4</option>
                <option value="claude-3">Claude 3</option>
            </select>
            <button type="submit">Execute</button>
        </form>
    </template>

    <template id="context-management-form-template">
        <form id="context-management-form">
            <label for="context-source">Context Source:</label>
            <input type="text" id="context-source" name="context-source">
            <button type="submit">Update Context</button>
        </form>
    </template>

    <template id="governance-control-panel-template">
        <form id="governance-control-panel-form">
            <label><input type="checkbox" name="human-in-loop"> Human in the Loop</label>
            <button type="submit">Save Settings</button>
        </form>
    </template>

    <template id="feedback-form-template">
        <form id="feedback-form">
            <label for="rating">Rating:</label>
            <input type="range" id="rating" name="rating" min="1" max="5">
            <label for="feedback-text">Feedback:</label>
            <textarea id="feedback-text" name="feedback-text"></textarea>
            <button type="submit">Submit Feedback</button>
        </form>
    </template>

    <script src="js/script.js"></script>
    <script src="js/quick-fix.js"></script>
</body>
</html>

// Quick fix to add working forms to the SynergyAI frontend
console.log('🚀 SynergyAI Quick Fix Loading...');

document.addEventListener('DOMContentLoaded', function() {
    console.log('✅ DOM loaded, adding forms...');
    
    // Add working project form
    const projectNav = document.getElementById('project-navigator');
    if (projectNav) {
        projectNav.innerHTML = `
            <h2>🎯 Project Navigator</h2>
            <div style="padding: 20px; background: #f5f5f5; border-radius: 8px; margin-top: 10px;">
                <form id="project-definition-form">
                    <div style="margin-bottom: 15px;">
                        <label for="project-name" style="display: block; margin-bottom: 5px; font-weight: bold; color: #1e3a8a;">Project Name:</label>
                        <input type="text" id="project-name" name="project-name" required 
                               placeholder="Enter your project name..."
                               style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 6px; font-size: 14px;">
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label for="project-description" style="display: block; margin-bottom: 5px; font-weight: bold; color: #1e3a8a;">Project Description:</label>
                        <textarea id="project-description" name="project-description" required rows="4"
                                  placeholder="Describe your project goals and objectives..."
                                  style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 6px; resize: vertical; font-size: 14px;"></textarea>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label for="project-goals" style="display: block; margin-bottom: 5px; font-weight: bold; color: #1e3a8a;">Project Goals:</label>
                        <textarea id="project-goals" name="project-goals" rows="3" 
                                  placeholder="List your specific project goals..."
                                  style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 6px; resize: vertical; font-size: 14px;"></textarea>
                    </div>

                    <button type="submit" id="analyze-btn" 
                            style="background: linear-gradient(135deg, #1e3a8a, #3b82f6); color: white; padding: 12px 24px; border: none; border-radius: 6px; cursor: pointer; font-size: 16px; font-weight: bold; width: 100%; transition: all 0.3s;">
                        🤖 Analyze Project with AI
                    </button>
                </form>
            </div>
        `;
        console.log('✅ Project form added');
    }
    
    // Add Prompt Engineering Workspace (AI-Generated Prompts)
    const promptEngineering = document.getElementById('prompt-engineering');
    if (promptEngineering) {
        promptEngineering.innerHTML = `
            <h2>🧠 AI Prompt Engineering Workspace</h2>
            <div style="padding: 20px; background: #f8fafc; border-radius: 8px; margin-top: 10px;">
                <div style="background: #e0f2fe; padding: 15px; border-radius: 6px; margin-bottom: 20px; border-left: 4px solid #0284c7;">
                    <h4 style="margin: 0 0 8px 0; color: #0284c7;">🤖 AI-Powered Prompt Generation</h4>
                    <p style="margin: 0; font-size: 14px; color: #374151;">Describe your goal and let AI generate optimized prompts for you!</p>
                </div>
                
                <form id="prompt-strategy-form">
                    <div style="margin-bottom: 15px;">
                        <label for="strategy-goal" style="display: block; margin-bottom: 5px; font-weight: bold; color: #1e3a8a;">What do you want to achieve?</label>
                        <textarea id="strategy-goal" name="strategy-goal" rows="3" required
                                  placeholder="Describe your goal or challenge. For example: 'I need to improve team productivity' or 'I want to analyze market opportunities for my product'"
                                  style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 6px; resize: vertical; font-size: 14px;"></textarea>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label for="strategy-context" style="display: block; margin-bottom: 5px; font-weight: bold; color: #1e3a8a;">Context & Background:</label>
                        <textarea id="strategy-context" name="strategy-context" rows="2"
                                  placeholder="Provide relevant context: industry, team size, current situation, constraints, etc."
                                  style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 6px; resize: vertical; font-size: 14px;"></textarea>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label for="strategy-type" style="display: block; margin-bottom: 5px; font-weight: bold; color: #1e3a8a;">Strategy Type:</label>
                        <select id="strategy-type" name="strategy-type" 
                                style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 6px; font-size: 14px;">
                            <option value="analysis">Deep Analysis & Insights</option>
                            <option value="brainstorming">Creative Brainstorming</option>
                            <option value="problem-solving">Problem Solving</option>
                            <option value="planning">Strategic Planning</option>
                            <option value="evaluation">Evaluation & Comparison</option>
                            <option value="optimization">Process Optimization</option>
                        </select>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label for="output-format" style="display: block; margin-bottom: 5px; font-weight: bold; color: #1e3a8a;">Desired Output Format:</label>
                        <select id="output-format" name="output-format" 
                                style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 6px; font-size: 14px;">
                            <option value="structured">Structured Analysis</option>
                            <option value="actionable">Actionable Recommendations</option>
                            <option value="creative">Creative Ideas List</option>
                            <option value="step-by-step">Step-by-Step Guide</option>
                            <option value="pros-cons">Pros & Cons Analysis</option>
                            <option value="detailed">Detailed Report</option>
                        </select>
                    </div>

                    <button type="submit" id="generate-strategy-btn" 
                            style="background: linear-gradient(135deg, #7c3aed, #a855f7); color: white; padding: 12px 24px; border: none; border-radius: 6px; cursor: pointer; font-size: 16px; font-weight: bold; width: 100%; transition: all 0.3s; margin-bottom: 15px;">
                        🧠 Generate AI Prompt Strategy
                    </button>
                </form>

                <div id="generated-prompts-area" style="display: none; margin-top: 20px; padding: 20px; background: white; border-radius: 8px; border: 2px solid #e5e7eb;">
                    <h4 style="margin: 0 0 15px 0; color: #1e3a8a;">🎯 AI-Generated Prompt Strategy</h4>
                    <div id="prompt-display" style="background: #f8fafc; padding: 15px; border-radius: 6px; margin-bottom: 15px; font-family: 'Courier New', monospace; font-size: 13px; white-space: pre-wrap;"></div>
                    
                    <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                        <button id="execute-generated-prompt" 
                                style="background: linear-gradient(135deg, #059669, #10b981); color: white; padding: 10px 20px; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: bold; flex: 1;">
                            🚀 Execute This Prompt
                        </button>
                        <button id="refine-prompt" 
                                style="background: linear-gradient(135deg, #f59e0b, #fbbf24); color: white; padding: 10px 20px; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: bold; flex: 1;">
                            ✨ Refine Prompt
                        </button>
                    </div>
                    
                    <div style="font-size: 12px; color: #6b7280;">
                        <p><strong>Strategy:</strong> <span id="strategy-info"></span></p>
                        <p><strong>Output Format:</strong> <span id="format-info"></span></p>
                    </div>
                </div>
            </div>
        `;
    }

    // Add AI status display
    const aiExecution = document.getElementById('ai-execution');
    if (aiExecution) {
        aiExecution.innerHTML = `
            <h2>🤖 AI Execution Center</h2>
            <div style="padding: 20px; background: #f0f9ff; border: 2px solid #3b82f6; border-radius: 8px; margin-top: 10px;">
                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                    <span style="color: #16a34a; font-size: 20px; margin-right: 10px;">●</span>
                    <strong>Status: Connected to Backend</strong>
                </div>
                <p><strong>🤖 AI Model:</strong> OpenRouter cypher-alpha:free</p>
                <p><strong>🔗 Backend API:</strong> http://localhost:5000</p>
                <p><strong>📊 Ready for:</strong> Real-time AI project analysis</p>
            </div>
        `;
    }
    
    // Add results area
    const summarization = document.getElementById('summarization');
    if (summarization) {
        summarization.innerHTML = `
            <h2>📊 AI Analysis Results</h2>
            <div id="results-container" style="padding: 20px; background: #fefefe; border: 2px solid #e5e7eb; border-radius: 8px; margin-top: 10px;">
                <p style="color: #6b7280; font-style: italic;">Submit a project above to see AI analysis results here...</p>
            </div>
        `;
    }
    
    // Bind form submissions
    setTimeout(() => {
        // Bind project form
        const form = document.getElementById('project-definition-form');
        if (form) {
            form.addEventListener('submit', async function(e) {
                e.preventDefault();
                console.log('📝 Form submitted!');
                
                const formData = new FormData(form);
                const projectData = {
                    'project-name': formData.get('project-name'),
                    'project-description': formData.get('project-description'),
                    'project-goals': formData.get('project-goals')
                };
                
                console.log('📊 Project data:', projectData);
                
                // Update button
                const btn = document.getElementById('analyze-btn');
                const originalText = btn.textContent;
                btn.textContent = '🔄 Analyzing with AI...';
                btn.disabled = true;
                btn.style.background = '#6b7280';
                
                // Show loading in results
                const resultsContainer = document.getElementById('results-container');
                resultsContainer.innerHTML = `
                    <div style="text-align: center; padding: 20px;">
                        <div style="font-size: 24px; margin-bottom: 10px;">🔄</div>
                        <p><strong>AI is analyzing your project...</strong></p>
                        <p style="color: #6b7280;">This may take a few seconds</p>
                    </div>
                `;
                
                try {
                    // Call backend API
                    const response = await fetch('http://localhost:5000/api/v1/agents/execute', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            steps: [{
                                step_id: 'project_definition',
                                agent: 'project_navigator',
                                task: {
                                    type: 'define_project',
                                    data: projectData
                                }
                            }]
                        })
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        const result = data.results?.project_definition?.summary || 'Project analysis completed successfully!';
                        
                        // Show success
                        resultsContainer.innerHTML = `
                            <div style="border-left: 4px solid #16a34a; padding: 20px; background: #f0fdf4;">
                                <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                    <span style="color: #16a34a; font-size: 20px; margin-right: 10px;">✅</span>
                                    <strong style="color: #16a34a;">AI Analysis Complete!</strong>
                                </div>
                                <div style="background: white; padding: 15px; border-radius: 6px; margin-bottom: 15px;">
                                    <h4 style="margin: 0 0 10px 0; color: #1e3a8a;">AI Analysis Result:</h4>
                                    <p style="margin: 0; line-height: 1.6;">${result}</p>
                                </div>
                                <div style="font-size: 12px; color: #6b7280;">
                                    <p><strong>Model:</strong> OpenRouter cypher-alpha:free</p>
                                    <p><strong>Timestamp:</strong> ${new Date().toLocaleString()}</p>
                                </div>
                            </div>
                        `;
                        
                        // Show success notification
                        showNotification('🎉 AI analysis completed successfully!', 'success');
                        
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                    
                } catch (error) {
                    console.error('API Error:', error);
                    
                    // Show error with fallback
                    resultsContainer.innerHTML = `
                        <div style="border-left: 4px solid #f59e0b; padding: 20px; background: #fffbeb;">
                            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                <span style="color: #f59e0b; font-size: 20px; margin-right: 10px;">⚠️</span>
                                <strong style="color: #f59e0b;">Backend Offline - Demo Mode</strong>
                            </div>
                            <div style="background: white; padding: 15px; border-radius: 6px; margin-bottom: 15px;">
                                <h4 style="margin: 0 0 10px 0; color: #1e3a8a;">Demo Analysis for "${projectData['project-name']}":</h4>
                                <p style="margin: 0; line-height: 1.6;">This project shows strong potential with clear objectives. The described goals align well with modern development practices. Consider breaking down larger goals into smaller, measurable milestones for better tracking and execution.</p>
                            </div>
                            <div style="font-size: 12px; color: #6b7280;">
                                <p><strong>Mode:</strong> Demo (Backend not connected)</p>
                                <p><strong>Timestamp:</strong> ${new Date().toLocaleString()}</p>
                            </div>
                        </div>
                    `;
                    
                    showNotification('⚠️ Using demo mode - start backend for real AI', 'warning');
                }
                
                // Reset button
                btn.textContent = originalText;
                btn.disabled = false;
                btn.style.background = 'linear-gradient(135deg, #1e3a8a, #3b82f6)';
            });
            
            console.log('✅ Project form events bound');
        }

        // Bind prompt strategy form (AI-generated prompts)
        const strategyForm = document.getElementById('prompt-strategy-form');
        if (strategyForm) {
            strategyForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                console.log('📝 Strategy form submitted!');
                
                const formData = new FormData(strategyForm);
                const strategyData = {
                    goal: formData.get('strategy-goal'),
                    context: formData.get('strategy-context'),
                    type: formData.get('strategy-type'),
                    outputFormat: formData.get('output-format')
                };
                
                console.log('📊 Strategy data:', strategyData);
                
                // Update button
                const btn = document.getElementById('generate-strategy-btn');
                const originalText = btn.textContent;
                btn.textContent = '🧠 AI is generating prompt strategy...';
                btn.disabled = true;
                btn.style.background = '#6b7280';
                
                try {
                    // Call backend API to generate prompt strategy
                    const response = await fetch('http://localhost:5000/api/v1/agents/execute', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            steps: [{
                                step_id: 'generate_prompt_strategy',
                                agent: 'project_navigator',
                                task: {
                                    type: 'generate_prompt_strategy',
                                    data: strategyData
                                }
                            }]
                        })
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        const generatedPrompt = data.results?.generate_prompt_strategy?.summary || generateFallbackPrompt(strategyData);
                        
                        // Show generated prompt
                        showGeneratedPrompt(generatedPrompt, strategyData);
                        showNotification('🎉 AI prompt strategy generated!', 'success');
                        
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                    
                } catch (error) {
                    console.error('API Error:', error);
                    
                    // Generate fallback prompt
                    const fallbackPrompt = generateFallbackPrompt(strategyData);
                    showGeneratedPrompt(fallbackPrompt, strategyData);
                    showNotification('⚠️ Using offline prompt generation', 'warning');
                }
                
                // Reset button
                btn.textContent = originalText;
                btn.disabled = false;
                btn.style.background = 'linear-gradient(135deg, #7c3aed, #a855f7)';
            });
            
            console.log('✅ Prompt strategy form events bound');
        }

        // Bind generated prompt execution
        bindGeneratedPromptEvents();
    }, 100);
});

// Function to generate fallback prompts when backend is offline
function generateFallbackPrompt(strategyData) {
    const { goal, context, type, outputFormat } = strategyData;
    
    const strategyTemplates = {
        analysis: `Analyze the following situation in detail:

Goal: ${goal}
Context: ${context || 'No additional context provided'}

Please provide:
1. Current situation assessment
2. Key factors and variables involved
3. Potential challenges and opportunities
4. Data-driven insights and patterns
5. Strategic recommendations

Format your response as: ${outputFormat}`,

        brainstorming: `Generate creative solutions for the following challenge:

Challenge: ${goal}
Background: ${context || 'No additional context provided'}

Please brainstorm:
1. 10+ innovative ideas or approaches
2. Unconventional solutions that others might miss
3. Combinations of existing concepts in new ways
4. Future-oriented possibilities
5. Quick wins and long-term strategies

Format your response as: ${outputFormat}`,

        'problem-solving': `Help solve this problem systematically:

Problem: ${goal}
Context: ${context || 'No additional context provided'}

Please provide:
1. Problem definition and root cause analysis
2. Multiple solution approaches
3. Step-by-step implementation plan
4. Risk assessment and mitigation strategies
5. Success metrics and evaluation criteria

Format your response as: ${outputFormat}`,

        planning: `Create a strategic plan for:

Objective: ${goal}
Situation: ${context || 'No additional context provided'}

Please develop:
1. Clear strategic framework
2. Phased implementation timeline
3. Resource requirements and allocation
4. Key milestones and deliverables
5. Risk management and contingency plans

Format your response as: ${outputFormat}`,

        evaluation: `Evaluate and compare options for:

Decision: ${goal}
Context: ${context || 'No additional context provided'}

Please provide:
1. Comprehensive criteria for evaluation
2. Detailed pros and cons analysis
3. Quantitative and qualitative assessment
4. Risk-benefit analysis
5. Final recommendation with reasoning

Format your response as: ${outputFormat}`,

        optimization: `Optimize the following process or system:

Target: ${goal}
Current State: ${context || 'No additional context provided'}

Please analyze:
1. Current inefficiencies and bottlenecks
2. Optimization opportunities and quick wins
3. Process redesign recommendations
4. Technology and automation possibilities
5. Performance metrics and KPIs

Format your response as: ${outputFormat}`
    };

    return strategyTemplates[type] || strategyTemplates.analysis;
}

// Function to show generated prompt
function showGeneratedPrompt(prompt, strategyData) {
    const promptArea = document.getElementById('generated-prompts-area');
    const promptDisplay = document.getElementById('prompt-display');
    const strategyInfo = document.getElementById('strategy-info');
    const formatInfo = document.getElementById('format-info');
    
    promptDisplay.textContent = prompt;
    strategyInfo.textContent = strategyData.type.charAt(0).toUpperCase() + strategyData.type.slice(1);
    formatInfo.textContent = strategyData.outputFormat.charAt(0).toUpperCase() + strategyData.outputFormat.slice(1);
    
    promptArea.style.display = 'block';
    promptArea.scrollIntoView({ behavior: 'smooth' });
}

// Function to bind generated prompt events
function bindGeneratedPromptEvents() {
    // Execute generated prompt
    document.addEventListener('click', async function(e) {
        if (e.target.id === 'execute-generated-prompt') {
            const promptDisplay = document.getElementById('prompt-display');
            const generatedPrompt = promptDisplay.textContent;
            
            if (!generatedPrompt) return;
            
            // Update button
            const btn = e.target;
            const originalText = btn.textContent;
            btn.textContent = '🔄 Executing...';
            btn.disabled = true;
            btn.style.background = '#6b7280';
            
            // Show loading in results
            const resultsContainer = document.getElementById('results-container');
            resultsContainer.innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <div style="font-size: 24px; margin-bottom: 10px;">🔄</div>
                    <p><strong>AI is processing your generated prompt...</strong></p>
                    <p style="color: #6b7280;">Using optimized prompt strategy</p>
                </div>
            `;
            
            try {
                // Execute the generated prompt
                const response = await fetch('http://localhost:5000/api/v1/agents/execute', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        steps: [{
                            step_id: 'execute_generated_prompt',
                            agent: 'project_navigator',
                            task: {
                                type: 'execute_generated_prompt',
                                data: {
                                    prompt: generatedPrompt
                                }
                            }
                        }]
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    const result = data.results?.execute_generated_prompt?.summary || 'Prompt executed successfully!';
                    
                    // Show success
                    resultsContainer.innerHTML = `
                        <div style="border-left: 4px solid #7c3aed; padding: 20px; background: #faf5ff;">
                            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                <span style="color: #7c3aed; font-size: 20px; margin-right: 10px;">🎯</span>
                                <strong style="color: #7c3aed;">AI-Generated Prompt Executed!</strong>
                            </div>
                            <div style="background: white; padding: 15px; border-radius: 6px; margin-bottom: 15px;">
                                <h4 style="margin: 0 0 10px 0; color: #1e3a8a;">Strategic AI Response:</h4>
                                <p style="margin: 0; line-height: 1.6;">${result}</p>
                            </div>
                            <div style="font-size: 12px; color: #6b7280;">
                                <p><strong>Strategy:</strong> AI-Generated Optimized Prompt</p>
                                <p><strong>Model:</strong> OpenRouter cypher-alpha:free</p>
                                <p><strong>Timestamp:</strong> ${new Date().toLocaleString()}</p>
                            </div>
                        </div>
                    `;
                    
                    showNotification('🎉 AI strategy executed successfully!', 'success');
                    
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
                
            } catch (error) {
                console.error('API Error:', error);
                
                // Show demo response
                resultsContainer.innerHTML = `
                    <div style="border-left: 4px solid #f59e0b; padding: 20px; background: #fffbeb;">
                        <div style="display: flex; align-items: center; margin-bottom: 15px;">
                            <span style="color: #f59e0b; font-size: 20px; margin-right: 10px;">⚠️</span>
                            <strong style="color: #f59e0b;">Demo Mode - Generated Prompt Preview</strong>
                        </div>
                        <div style="background: white; padding: 15px; border-radius: 6px; margin-bottom: 15px;">
                            <h4 style="margin: 0 0 10px 0; color: #1e3a8a;">Demo Response:</h4>
                            <p style="margin: 0; line-height: 1.6;">Your AI-generated prompt would be executed here with real AI analysis. The system would provide detailed, strategic insights based on the optimized prompt structure created by the Prompt Engineering AI.</p>
                        </div>
                    </div>
                `;
                
                showNotification('⚠️ Demo mode - start backend for real execution', 'warning');
            }
            
            // Reset button
            btn.textContent = originalText;
            btn.disabled = false;
            btn.style.background = 'linear-gradient(135deg, #059669, #10b981)';
        }
        
        // Refine prompt
        if (e.target.id === 'refine-prompt') {
            showNotification('🔄 Prompt refinement feature coming soon!', 'info');
        }
    });
}
});

// Notification function
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 8px;
        color: white;
        font-weight: bold;
        z-index: 1000;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        animation: slideIn 0.3s ease-out;
    `;
    
    if (type === 'success') {
        notification.style.background = '#16a34a';
    } else if (type === 'warning') {
        notification.style.background = '#f59e0b';
    } else {
        notification.style.background = '#3b82f6';
    }
    
    notification.innerHTML = `
        ${message}
        <button onclick="this.parentElement.remove()" style="background: none; border: none; color: inherit; float: right; font-size: 18px; cursor: pointer; margin-left: 10px;">×</button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

console.log('✅ SynergyAI Quick Fix Loaded!');
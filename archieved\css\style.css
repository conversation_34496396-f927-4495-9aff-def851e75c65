/* Phase 2: CSS Styling and Design */
/* Prompt 2.1: Create the Core Stylesheet */

/* Design System Foundation */
:root {
    /* Color Palette */
    --primary-color: #1e3a8a; /* Deep blue */
    --secondary-color: #059669; /* Emerald green */
    --accent-color: #f59e0b; /* Amber */
    --neutral-100: #f8fafc; /* Light gray */
    --neutral-900: #1e293b; /* Dark gray */
    --text-color-light: #f8fafc;
    --text-color-dark: #1e293b;
    --status-error: #dc2626;
    --status-success: #16a34a;
    --status-warning: #facc15;

    /* Typography */
    --font-family-headings: 'Poppins', sans-serif;
    --font-family-body: 'Open Sans', sans-serif;
    --font-family-mono: 'JetBrains Mono', monospace;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;

    /* Sizing */
    --header-height: 60px;
    --sidebar-width: 250px;

    /* Borders and Shadows */
    --border-radius: 0.5rem;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Base Styles */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--font-family-body);
    background-color: var(--neutral-100);
    color: var(--text-color-dark);
    line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-headings);
    color: var(--primary-color);
}

a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: var(--accent-color);
}

/* Layout System */
header {
    background-color: var(--primary-color);
    color: var(--text-color-light);
    padding: 0 var(--spacing-md);
    height: var(--header-height);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: var(--box-shadow);
}

header nav {
    display: flex;
    justify-content: space-between;
    width: 100%;
}

header h1 {
    color: var(--text-color-light);
}

header ul {
    list-style: none;
    display: flex;
    gap: var(--spacing-md);
}

header ul a {
    color: var(--text-color-light);
}

main {
    display: grid;
    grid-template-columns: var(--sidebar-width) 1fr;
    margin-top: var(--header-height);
    height: calc(100vh - var(--header-height) - 40px); /* 40px for footer */
}

.sidebar {
    background-color: #fff;
    padding: var(--spacing-md);
    border-right: 1px solid #e5e7eb;
    overflow-y: auto;
}

.main-content {
    padding: var(--spacing-md);
    overflow-y: auto;
}

footer {
    background-color: var(--neutral-900);
    color: var(--text-color-light);
    text-align: center;
    padding: var(--spacing-sm);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
}

/* Component Styling */
.panel, .workspace, .dashboard {
    background-color: #fff;
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    box-shadow: var(--box-shadow);
}

button {
    background-color: var(--primary-color);
    color: var(--text-color-light);
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background-color 0.3s ease;
}

button:hover {
    background-color: var(--secondary-color);
}

input, textarea, select {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid #ccc;
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-md);
}

form {
    display: flex;
    flex-direction: column;
}

/* Interactive Elements */
button:focus, input:focus, textarea:focus, select:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* Modal and Overlay */
.modal-hidden, .overlay-hidden {
    display: none;
}

/* Prompt 2.2: Create Advanced UI Components */

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: var(--spacing-md);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-width: 400px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    animation: slideInRight 0.3s ease-out;
}

.notification-success {
    background-color: var(--status-success);
    color: white;
}

.notification-error {
    background-color: var(--status-error);
    color: white;
}

.notification-warning {
    background-color: var(--status-warning);
    color: var(--text-color-dark);
}

.notification-info {
    background-color: var(--primary-color);
    color: white;
}

.notification-close {
    background: none;
    border: none;
    color: inherit;
    font-size: 1.2rem;
    cursor: pointer;
    margin-left: var(--spacing-md);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Response Status */
.response-status {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: bold;
    text-transform: uppercase;
}

.response-status.success {
    background-color: var(--status-success);
    color: white;
}

.response-status.error {
    background-color: var(--status-error);
    color: white;
}

/* Response Metadata */
.response-metadata {
    margin-top: var(--spacing-md);
    padding: var(--spacing-md);
    background-color: var(--neutral-100);
    border-radius: 4px;
    font-size: 0.875rem;
}

.metadata-item {
    margin-bottom: var(--spacing-xs);
}

.metadata-item:last-child {
    margin-bottom: 0;
}

/* AI Agent Card */
.ai-agent-card {
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    transition: box-shadow 0.3s ease;
}

.ai-agent-card:hover {
    box-shadow: 0 8px 12px rgba(0,0,0,0.15);
}

.status-indicator {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 9999px;
    font-size: 0.8rem;
    display: inline-block;
    margin-bottom: var(--spacing-sm);
}

.status-indicator.idle { background-color: #d1d5db; }
.status-indicator.active { background-color: var(--status-success); color: white; }
.status-indicator.processing { background-color: var(--status-warning); }

.progress-indicator {
    width: 100%;
    background-color: #e5e7eb;
    border-radius: var(--border-radius);
    margin-top: var(--spacing-sm);
}

.progress-bar {
    width: 60%; /* Example */
    height: 10px;
    background-color: var(--secondary-color);
    border-radius: var(--border-radius);
}

/* Modal and Overlay System */
.modal-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.modal-content {
    background-color: white;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    width: 90%;
    max-width: 600px;
}

/* Animation and Transition Systems */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-container {
    animation: fadeIn 0.3s ease;
}

/* Prompt 2.3: Implement Responsive Design System */

/* Mobile-First (Default styles are mobile-first) */
header nav {
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
}

header {
    height: auto;
    padding: var(--spacing-sm);
}

main {
    grid-template-columns: 1fr;
    margin-top: 120px; /* Adjust for taller header */
    height: calc(100vh - 120px - 40px);
}

.sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
}

button {
    padding: var(--spacing-md); /* Larger touch targets */
}

/* Tablet Optimizations */
@media (min-width: 768px) {
    header {
        height: var(--header-height);
        padding: 0 var(--spacing-md);
    }

    header nav {
        flex-direction: row;
        justify-content: space-between;
    }

    main {
        grid-template-columns: var(--sidebar-width) 1fr;
        margin-top: var(--header-height);
        height: calc(100vh - var(--header-height) - 40px);
    }

    .sidebar {
        border-right: 1px solid #e5e7eb;
        border-bottom: none;
    }
}

/* Desktop Enhancements */
@media (min-width: 1024px) {
    :root {
        font-size: 16px; /* Base font size */
    }
}

/* Large Screens */
@media (min-width: 1440px) {
    .main-content {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: var(--spacing-md);
    }
}
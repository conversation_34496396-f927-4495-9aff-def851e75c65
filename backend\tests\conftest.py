# backend/tests/conftest.py
import pytest
import asyncio
from unittest.mock import Mock, patch
from backend.app import create_app, db
from backend.models.models import User, Project
from backend.config import TestingConfig


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def app():
    """Create and configure a new app instance for each test."""
    app = create_app(TestingConfig)
    
    with app.app_context():
        db.create_all()
        yield app
        db.session.remove()
        db.drop_all()


@pytest.fixture(scope="function")
def client(app):
    """A test client for the app."""
    return app.test_client()


@pytest.fixture
def sample_user(app):
    """Create a sample user for testing."""
    with app.app_context():
        user = User(
            username="testuser",
            email="<EMAIL>",
            password_hash="hashed_password",
            preferences={"theme": "light", "notifications": True}
        )
        db.session.add(user)
        db.session.commit()
        # Refresh to ensure the ID is available
        db.session.refresh(user)
        user_id = user.id
        db.session.expunge(user)  # Detach from session
        user.id = user_id  # Manually set the ID
        return user


@pytest.fixture
def mock_openrouter_client():
    """Mock OpenRouter client for testing."""
    with patch('backend.services.openrouter.OpenRouterClient') as mock_client:
        mock_instance = Mock()
        mock_instance.generate_completion.return_value = {
            "choices": [{
                "message": {
                    "content": "This is a mocked AI response for testing purposes."
                }
            }],
            "usage": {
                "prompt_tokens": 10,
                "completion_tokens": 15,
                "total_tokens": 25
            }
        }
        mock_client.return_value = mock_instance
        yield mock_instance
import pytest
from backend.app import create_app, db

@pytest.fixture(scope='module')
def test_app():
    app = create_app(config_class='backend.config.TestingConfig')
    with app.app_context():
        yield app

@pytest.fixture(scope='module')
def test_client(test_app):
    return test_app.test_client()

@pytest.fixture(scope='module')
def test_database(test_app):
    db.create_all()
    yield db
    db.session.remove()
    db.drop_all()
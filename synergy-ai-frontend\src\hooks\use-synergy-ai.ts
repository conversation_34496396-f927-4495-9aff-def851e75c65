import { useEffect, useCallback } from 'react';
import { useAppStore } from '../store/app-store';
import { getClient } from '../lib/api-client';
import { getWebSocketClient } from '../lib/websocket-client';
import type {
  CreateProjectRequest,
  UpdateProjectRequest,
  ExecuteWorkflowRequest,
  AnalyzeProjectRequest,
  SuggestStrategyRequest,
} from '../types/api';

export const useSynergyAI = () => {
  const store = useAppStore();

  // Initialize connections
  const initializeConnections = useCallback(async () => {
    try {
      store.setLoading({ isLoading: true, message: 'Connecting to SynergyAI...' });

      // Test API connection with shorter timeout
      const client = getClient();
      const isApiConnected = await Promise.race([
        client.checkAuth(),
        new Promise<boolean>((_, reject) =>
          setTimeout(() => reject(new Error('Connection timeout')), 3000)
        )
      ]);

      store.setApiConnected(isApiConnected);

      if (isApiConnected) {
        // Load initial data
        try {
          await Promise.all([
            loadProjects(),
            loadAgents(),
          ]);
        } catch (dataError) {
          console.warn('Failed to load initial data:', dataError);
        }
      }

      // Initialize WebSocket connection
      try {
        const wsClient = getWebSocketClient();
        await wsClient.connect();
        store.setWebsocketConnected(true);
      } catch (wsError) {
        console.warn('WebSocket connection failed:', wsError);
        store.setWebsocketConnected(false);
      }

      store.updateConnectionCheck();
    } catch (error: any) {
      console.warn('API connection failed:', error);
      store.setError({
        hasError: true,
        message: 'Backend API is not available',
        details: error,
      });
      store.setApiConnected(false);
    } finally {
      store.setLoading({ isLoading: false });
    }
  }, [store]);

  // Project operations
  const loadProjects = useCallback(async () => {
    try {
      const client = getClient();
      const projects = await client.getProjects();
      store.setProjects(projects);
    } catch (error: any) {
      store.setError({
        hasError: true,
        message: 'Failed to load projects',
        details: error,
      });
      throw error;
    }
  }, [store]);

  const createProject = useCallback(async (projectData: CreateProjectRequest) => {
    try {
      store.setLoading({ isLoading: true, message: 'Creating project...' });
      const client = getClient();
      const newProject = await client.createProject(projectData);
      store.addProject(newProject);
      return newProject;
    } catch (error: any) {
      store.setError({
        hasError: true,
        message: 'Failed to create project',
        details: error,
      });
      throw error;
    } finally {
      store.setLoading({ isLoading: false });
    }
  }, [store]);

  const updateProject = useCallback(async (id: number, updates: UpdateProjectRequest) => {
    try {
      store.setLoading({ isLoading: true, message: 'Updating project...' });
      const client = getClient();
      const updatedProject = await client.updateProject(id, updates);
      store.updateProject(id, updatedProject);
      return updatedProject;
    } catch (error: any) {
      store.setError({
        hasError: true,
        message: 'Failed to update project',
        details: error,
      });
      throw error;
    } finally {
      store.setLoading({ isLoading: false });
    }
  }, [store]);

  const deleteProject = useCallback(async (id: number) => {
    try {
      store.setLoading({ isLoading: true, message: 'Deleting project...' });
      const client = getClient();
      await client.deleteProject(id);
      store.deleteProject(id);
    } catch (error: any) {
      store.setError({
        hasError: true,
        message: 'Failed to delete project',
        details: error,
      });
      throw error;
    } finally {
      store.setLoading({ isLoading: false });
    }
  }, [store]);

  // Agent operations
  const loadAgents = useCallback(async () => {
    try {
      const client = getClient();
      const agents = await client.getAgents();
      store.setAgents(agents);
    } catch (error: any) {
      store.setError({
        hasError: true,
        message: 'Failed to load agents',
        details: error,
      });
      throw error;
    }
  }, [store]);

  const executeWorkflow = useCallback(async (request: ExecuteWorkflowRequest) => {
    try {
      store.setLoading({ isLoading: true, message: 'Executing workflow...' });
      const client = getClient();
      const result = await client.executeWorkflow(request);
      store.addWorkflowResult(result);
      return result;
    } catch (error: any) {
      store.setError({
        hasError: true,
        message: 'Failed to execute workflow',
        details: error,
      });
      throw error;
    } finally {
      store.setLoading({ isLoading: false });
    }
  }, [store]);

  // AI Strategy operations
  const analyzeProject = useCallback(async (request: AnalyzeProjectRequest) => {
    try {
      store.setLoading({ isLoading: true, message: 'Analyzing project...' });
      const client = getClient();
      const response = await client.analyzeProject(request);
      store.setCurrentAnalysis(response.analysis);
      return response;
    } catch (error: any) {
      store.setError({
        hasError: true,
        message: 'Failed to analyze project',
        details: error,
      });
      throw error;
    } finally {
      store.setLoading({ isLoading: false });
    }
  }, [store]);

  const suggestStrategy = useCallback(async (request: SuggestStrategyRequest) => {
    try {
      store.setLoading({ isLoading: true, message: 'Getting strategy recommendation...' });
      const client = getClient();
      const response = await client.suggestStrategy(request);
      store.setCurrentStrategy(response.strategy);
      return response;
    } catch (error: any) {
      store.setError({
        hasError: true,
        message: 'Failed to get strategy recommendation',
        details: error,
      });
      throw error;
    } finally {
      store.setLoading({ isLoading: false });
    }
  }, [store]);

  const getPromptStrategy = useCallback(async (projectId: number, objectives: string[]) => {
    try {
      store.setLoading({ isLoading: true, message: 'Getting AI strategy...' });
      const client = getClient();
      const result = await client.getPromptStrategy(projectId, objectives);
      store.setCurrentAnalysis(result.analysis.analysis);
      store.setCurrentStrategy(result.strategy.strategy);
      return result;
    } catch (error: any) {
      store.setError({
        hasError: true,
        message: 'Failed to get AI strategy',
        details: error,
      });
      throw error;
    } finally {
      store.setLoading({ isLoading: false });
    }
  }, [store]);

  // WebSocket event handlers - only set up after WebSocket is initialized
  useEffect(() => {
    // Only set up WebSocket listeners if connected
    if (!store.websocketConnected) {
      return;
    }

    try {
      const wsClient = getWebSocketClient();

      const unsubscribeAgentStatus = wsClient.onAgentStatusUpdate((data) => {
        store.updateAgent(data.agent_id, { status: data.status });
      });

      const unsubscribeWorkflowProgress = wsClient.onWorkflowProgress((data) => {
        // Handle workflow progress updates
        console.log('Workflow progress:', data);
      });

      const unsubscribeAIComplete = wsClient.onAIExecutionComplete((data) => {
        // Handle AI execution completion
        console.log('AI execution complete:', data);
      });

      return () => {
        unsubscribeAgentStatus();
        unsubscribeWorkflowProgress();
        unsubscribeAIComplete();
      };
    } catch (error) {
      console.warn('Failed to set up WebSocket listeners:', error);
    }
  }, [store, store.websocketConnected]);

  return {
    // State
    ...store,
    
    // Actions
    initializeConnections,
    loadProjects,
    createProject,
    updateProject,
    deleteProject,
    loadAgents,
    executeWorkflow,
    analyzeProject,
    suggestStrategy,
    getPromptStrategy,
    
    // Utilities
    clearError: store.clearError,
    selectProject: store.selectProject,
    setActivePanel: store.setActivePanel,
  };
};

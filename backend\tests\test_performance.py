# backend/tests/test_performance.py
import pytest
import time
import asyncio
import threading
from concurrent.futures import ThreadPoolExecutor
from unittest.mock import patch, Mock
from backend.app import create_app, db
from backend.services.openrouter import OpenRouterClient
from backend.services.agents import AgentOrchestrator
from backend.config import TestingConfig


class TestPerformance:
    """Performance tests for SynergyAI application"""
    
    @pytest.fixture
    def app(self):
        """Create test application"""
        app = create_app(TestingConfig)
        with app.app_context():
            db.create_all()
            yield app
            db.drop_all()
    
    @pytest.fixture
    def client(self, app):
        """Create test client"""
        return app.test_client()
    
    @pytest.mark.slow
    def test_api_response_time(self, client, app):
        """Test API response times are within acceptable limits"""
        with app.app_context():
            endpoints = [
                '/api/v1/projects/',
                '/api/v1/agents/',
            ]
            
            max_response_time = 2.0  # seconds
            
            for endpoint in endpoints:
                start_time = time.time()
                response = client.get(endpoint)
                end_time = time.time()
                
                response_time = end_time - start_time
                
                assert response.status_code in [200, 404]  # Allow 404 for empty endpoints
                assert response_time < max_response_time, f"Endpoint {endpoint} took {response_time:.2f}s"
    
    @pytest.mark.slow
    @patch('backend.services.openrouter.OpenRouterClient.generate_completion')
    def test_concurrent_ai_requests(self, mock_ai_client):
        """Test handling of concurrent AI requests"""
        # Mock AI response with slight delay to simulate real API
        def mock_response(*args, **kwargs):
            time.sleep(0.1)  # Simulate API latency
            return {
                "choices": [{"message": {"content": "Concurrent response"}}],
                "usage": {"total_tokens": 20}
            }
        
        mock_ai_client.side_effect = mock_response
        
        client = OpenRouterClient(api_key="test-key")
        
        # Test concurrent requests
        num_requests = 10
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = []
            for i in range(num_requests):
                future = executor.submit(
                    client.generate_completion,
                    f"Test prompt {i}",
                    "openrouter/cypher-alpha:free"
                )
                futures.append(future)
            
            # Wait for all requests to complete
            results = [future.result() for future in futures]
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Verify all requests completed successfully
        assert len(results) == num_requests
        for result in results:
            assert "choices" in result
        
        # Performance assertion - should complete faster than sequential execution
        max_expected_time = num_requests * 0.15  # Allow some overhead
        assert total_time < max_expected_time, f"Concurrent requests took {total_time:.2f}s"
    
    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_workflow_execution_performance(self):
        """Test performance of workflow execution"""
        orchestrator = AgentOrchestrator()
        
        with patch('backend.services.openrouter.OpenRouterClient.generate_completion') as mock_ai:
            mock_ai.return_value = {
                "choices": [{"message": {"content": "Fast response"}}]
            }
            
            # Create a complex workflow
            workflow_config = {
                "steps": [
                    {
                        "step_id": f"step_{i}",
                        "agent": "project_navigator",
                        "task": {
                            "type": "define_project",
                            "data": {"title": f"Performance Test Project {i}"}
                        }
                    }
                    for i in range(5)
                ]
            }
            
            start_time = time.time()
            results = await orchestrator.execute_workflow(workflow_config)
            end_time = time.time()
            
            execution_time = end_time - start_time
            
            # Verify workflow completed
            assert len(results) == 5
            
            # Performance assertion
            max_execution_time = 3.0  # seconds
            assert execution_time < max_execution_time, f"Workflow took {execution_time:.2f}s"
    
    @pytest.mark.slow
    def test_database_query_performance(self, app):
        """Test database query performance with large datasets"""
        with app.app_context():
            from backend.models.models import User, Project
            
            # Create test data
            users = []
            projects = []
            
            # Create users
            for i in range(100):
                user = User(
                    username=f"user_{i}",
                    email=f"user_{i}@example.com",
                    password_hash="hashed_password"
                )
                users.append(user)
            
            db.session.add_all(users)
            db.session.commit()
            
            # Create projects
            for i in range(500):
                project = Project(
                    title=f"Project {i}",
                    description=f"Description for project {i}",
                    user_id=users[i % 100].id
                )
                projects.append(project)
            
            db.session.add_all(projects)
            db.session.commit()
            
            # Test query performance
            start_time = time.time()
            all_projects = Project.query.all()
            end_time = time.time()
            
            query_time = end_time - start_time
            
            assert len(all_projects) == 500
            assert query_time < 1.0, f"Database query took {query_time:.2f}s"
    
    @pytest.mark.slow
    def test_memory_usage_during_large_operations(self, app):
        """Test memory usage during large operations"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        with app.app_context():
            # Simulate large operation
            large_data = []
            for i in range(10000):
                large_data.append({
                    "id": i,
                    "data": f"Large data item {i}" * 100
                })
            
            # Process the data
            processed_data = [item for item in large_data if item["id"] % 2 == 0]
            
            current_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = current_memory - initial_memory
            
            # Clean up
            del large_data
            del processed_data
            
            # Memory usage should be reasonable
            max_memory_increase = 100  # MB
            assert memory_increase < max_memory_increase, f"Memory increased by {memory_increase:.2f}MB"
    
    @pytest.mark.slow
    def test_api_load_handling(self, client, app):
        """Test API load handling with multiple concurrent requests"""
        with app.app_context():
            def make_request():
                return client.get('/api/v1/projects/')
            
            # Simulate concurrent users
            num_concurrent_requests = 20
            start_time = time.time()
            
            with ThreadPoolExecutor(max_workers=10) as executor:
                futures = [executor.submit(make_request) for _ in range(num_concurrent_requests)]
                responses = [future.result() for future in futures]
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Verify all requests completed successfully
            for response in responses:
                assert response.status_code == 200
            
            # Performance assertion
            max_total_time = 5.0  # seconds
            assert total_time < max_total_time, f"Load test took {total_time:.2f}s"
            
            # Calculate average response time
            avg_response_time = total_time / num_concurrent_requests
            max_avg_response_time = 0.5  # seconds
            assert avg_response_time < max_avg_response_time, f"Average response time: {avg_response_time:.2f}s"
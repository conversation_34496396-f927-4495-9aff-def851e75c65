import React, { useState } from 'react';

export const DirectAPITest: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [testing, setTesting] = useState(false);

  const testDirectConnection = async () => {
    setTesting(true);
    setTestResults([]);
    const results: string[] = [];

    try {
      results.push('🔄 Testing direct connection to http://localhost:5000/');
      setTestResults([...results]);

      // Test root endpoint
      const rootResponse = await fetch('http://localhost:5000/');
      const rootData = await rootResponse.json();
      
      if (rootResponse.ok) {
        results.push('✅ Root endpoint: Success');
        results.push(`📋 Backend status: ${rootData.status}`);
        results.push(`📋 Available endpoints: ${Object.keys(rootData.endpoints).join(', ')}`);
      } else {
        results.push('❌ Root endpoint: Failed');
      }
      setTestResults([...results]);

      // Test projects endpoint
      results.push('🔄 Testing projects endpoint...');
      setTestResults([...results]);

      const projectsResponse = await fetch('http://localhost:5000/api/v1/projects');
      
      if (projectsResponse.ok) {
        const projectsData = await projectsResponse.json();
        results.push('✅ Projects API: Success');
        results.push(`📋 Projects found: ${projectsData.projects?.length || 0}`);
      } else {
        results.push(`❌ Projects API: Failed (${projectsResponse.status})`);
      }
      setTestResults([...results]);

      // Test agents endpoint
      results.push('🔄 Testing agents endpoint...');
      setTestResults([...results]);

      const agentsResponse = await fetch('http://localhost:5000/api/v1/agents');
      
      if (agentsResponse.ok) {
        const agentsData = await agentsResponse.json();
        results.push('✅ Agents API: Success');
        results.push(`📋 Agents found: ${agentsData.agents?.length || 0}`);
      } else {
        results.push(`❌ Agents API: Failed (${agentsResponse.status})`);
      }

      results.push('🎉 Direct API test completed!');
    } catch (error: any) {
      results.push(`❌ Connection failed: ${error.message}`);
      results.push('💡 Make sure the backend is running at http://localhost:5000/');
    } finally {
      setTestResults(results);
      setTesting(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Direct API Connection Test</h3>
      
      <p className="text-sm text-gray-600 mb-4">
        This test bypasses the frontend API client and connects directly to the backend.
      </p>

      <button
        onClick={testDirectConnection}
        disabled={testing}
        className="bg-green-600 hover:bg-green-700 disabled:bg-green-300 text-white font-medium px-4 py-2 rounded-lg transition-colors duration-200 mb-4"
      >
        {testing ? 'Testing...' : 'Test Direct Connection'}
      </button>

      {testResults.length > 0 && (
        <div className="bg-gray-50 rounded-lg p-3">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Test Results:</h4>
          <div className="space-y-1 max-h-64 overflow-y-auto">
            {testResults.map((result, index) => (
              <div key={index} className="text-sm text-gray-600 font-mono">
                {result}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

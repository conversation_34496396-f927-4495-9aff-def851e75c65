# backend/services/agents.py
import asyncio
from abc import ABC, abstractmethod
from typing import Dict, Any
from .openrouter import OpenRouterClient

class BaseAgent(ABC):
    def __init__(self, agent_id: str, name: str):
        self.agent_id = agent_id
        self.name = name
        self.status = "idle"
        self.ai_client = OpenRouterClient()

    @abstractmethod
    async def process_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        pass

class ProjectNavigatorAgent(BaseAgent):
    def __init__(self):
        super().__init__(agent_id="project_navigator", name="Project Navigator AI")

    async def process_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        task_type = task.get("type")
        if task_type == "define_project":
            return await self._define_project(task["data"])
        else:
            raise ValueError(f"Unknown task type: {task_type}")

    async def _define_project(self, project_data: Dict[str, Any]) -> Dict[str, Any]:
        prompt = self._create_project_definition_prompt(project_data)
        
        # Note: The client's generate_completion is not async,
        # so we run it in a thread pool to avoid blocking the event loop.
        loop = asyncio.get_running_loop()
        response = await loop.run_in_executor(
            None,  # Uses the default executor
            self.ai_client.generate_completion,
            prompt,
            "openrouter/cypher-alpha:free" # Using the free model as specified
        )

        # Parse the response properly for the new API format
        if "error" in response:
            return {"summary": f"AI Error: {response['error']}"}
        
        if "choices" in response and len(response["choices"]) > 0:
            # Handle the new message format
            choice = response["choices"][0]
            if "message" in choice:
                content = choice["message"].get("content", "")
            else:
                content = choice.get("text", "")
            
            if content:
                return {"summary": content}
        
        return {"summary": "Could not generate summary - no valid response from AI model."}

    def _create_project_definition_prompt(self, project_data: Dict[str, Any]) -> str:
        # Handle both old and new data formats
        title = project_data.get('title') or project_data.get('project-name', 'Untitled Project')
        description = project_data.get('description') or project_data.get('project-description', 'No description provided')
        goals = project_data.get('goals') or project_data.get('project-goals', 'No goals specified')
        
        return f"""
        As an AI project analyst, please analyze the following project and provide a comprehensive summary with insights and recommendations:

        Project Title: {title}
        Project Description: {description}
        Project Goals: {goals}

        Please provide:
        1. A brief analysis of the project scope and objectives
        2. Key strengths and potential challenges
        3. Recommended next steps or considerations
        4. Overall assessment of project viability

        Keep the response concise but informative (2-3 paragraphs).
        """

class AgentOrchestrator:
    def __init__(self):
        self.agents = {
            "project_navigator": ProjectNavigatorAgent()
        }

    async def execute_workflow(self, workflow_config: Dict[str, Any]) -> Dict[str, Any]:
        results = {}
        for step in workflow_config.get("steps", []):
            agent_id = step.get("agent")
            task = step.get("task")
            if agent_id in self.agents:
                agent = self.agents[agent_id]
                result = await agent.process_task(task)
                results[step["step_id"]] = result
        return results

# backend/tests/test_app.py
import pytest
from backend.app import create_app, db
from backend.config import TestingConfig


class TestAppConfiguration:
    """Test suite for Flask application configuration and setup"""
    
    def test_create_app_with_testing_config(self):
        """Test app creation with testing configuration"""
        app = create_app(TestingConfig)
        assert app is not None
        assert app.config['TESTING'] is True
        assert 'sqlite:///:memory:' in app.config['SQLALCHEMY_DATABASE_URI']
    
    def test_create_app_with_default_config(self):
        """Test app creation with default configuration"""
        app = create_app()
        assert app is not None
        assert app.config.get('TESTING') is not True
    
    def test_database_initialization(self):
        """Test database initialization"""
        app = create_app(TestingConfig)
        with app.app_context():
            db.create_all()
            # Should not raise any exceptions
            assert True
            db.drop_all()
    
    def test_index_route(self):
        """Test the index route"""
        app = create_app(TestingConfig)
        client = app.test_client()
        
        response = client.get('/')
        assert response.status_code == 200
        assert b'SynergyAI Backend is running!' in response.data


def test_create_app():
    """
    Tests if the create_app function returns a Flask app instance.
    """
    app = create_app(TestingConfig)
    assert app is not None
    assert app.config['TESTING'] is True

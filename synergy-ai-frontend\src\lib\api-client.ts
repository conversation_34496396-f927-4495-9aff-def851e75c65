import axios, { type AxiosInstance, type AxiosError } from 'axios';
import type {
  Project,
  ProjectsResponse,
  CreateProjectRequest,
  UpdateProjectRequest,
  Agent,
  AgentsResponse,
  ExecuteWorkflowRequest,
  WorkflowResult,
  AnalyzeProjectRequest,
  AnalyzeProjectResponse,
  SuggestStrategyRequest,
  SuggestStrategyResponse,
  ScoreContextRequest,
  ScoreContextResponse,
  ApiError,
  AuthConfig,
} from '../types/api';

export class SynergyAIClient {
  private api: AxiosInstance;
  private authToken: string;

  constructor(config: AuthConfig) {
    this.authToken = config.token;

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Only add Authorization header if token is provided
    if (this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`;
    }

    this.api = axios.create({
      baseURL: config.baseURL || 'http://localhost:5000/api/v1',
      headers,
      timeout: 10000, // 10 second timeout
    });

    // Add response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error: AxiosError) => {
        return Promise.reject(this.handleError(error));
      }
    );
  }

  // Error handling
  private handleError(error: AxiosError): ApiError {
    if (error.response) {
      const data = error.response.data as any;
      return {
        error: data?.error || 'API Error',
        code: data?.code || 'UNKNOWN_ERROR',
        details: {
          status: error.response.status,
          statusText: error.response.statusText,
          ...data?.details,
        },
      };
    }
    
    if (error.request) {
      return {
        error: 'Network Error - Unable to reach server',
        code: 'NETWORK_ERROR',
        details: { message: error.message },
      };
    }
    
    return {
      error: error.message || 'Unknown Error',
      code: 'UNKNOWN_ERROR',
    };
  }

  // Health check - test basic connectivity
  async checkHealth(): Promise<boolean> {
    try {
      // Test the root endpoint first
      const response = await this.api.get('/', {
        timeout: 5000,
        baseURL: this.api.defaults.baseURL?.replace('/api/v1', '') || 'http://localhost:5000'
      });
      return response.status === 200;
    } catch (error: any) {
      console.warn('Health check failed:', error.message);
      return false;
    }
  }

  // Authentication
  async checkAuth(): Promise<boolean> {
    try {
      // First check if the server is reachable
      const isHealthy = await this.checkHealth();
      if (!isHealthy) {
        return false;
      }

      // Then try to access a protected endpoint
      await this.getProjects();
      return true;
    } catch (error: any) {
      if (error.details?.status === 401) {
        return false;
      }
      // If it's a network error, the server might not require auth
      if (error.code === 'NETWORK_ERROR') {
        return false;
      }
      // For other errors, assume we're connected but there might be other issues
      console.warn('Auth check failed but server seems reachable:', error.message);
      return true;
    }
  }

  updateAuthToken(token: string): void {
    this.authToken = token;
    this.api.defaults.headers['Authorization'] = `Bearer ${token}`;
  }

  // Projects API
  async getProjects(): Promise<Project[]> {
    const response = await this.api.get<ProjectsResponse>('/projects');
    return response.data.projects;
  }

  async getProject(id: number): Promise<Project> {
    const response = await this.api.get<Project>(`/projects/${id}`);
    return response.data;
  }

  async createProject(projectData: CreateProjectRequest): Promise<Project> {
    const response = await this.api.post<Project>('/projects', projectData);
    return response.data;
  }

  async updateProject(id: number, updates: UpdateProjectRequest): Promise<Project> {
    const response = await this.api.put<Project>(`/projects/${id}`, updates);
    return response.data;
  }

  async deleteProject(id: number): Promise<void> {
    await this.api.delete(`/projects/${id}`);
  }

  // Agents API
  async getAgents(): Promise<Agent[]> {
    const response = await this.api.get<AgentsResponse>('/agents');
    return response.data.agents;
  }

  async getAgentStatus(agentId: string): Promise<Agent> {
    const response = await this.api.get<Agent>(`/agents/${agentId}/status`);
    return response.data;
  }

  async executeWorkflow(steps: ExecuteWorkflowRequest): Promise<WorkflowResult> {
    const response = await this.api.post<WorkflowResult>('/agents/execute', steps);
    return response.data;
  }

  // Intelligent Prompt Engineering API
  async analyzeProject(request: AnalyzeProjectRequest): Promise<AnalyzeProjectResponse> {
    const response = await this.api.post<AnalyzeProjectResponse>(
      '/agents/prompt-engineer/analyze',
      request
    );
    return response.data;
  }

  async suggestStrategy(request: SuggestStrategyRequest): Promise<SuggestStrategyResponse> {
    const response = await this.api.post<SuggestStrategyResponse>(
      '/agents/prompt-engineer/suggest-strategy',
      request
    );
    return response.data;
  }

  async scoreContext(request: ScoreContextRequest): Promise<ScoreContextResponse> {
    const response = await this.api.post<ScoreContextResponse>(
      '/agents/prompt-engineer/score-context',
      request
    );
    return response.data;
  }

  // Combined workflow methods
  async getPromptStrategy(projectId: number, objectives: string[]): Promise<{
    analysis: AnalyzeProjectResponse;
    strategy: SuggestStrategyResponse;
  }> {
    // Step 1: Analyze project goals
    const analysis = await this.analyzeProject({
      project_id: projectId,
      current_stage: 'research',
      user_objectives: objectives,
    });

    // Step 2: Get strategy recommendation
    const strategy = await this.suggestStrategy({
      analysis_id: 'temp_id', // This would come from the analysis response in real implementation
      domain_context: analysis.analysis.current_objective,
      priority_goals: objectives,
    });

    return { analysis, strategy };
  }
}

// Default client instance
let defaultClient: SynergyAIClient | null = null;

export const createClient = (config: AuthConfig): SynergyAIClient => {
  const client = new SynergyAIClient(config);
  defaultClient = client;
  return client;
};

export const getClient = (): SynergyAIClient => {
  if (!defaultClient) {
    throw new Error('API client not initialized. Call createClient() first.');
  }
  return defaultClient;
};

// Utility function for testing API connectivity
export const testConnection = async (baseURL = 'http://localhost:5000'): Promise<boolean> => {
  try {
    const response = await fetch(baseURL);
    return response.ok;
  } catch {
    return false;
  }
};

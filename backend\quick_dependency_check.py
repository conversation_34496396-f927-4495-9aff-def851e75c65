#!/usr/bin/env python3
"""
Quick dependency check for SynergyAI Phase 6.2
"""

def check_dependencies():
    """Check if all required dependencies are available"""
    print("🔍 Checking SynergyAI Dependencies...")
    print("=" * 50)
    
    dependencies = [
        ("Flask", "flask"),
        ("Flask-SQLAlchemy", "flask_sqlalchemy"),
        ("Flask-Migrate", "flask_migrate"),
        ("python-dotenv", "dotenv"),
        ("requests", "requests"),
        ("pytest", "pytest"),
        ("pytest-asyncio", "pytest_asyncio"),
    ]
    
    missing = []
    installed = []
    
    for name, module in dependencies:
        try:
            __import__(module)
            print(f"✅ {name}")
            installed.append(name)
        except ImportError:
            print(f"❌ {name} - MISSING")
            missing.append(name)
    
    print("\n" + "=" * 50)
    print(f"📊 Results: {len(installed)}/{len(dependencies)} dependencies available")
    
    if missing:
        print(f"\n🚨 Missing dependencies: {', '.join(missing)}")
        print("\n💡 To install missing dependencies:")
        print("   pip install -r requirements.txt")
        print("   pip install -r requirements-test.txt")
        return False
    else:
        print("\n🎉 All dependencies are installed!")
        print("✅ Ready to run Phase 6.2 tests!")
        return True

if __name__ == "__main__":
    success = check_dependencies()
    if success:
        print("\n🚀 You can now run: python run_tests.py")
    else:
        print("\n⚠️  Please install missing dependencies first")
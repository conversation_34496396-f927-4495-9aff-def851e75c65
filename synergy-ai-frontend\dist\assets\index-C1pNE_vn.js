(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const f of document.querySelectorAll('link[rel="modulepreload"]'))c(f);new MutationObserver(f=>{for(const h of f)if(h.type==="childList")for(const y of h.addedNodes)y.tagName==="LINK"&&y.rel==="modulepreload"&&c(y)}).observe(document,{childList:!0,subtree:!0});function s(f){const h={};return f.integrity&&(h.integrity=f.integrity),f.referrerPolicy&&(h.referrerPolicy=f.referrerPolicy),f.crossOrigin==="use-credentials"?h.credentials="include":f.crossOrigin==="anonymous"?h.credentials="omit":h.credentials="same-origin",h}function c(f){if(f.ep)return;f.ep=!0;const h=s(f);fetch(f.href,h)}})();function d0(u){return u&&u.__esModule&&Object.prototype.hasOwnProperty.call(u,"default")?u.default:u}var Jc={exports:{}},Ya={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Th;function h0(){if(Th)return Ya;Th=1;var u=Symbol.for("react.transitional.element"),a=Symbol.for("react.fragment");function s(c,f,h){var y=null;if(h!==void 0&&(y=""+h),f.key!==void 0&&(y=""+f.key),"key"in f){h={};for(var S in f)S!=="key"&&(h[S]=f[S])}else h=f;return f=h.ref,{$$typeof:u,type:c,key:y,ref:f!==void 0?f:null,props:h}}return Ya.Fragment=a,Ya.jsx=s,Ya.jsxs=s,Ya}var Rh;function y0(){return Rh||(Rh=1,Jc.exports=h0()),Jc.exports}var L=y0(),Wc={exports:{}},et={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Oh;function m0(){if(Oh)return et;Oh=1;var u=Symbol.for("react.transitional.element"),a=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),f=Symbol.for("react.profiler"),h=Symbol.for("react.consumer"),y=Symbol.for("react.context"),S=Symbol.for("react.forward_ref"),T=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),b=Symbol.for("react.lazy"),N=Symbol.iterator;function z(g){return g===null||typeof g!="object"?null:(g=N&&g[N]||g["@@iterator"],typeof g=="function"?g:null)}var H={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},B=Object.assign,q={};function j(g,M,G){this.props=g,this.context=M,this.refs=q,this.updater=G||H}j.prototype.isReactComponent={},j.prototype.setState=function(g,M){if(typeof g!="object"&&typeof g!="function"&&g!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,g,M,"setState")},j.prototype.forceUpdate=function(g){this.updater.enqueueForceUpdate(this,g,"forceUpdate")};function Q(){}Q.prototype=j.prototype;function I(g,M,G){this.props=g,this.context=M,this.refs=q,this.updater=G||H}var nt=I.prototype=new Q;nt.constructor=I,B(nt,j.prototype),nt.isPureReactComponent=!0;var Ot=Array.isArray,$={H:null,A:null,T:null,S:null,V:null},At=Object.prototype.hasOwnProperty;function xt(g,M,G,Y,Z,rt){return G=rt.ref,{$$typeof:u,type:g,key:M,ref:G!==void 0?G:null,props:rt}}function jt(g,M){return xt(g.type,M,void 0,void 0,void 0,g.props)}function ie(g){return typeof g=="object"&&g!==null&&g.$$typeof===u}function Pn(g){var M={"=":"=0",":":"=2"};return"$"+g.replace(/[=:]/g,function(G){return M[G]})}var Ye=/\/+/g;function Qt(g,M){return typeof g=="object"&&g!==null&&g.key!=null?Pn(""+g.key):M.toString(36)}function wn(){}function Dn(g){switch(g.status){case"fulfilled":return g.value;case"rejected":throw g.reason;default:switch(typeof g.status=="string"?g.then(wn,wn):(g.status="pending",g.then(function(M){g.status==="pending"&&(g.status="fulfilled",g.value=M)},function(M){g.status==="pending"&&(g.status="rejected",g.reason=M)})),g.status){case"fulfilled":return g.value;case"rejected":throw g.reason}}throw g}function Zt(g,M,G,Y,Z){var rt=typeof g;(rt==="undefined"||rt==="boolean")&&(g=null);var P=!1;if(g===null)P=!0;else switch(rt){case"bigint":case"string":case"number":P=!0;break;case"object":switch(g.$$typeof){case u:case a:P=!0;break;case b:return P=g._init,Zt(P(g._payload),M,G,Y,Z)}}if(P)return Z=Z(g),P=Y===""?"."+Qt(g,0):Y,Ot(Z)?(G="",P!=null&&(G=P.replace(Ye,"$&/")+"/"),Zt(Z,M,G,"",function(nn){return nn})):Z!=null&&(ie(Z)&&(Z=jt(Z,G+(Z.key==null||g&&g.key===Z.key?"":(""+Z.key).replace(Ye,"$&/")+"/")+P)),M.push(Z)),1;P=0;var ue=Y===""?".":Y+":";if(Ot(g))for(var St=0;St<g.length;St++)Y=g[St],rt=ue+Qt(Y,St),P+=Zt(Y,M,G,rt,Z);else if(St=z(g),typeof St=="function")for(g=St.call(g),St=0;!(Y=g.next()).done;)Y=Y.value,rt=ue+Qt(Y,St++),P+=Zt(Y,M,G,rt,Z);else if(rt==="object"){if(typeof g.then=="function")return Zt(Dn(g),M,G,Y,Z);throw M=String(g),Error("Objects are not valid as a React child (found: "+(M==="[object Object]"?"object with keys {"+Object.keys(g).join(", ")+"}":M)+"). If you meant to render a collection of children, use an array instead.")}return P}function D(g,M,G){if(g==null)return g;var Y=[],Z=0;return Zt(g,Y,"","",function(rt){return M.call(G,rt,Z++)}),Y}function X(g){if(g._status===-1){var M=g._result;M=M(),M.then(function(G){(g._status===0||g._status===-1)&&(g._status=1,g._result=G)},function(G){(g._status===0||g._status===-1)&&(g._status=2,g._result=G)}),g._status===-1&&(g._status=0,g._result=M)}if(g._status===1)return g._result.default;throw g._result}var W=typeof reportError=="function"?reportError:function(g){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var M=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof g=="object"&&g!==null&&typeof g.message=="string"?String(g.message):String(g),error:g});if(!window.dispatchEvent(M))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",g);return}console.error(g)};function gt(){}return et.Children={map:D,forEach:function(g,M,G){D(g,function(){M.apply(this,arguments)},G)},count:function(g){var M=0;return D(g,function(){M++}),M},toArray:function(g){return D(g,function(M){return M})||[]},only:function(g){if(!ie(g))throw Error("React.Children.only expected to receive a single React element child.");return g}},et.Component=j,et.Fragment=s,et.Profiler=f,et.PureComponent=I,et.StrictMode=c,et.Suspense=T,et.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=$,et.__COMPILER_RUNTIME={__proto__:null,c:function(g){return $.H.useMemoCache(g)}},et.cache=function(g){return function(){return g.apply(null,arguments)}},et.cloneElement=function(g,M,G){if(g==null)throw Error("The argument must be a React element, but you passed "+g+".");var Y=B({},g.props),Z=g.key,rt=void 0;if(M!=null)for(P in M.ref!==void 0&&(rt=void 0),M.key!==void 0&&(Z=""+M.key),M)!At.call(M,P)||P==="key"||P==="__self"||P==="__source"||P==="ref"&&M.ref===void 0||(Y[P]=M[P]);var P=arguments.length-2;if(P===1)Y.children=G;else if(1<P){for(var ue=Array(P),St=0;St<P;St++)ue[St]=arguments[St+2];Y.children=ue}return xt(g.type,Z,void 0,void 0,rt,Y)},et.createContext=function(g){return g={$$typeof:y,_currentValue:g,_currentValue2:g,_threadCount:0,Provider:null,Consumer:null},g.Provider=g,g.Consumer={$$typeof:h,_context:g},g},et.createElement=function(g,M,G){var Y,Z={},rt=null;if(M!=null)for(Y in M.key!==void 0&&(rt=""+M.key),M)At.call(M,Y)&&Y!=="key"&&Y!=="__self"&&Y!=="__source"&&(Z[Y]=M[Y]);var P=arguments.length-2;if(P===1)Z.children=G;else if(1<P){for(var ue=Array(P),St=0;St<P;St++)ue[St]=arguments[St+2];Z.children=ue}if(g&&g.defaultProps)for(Y in P=g.defaultProps,P)Z[Y]===void 0&&(Z[Y]=P[Y]);return xt(g,rt,void 0,void 0,null,Z)},et.createRef=function(){return{current:null}},et.forwardRef=function(g){return{$$typeof:S,render:g}},et.isValidElement=ie,et.lazy=function(g){return{$$typeof:b,_payload:{_status:-1,_result:g},_init:X}},et.memo=function(g,M){return{$$typeof:p,type:g,compare:M===void 0?null:M}},et.startTransition=function(g){var M=$.T,G={};$.T=G;try{var Y=g(),Z=$.S;Z!==null&&Z(G,Y),typeof Y=="object"&&Y!==null&&typeof Y.then=="function"&&Y.then(gt,W)}catch(rt){W(rt)}finally{$.T=M}},et.unstable_useCacheRefresh=function(){return $.H.useCacheRefresh()},et.use=function(g){return $.H.use(g)},et.useActionState=function(g,M,G){return $.H.useActionState(g,M,G)},et.useCallback=function(g,M){return $.H.useCallback(g,M)},et.useContext=function(g){return $.H.useContext(g)},et.useDebugValue=function(){},et.useDeferredValue=function(g,M){return $.H.useDeferredValue(g,M)},et.useEffect=function(g,M,G){var Y=$.H;if(typeof G=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return Y.useEffect(g,M)},et.useId=function(){return $.H.useId()},et.useImperativeHandle=function(g,M,G){return $.H.useImperativeHandle(g,M,G)},et.useInsertionEffect=function(g,M){return $.H.useInsertionEffect(g,M)},et.useLayoutEffect=function(g,M){return $.H.useLayoutEffect(g,M)},et.useMemo=function(g,M){return $.H.useMemo(g,M)},et.useOptimistic=function(g,M){return $.H.useOptimistic(g,M)},et.useReducer=function(g,M,G){return $.H.useReducer(g,M,G)},et.useRef=function(g){return $.H.useRef(g)},et.useState=function(g){return $.H.useState(g)},et.useSyncExternalStore=function(g,M,G){return $.H.useSyncExternalStore(g,M,G)},et.useTransition=function(){return $.H.useTransition()},et.version="19.1.0",et}var xh;function Er(){return xh||(xh=1,Wc.exports=m0()),Wc.exports}var Xt=Er();const Nh=d0(Xt);var Fc={exports:{}},Xa={},$c={exports:{}},Pc={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wh;function p0(){return wh||(wh=1,function(u){function a(D,X){var W=D.length;D.push(X);t:for(;0<W;){var gt=W-1>>>1,g=D[gt];if(0<f(g,X))D[gt]=X,D[W]=g,W=gt;else break t}}function s(D){return D.length===0?null:D[0]}function c(D){if(D.length===0)return null;var X=D[0],W=D.pop();if(W!==X){D[0]=W;t:for(var gt=0,g=D.length,M=g>>>1;gt<M;){var G=2*(gt+1)-1,Y=D[G],Z=G+1,rt=D[Z];if(0>f(Y,W))Z<g&&0>f(rt,Y)?(D[gt]=rt,D[Z]=W,gt=Z):(D[gt]=Y,D[G]=W,gt=G);else if(Z<g&&0>f(rt,W))D[gt]=rt,D[Z]=W,gt=Z;else break t}}return X}function f(D,X){var W=D.sortIndex-X.sortIndex;return W!==0?W:D.id-X.id}if(u.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var h=performance;u.unstable_now=function(){return h.now()}}else{var y=Date,S=y.now();u.unstable_now=function(){return y.now()-S}}var T=[],p=[],b=1,N=null,z=3,H=!1,B=!1,q=!1,j=!1,Q=typeof setTimeout=="function"?setTimeout:null,I=typeof clearTimeout=="function"?clearTimeout:null,nt=typeof setImmediate<"u"?setImmediate:null;function Ot(D){for(var X=s(p);X!==null;){if(X.callback===null)c(p);else if(X.startTime<=D)c(p),X.sortIndex=X.expirationTime,a(T,X);else break;X=s(p)}}function $(D){if(q=!1,Ot(D),!B)if(s(T)!==null)B=!0,At||(At=!0,Qt());else{var X=s(p);X!==null&&Zt($,X.startTime-D)}}var At=!1,xt=-1,jt=5,ie=-1;function Pn(){return j?!0:!(u.unstable_now()-ie<jt)}function Ye(){if(j=!1,At){var D=u.unstable_now();ie=D;var X=!0;try{t:{B=!1,q&&(q=!1,I(xt),xt=-1),H=!0;var W=z;try{e:{for(Ot(D),N=s(T);N!==null&&!(N.expirationTime>D&&Pn());){var gt=N.callback;if(typeof gt=="function"){N.callback=null,z=N.priorityLevel;var g=gt(N.expirationTime<=D);if(D=u.unstable_now(),typeof g=="function"){N.callback=g,Ot(D),X=!0;break e}N===s(T)&&c(T),Ot(D)}else c(T);N=s(T)}if(N!==null)X=!0;else{var M=s(p);M!==null&&Zt($,M.startTime-D),X=!1}}break t}finally{N=null,z=W,H=!1}X=void 0}}finally{X?Qt():At=!1}}}var Qt;if(typeof nt=="function")Qt=function(){nt(Ye)};else if(typeof MessageChannel<"u"){var wn=new MessageChannel,Dn=wn.port2;wn.port1.onmessage=Ye,Qt=function(){Dn.postMessage(null)}}else Qt=function(){Q(Ye,0)};function Zt(D,X){xt=Q(function(){D(u.unstable_now())},X)}u.unstable_IdlePriority=5,u.unstable_ImmediatePriority=1,u.unstable_LowPriority=4,u.unstable_NormalPriority=3,u.unstable_Profiling=null,u.unstable_UserBlockingPriority=2,u.unstable_cancelCallback=function(D){D.callback=null},u.unstable_forceFrameRate=function(D){0>D||125<D?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):jt=0<D?Math.floor(1e3/D):5},u.unstable_getCurrentPriorityLevel=function(){return z},u.unstable_next=function(D){switch(z){case 1:case 2:case 3:var X=3;break;default:X=z}var W=z;z=X;try{return D()}finally{z=W}},u.unstable_requestPaint=function(){j=!0},u.unstable_runWithPriority=function(D,X){switch(D){case 1:case 2:case 3:case 4:case 5:break;default:D=3}var W=z;z=D;try{return X()}finally{z=W}},u.unstable_scheduleCallback=function(D,X,W){var gt=u.unstable_now();switch(typeof W=="object"&&W!==null?(W=W.delay,W=typeof W=="number"&&0<W?gt+W:gt):W=gt,D){case 1:var g=-1;break;case 2:g=250;break;case 5:g=1073741823;break;case 4:g=1e4;break;default:g=5e3}return g=W+g,D={id:b++,callback:X,priorityLevel:D,startTime:W,expirationTime:g,sortIndex:-1},W>gt?(D.sortIndex=W,a(p,D),s(T)===null&&D===s(p)&&(q?(I(xt),xt=-1):q=!0,Zt($,W-gt))):(D.sortIndex=g,a(T,D),B||H||(B=!0,At||(At=!0,Qt()))),D},u.unstable_shouldYield=Pn,u.unstable_wrapCallback=function(D){var X=z;return function(){var W=z;z=X;try{return D.apply(this,arguments)}finally{z=W}}}}(Pc)),Pc}var Dh;function g0(){return Dh||(Dh=1,$c.exports=p0()),$c.exports}var Ic={exports:{}},Wt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ch;function v0(){if(Ch)return Wt;Ch=1;var u=Er();function a(T){var p="https://react.dev/errors/"+T;if(1<arguments.length){p+="?args[]="+encodeURIComponent(arguments[1]);for(var b=2;b<arguments.length;b++)p+="&args[]="+encodeURIComponent(arguments[b])}return"Minified React error #"+T+"; visit "+p+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(){}var c={d:{f:s,r:function(){throw Error(a(522))},D:s,C:s,L:s,m:s,X:s,S:s,M:s},p:0,findDOMNode:null},f=Symbol.for("react.portal");function h(T,p,b){var N=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:f,key:N==null?null:""+N,children:T,containerInfo:p,implementation:b}}var y=u.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function S(T,p){if(T==="font")return"";if(typeof p=="string")return p==="use-credentials"?p:""}return Wt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=c,Wt.createPortal=function(T,p){var b=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!p||p.nodeType!==1&&p.nodeType!==9&&p.nodeType!==11)throw Error(a(299));return h(T,p,null,b)},Wt.flushSync=function(T){var p=y.T,b=c.p;try{if(y.T=null,c.p=2,T)return T()}finally{y.T=p,c.p=b,c.d.f()}},Wt.preconnect=function(T,p){typeof T=="string"&&(p?(p=p.crossOrigin,p=typeof p=="string"?p==="use-credentials"?p:"":void 0):p=null,c.d.C(T,p))},Wt.prefetchDNS=function(T){typeof T=="string"&&c.d.D(T)},Wt.preinit=function(T,p){if(typeof T=="string"&&p&&typeof p.as=="string"){var b=p.as,N=S(b,p.crossOrigin),z=typeof p.integrity=="string"?p.integrity:void 0,H=typeof p.fetchPriority=="string"?p.fetchPriority:void 0;b==="style"?c.d.S(T,typeof p.precedence=="string"?p.precedence:void 0,{crossOrigin:N,integrity:z,fetchPriority:H}):b==="script"&&c.d.X(T,{crossOrigin:N,integrity:z,fetchPriority:H,nonce:typeof p.nonce=="string"?p.nonce:void 0})}},Wt.preinitModule=function(T,p){if(typeof T=="string")if(typeof p=="object"&&p!==null){if(p.as==null||p.as==="script"){var b=S(p.as,p.crossOrigin);c.d.M(T,{crossOrigin:b,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0})}}else p==null&&c.d.M(T)},Wt.preload=function(T,p){if(typeof T=="string"&&typeof p=="object"&&p!==null&&typeof p.as=="string"){var b=p.as,N=S(b,p.crossOrigin);c.d.L(T,b,{crossOrigin:N,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0,type:typeof p.type=="string"?p.type:void 0,fetchPriority:typeof p.fetchPriority=="string"?p.fetchPriority:void 0,referrerPolicy:typeof p.referrerPolicy=="string"?p.referrerPolicy:void 0,imageSrcSet:typeof p.imageSrcSet=="string"?p.imageSrcSet:void 0,imageSizes:typeof p.imageSizes=="string"?p.imageSizes:void 0,media:typeof p.media=="string"?p.media:void 0})}},Wt.preloadModule=function(T,p){if(typeof T=="string")if(p){var b=S(p.as,p.crossOrigin);c.d.m(T,{as:typeof p.as=="string"&&p.as!=="script"?p.as:void 0,crossOrigin:b,integrity:typeof p.integrity=="string"?p.integrity:void 0})}else c.d.m(T)},Wt.requestFormReset=function(T){c.d.r(T)},Wt.unstable_batchedUpdates=function(T,p){return T(p)},Wt.useFormState=function(T,p,b){return y.H.useFormState(T,p,b)},Wt.useFormStatus=function(){return y.H.useHostTransitionStatus()},Wt.version="19.1.0",Wt}var Uh;function b0(){if(Uh)return Ic.exports;Uh=1;function u(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(u)}catch(a){console.error(a)}}return u(),Ic.exports=v0(),Ic.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zh;function S0(){if(zh)return Xa;zh=1;var u=g0(),a=Er(),s=b0();function c(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function f(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function h(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function y(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function S(t){if(h(t)!==t)throw Error(c(188))}function T(t){var e=t.alternate;if(!e){if(e=h(t),e===null)throw Error(c(188));return e!==t?null:t}for(var n=t,l=e;;){var i=n.return;if(i===null)break;var r=i.alternate;if(r===null){if(l=i.return,l!==null){n=l;continue}break}if(i.child===r.child){for(r=i.child;r;){if(r===n)return S(i),t;if(r===l)return S(i),e;r=r.sibling}throw Error(c(188))}if(n.return!==l.return)n=i,l=r;else{for(var o=!1,d=i.child;d;){if(d===n){o=!0,n=i,l=r;break}if(d===l){o=!0,l=i,n=r;break}d=d.sibling}if(!o){for(d=r.child;d;){if(d===n){o=!0,n=r,l=i;break}if(d===l){o=!0,l=r,n=i;break}d=d.sibling}if(!o)throw Error(c(189))}}if(n.alternate!==l)throw Error(c(190))}if(n.tag!==3)throw Error(c(188));return n.stateNode.current===n?t:e}function p(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=p(t),e!==null)return e;t=t.sibling}return null}var b=Object.assign,N=Symbol.for("react.element"),z=Symbol.for("react.transitional.element"),H=Symbol.for("react.portal"),B=Symbol.for("react.fragment"),q=Symbol.for("react.strict_mode"),j=Symbol.for("react.profiler"),Q=Symbol.for("react.provider"),I=Symbol.for("react.consumer"),nt=Symbol.for("react.context"),Ot=Symbol.for("react.forward_ref"),$=Symbol.for("react.suspense"),At=Symbol.for("react.suspense_list"),xt=Symbol.for("react.memo"),jt=Symbol.for("react.lazy"),ie=Symbol.for("react.activity"),Pn=Symbol.for("react.memo_cache_sentinel"),Ye=Symbol.iterator;function Qt(t){return t===null||typeof t!="object"?null:(t=Ye&&t[Ye]||t["@@iterator"],typeof t=="function"?t:null)}var wn=Symbol.for("react.client.reference");function Dn(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===wn?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case B:return"Fragment";case j:return"Profiler";case q:return"StrictMode";case $:return"Suspense";case At:return"SuspenseList";case ie:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case H:return"Portal";case nt:return(t.displayName||"Context")+".Provider";case I:return(t._context.displayName||"Context")+".Consumer";case Ot:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case xt:return e=t.displayName||null,e!==null?e:Dn(t.type)||"Memo";case jt:e=t._payload,t=t._init;try{return Dn(t(e))}catch{}}return null}var Zt=Array.isArray,D=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,X=s.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,W={pending:!1,data:null,method:null,action:null},gt=[],g=-1;function M(t){return{current:t}}function G(t){0>g||(t.current=gt[g],gt[g]=null,g--)}function Y(t,e){g++,gt[g]=t.current,t.current=e}var Z=M(null),rt=M(null),P=M(null),ue=M(null);function St(t,e){switch(Y(P,e),Y(rt,t),Y(Z,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?Pd(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=Pd(e),t=Id(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}G(Z),Y(Z,t)}function nn(){G(Z),G(rt),G(P)}function zu(t){t.memoizedState!==null&&Y(ue,t);var e=Z.current,n=Id(e,t.type);e!==n&&(Y(rt,t),Y(Z,n))}function Wa(t){rt.current===t&&(G(Z),G(rt)),ue.current===t&&(G(ue),Ba._currentValue=W)}var Mu=Object.prototype.hasOwnProperty,Bu=u.unstable_scheduleCallback,ju=u.unstable_cancelCallback,ky=u.unstable_shouldYield,Qy=u.unstable_requestPaint,Ce=u.unstable_now,Zy=u.unstable_getCurrentPriorityLevel,Dr=u.unstable_ImmediatePriority,Cr=u.unstable_UserBlockingPriority,Fa=u.unstable_NormalPriority,Ky=u.unstable_LowPriority,Ur=u.unstable_IdlePriority,Jy=u.log,Wy=u.unstable_setDisableYieldValue,Vl=null,se=null;function ln(t){if(typeof Jy=="function"&&Wy(t),se&&typeof se.setStrictMode=="function")try{se.setStrictMode(Vl,t)}catch{}}var ce=Math.clz32?Math.clz32:Py,Fy=Math.log,$y=Math.LN2;function Py(t){return t>>>=0,t===0?32:31-(Fy(t)/$y|0)|0}var $a=256,Pa=4194304;function Cn(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Ia(t,e,n){var l=t.pendingLanes;if(l===0)return 0;var i=0,r=t.suspendedLanes,o=t.pingedLanes;t=t.warmLanes;var d=l&134217727;return d!==0?(l=d&~r,l!==0?i=Cn(l):(o&=d,o!==0?i=Cn(o):n||(n=d&~t,n!==0&&(i=Cn(n))))):(d=l&~r,d!==0?i=Cn(d):o!==0?i=Cn(o):n||(n=l&~t,n!==0&&(i=Cn(n)))),i===0?0:e!==0&&e!==i&&(e&r)===0&&(r=i&-i,n=e&-e,r>=n||r===32&&(n&4194048)!==0)?e:i}function kl(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function Iy(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function zr(){var t=$a;return $a<<=1,($a&4194048)===0&&($a=256),t}function Mr(){var t=Pa;return Pa<<=1,(Pa&62914560)===0&&(Pa=4194304),t}function qu(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function Ql(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function tm(t,e,n,l,i,r){var o=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var d=t.entanglements,m=t.expirationTimes,A=t.hiddenUpdates;for(n=o&~n;0<n;){var w=31-ce(n),U=1<<w;d[w]=0,m[w]=-1;var R=A[w];if(R!==null)for(A[w]=null,w=0;w<R.length;w++){var O=R[w];O!==null&&(O.lane&=-536870913)}n&=~U}l!==0&&Br(t,l,0),r!==0&&i===0&&t.tag!==0&&(t.suspendedLanes|=r&~(o&~e))}function Br(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var l=31-ce(e);t.entangledLanes|=e,t.entanglements[l]=t.entanglements[l]|1073741824|n&4194090}function jr(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var l=31-ce(n),i=1<<l;i&e|t[l]&e&&(t[l]|=e),n&=~i}}function Lu(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function Hu(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function qr(){var t=X.p;return t!==0?t:(t=window.event,t===void 0?32:vh(t.type))}function em(t,e){var n=X.p;try{return X.p=t,e()}finally{X.p=n}}var an=Math.random().toString(36).slice(2),Kt="__reactFiber$"+an,Pt="__reactProps$"+an,In="__reactContainer$"+an,Yu="__reactEvents$"+an,nm="__reactListeners$"+an,lm="__reactHandles$"+an,Lr="__reactResources$"+an,Zl="__reactMarker$"+an;function Xu(t){delete t[Kt],delete t[Pt],delete t[Yu],delete t[nm],delete t[lm]}function tl(t){var e=t[Kt];if(e)return e;for(var n=t.parentNode;n;){if(e=n[In]||n[Kt]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=lh(t);t!==null;){if(n=t[Kt])return n;t=lh(t)}return e}t=n,n=t.parentNode}return null}function el(t){if(t=t[Kt]||t[In]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Kl(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(c(33))}function nl(t){var e=t[Lr];return e||(e=t[Lr]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function qt(t){t[Zl]=!0}var Hr=new Set,Yr={};function Un(t,e){ll(t,e),ll(t+"Capture",e)}function ll(t,e){for(Yr[t]=e,t=0;t<e.length;t++)Hr.add(e[t])}var am=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Xr={},Gr={};function im(t){return Mu.call(Gr,t)?!0:Mu.call(Xr,t)?!1:am.test(t)?Gr[t]=!0:(Xr[t]=!0,!1)}function ti(t,e,n){if(im(e))if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var l=e.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+n)}}function ei(t,e,n){if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+n)}}function Xe(t,e,n,l){if(l===null)t.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttributeNS(e,n,""+l)}}var Gu,Vr;function al(t){if(Gu===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);Gu=e&&e[1]||"",Vr=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Gu+t+Vr}var Vu=!1;function ku(t,e){if(!t||Vu)return"";Vu=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(e){var U=function(){throw Error()};if(Object.defineProperty(U.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(U,[])}catch(O){var R=O}Reflect.construct(t,[],U)}else{try{U.call()}catch(O){R=O}t.call(U.prototype)}}else{try{throw Error()}catch(O){R=O}(U=t())&&typeof U.catch=="function"&&U.catch(function(){})}}catch(O){if(O&&R&&typeof O.stack=="string")return[O.stack,R.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var r=l.DetermineComponentFrameRoot(),o=r[0],d=r[1];if(o&&d){var m=o.split(`
`),A=d.split(`
`);for(i=l=0;l<m.length&&!m[l].includes("DetermineComponentFrameRoot");)l++;for(;i<A.length&&!A[i].includes("DetermineComponentFrameRoot");)i++;if(l===m.length||i===A.length)for(l=m.length-1,i=A.length-1;1<=l&&0<=i&&m[l]!==A[i];)i--;for(;1<=l&&0<=i;l--,i--)if(m[l]!==A[i]){if(l!==1||i!==1)do if(l--,i--,0>i||m[l]!==A[i]){var w=`
`+m[l].replace(" at new "," at ");return t.displayName&&w.includes("<anonymous>")&&(w=w.replace("<anonymous>",t.displayName)),w}while(1<=l&&0<=i);break}}}finally{Vu=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?al(n):""}function um(t){switch(t.tag){case 26:case 27:case 5:return al(t.type);case 16:return al("Lazy");case 13:return al("Suspense");case 19:return al("SuspenseList");case 0:case 15:return ku(t.type,!1);case 11:return ku(t.type.render,!1);case 1:return ku(t.type,!0);case 31:return al("Activity");default:return""}}function kr(t){try{var e="";do e+=um(t),t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function pe(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Qr(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function sm(t){var e=Qr(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),l=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,r=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return i.call(this)},set:function(o){l=""+o,r.call(this,o)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(o){l=""+o},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function ni(t){t._valueTracker||(t._valueTracker=sm(t))}function Zr(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),l="";return t&&(l=Qr(t)?t.checked?"true":"false":t.value),t=l,t!==n?(e.setValue(t),!0):!1}function li(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var cm=/[\n"\\]/g;function ge(t){return t.replace(cm,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Qu(t,e,n,l,i,r,o,d){t.name="",o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"?t.type=o:t.removeAttribute("type"),e!=null?o==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+pe(e)):t.value!==""+pe(e)&&(t.value=""+pe(e)):o!=="submit"&&o!=="reset"||t.removeAttribute("value"),e!=null?Zu(t,o,pe(e)):n!=null?Zu(t,o,pe(n)):l!=null&&t.removeAttribute("value"),i==null&&r!=null&&(t.defaultChecked=!!r),i!=null&&(t.checked=i&&typeof i!="function"&&typeof i!="symbol"),d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"?t.name=""+pe(d):t.removeAttribute("name")}function Kr(t,e,n,l,i,r,o,d){if(r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"&&(t.type=r),e!=null||n!=null){if(!(r!=="submit"&&r!=="reset"||e!=null))return;n=n!=null?""+pe(n):"",e=e!=null?""+pe(e):n,d||e===t.value||(t.value=e),t.defaultValue=e}l=l??i,l=typeof l!="function"&&typeof l!="symbol"&&!!l,t.checked=d?t.checked:!!l,t.defaultChecked=!!l,o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"&&(t.name=o)}function Zu(t,e,n){e==="number"&&li(t.ownerDocument)===t||t.defaultValue===""+n||(t.defaultValue=""+n)}function il(t,e,n,l){if(t=t.options,e){e={};for(var i=0;i<n.length;i++)e["$"+n[i]]=!0;for(n=0;n<t.length;n++)i=e.hasOwnProperty("$"+t[n].value),t[n].selected!==i&&(t[n].selected=i),i&&l&&(t[n].defaultSelected=!0)}else{for(n=""+pe(n),e=null,i=0;i<t.length;i++){if(t[i].value===n){t[i].selected=!0,l&&(t[i].defaultSelected=!0);return}e!==null||t[i].disabled||(e=t[i])}e!==null&&(e.selected=!0)}}function Jr(t,e,n){if(e!=null&&(e=""+pe(e),e!==t.value&&(t.value=e),n==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=n!=null?""+pe(n):""}function Wr(t,e,n,l){if(e==null){if(l!=null){if(n!=null)throw Error(c(92));if(Zt(l)){if(1<l.length)throw Error(c(93));l=l[0]}n=l}n==null&&(n=""),e=n}n=pe(e),t.defaultValue=n,l=t.textContent,l===n&&l!==""&&l!==null&&(t.value=l)}function ul(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var rm=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Fr(t,e,n){var l=e.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?l?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":l?t.setProperty(e,n):typeof n!="number"||n===0||rm.has(e)?e==="float"?t.cssFloat=n:t[e]=(""+n).trim():t[e]=n+"px"}function $r(t,e,n){if(e!=null&&typeof e!="object")throw Error(c(62));if(t=t.style,n!=null){for(var l in n)!n.hasOwnProperty(l)||e!=null&&e.hasOwnProperty(l)||(l.indexOf("--")===0?t.setProperty(l,""):l==="float"?t.cssFloat="":t[l]="");for(var i in e)l=e[i],e.hasOwnProperty(i)&&n[i]!==l&&Fr(t,i,l)}else for(var r in e)e.hasOwnProperty(r)&&Fr(t,r,e[r])}function Ku(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var om=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),fm=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function ai(t){return fm.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Ju=null;function Wu(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var sl=null,cl=null;function Pr(t){var e=el(t);if(e&&(t=e.stateNode)){var n=t[Pt]||null;t:switch(t=e.stateNode,e.type){case"input":if(Qu(t,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+ge(""+e)+'"][type="radio"]'),e=0;e<n.length;e++){var l=n[e];if(l!==t&&l.form===t.form){var i=l[Pt]||null;if(!i)throw Error(c(90));Qu(l,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(e=0;e<n.length;e++)l=n[e],l.form===t.form&&Zr(l)}break t;case"textarea":Jr(t,n.value,n.defaultValue);break t;case"select":e=n.value,e!=null&&il(t,!!n.multiple,e,!1)}}}var Fu=!1;function Ir(t,e,n){if(Fu)return t(e,n);Fu=!0;try{var l=t(e);return l}finally{if(Fu=!1,(sl!==null||cl!==null)&&(Vi(),sl&&(e=sl,t=cl,cl=sl=null,Pr(e),t)))for(e=0;e<t.length;e++)Pr(t[e])}}function Jl(t,e){var n=t.stateNode;if(n===null)return null;var l=n[Pt]||null;if(l===null)return null;n=l[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(t=t.type,l=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!l;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(c(231,e,typeof n));return n}var Ge=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),$u=!1;if(Ge)try{var Wl={};Object.defineProperty(Wl,"passive",{get:function(){$u=!0}}),window.addEventListener("test",Wl,Wl),window.removeEventListener("test",Wl,Wl)}catch{$u=!1}var un=null,Pu=null,ii=null;function to(){if(ii)return ii;var t,e=Pu,n=e.length,l,i="value"in un?un.value:un.textContent,r=i.length;for(t=0;t<n&&e[t]===i[t];t++);var o=n-t;for(l=1;l<=o&&e[n-l]===i[r-l];l++);return ii=i.slice(t,1<l?1-l:void 0)}function ui(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function si(){return!0}function eo(){return!1}function It(t){function e(n,l,i,r,o){this._reactName=n,this._targetInst=i,this.type=l,this.nativeEvent=r,this.target=o,this.currentTarget=null;for(var d in t)t.hasOwnProperty(d)&&(n=t[d],this[d]=n?n(r):r[d]);return this.isDefaultPrevented=(r.defaultPrevented!=null?r.defaultPrevented:r.returnValue===!1)?si:eo,this.isPropagationStopped=eo,this}return b(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=si)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=si)},persist:function(){},isPersistent:si}),e}var zn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ci=It(zn),Fl=b({},zn,{view:0,detail:0}),dm=It(Fl),Iu,ts,$l,ri=b({},Fl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ns,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==$l&&($l&&t.type==="mousemove"?(Iu=t.screenX-$l.screenX,ts=t.screenY-$l.screenY):ts=Iu=0,$l=t),Iu)},movementY:function(t){return"movementY"in t?t.movementY:ts}}),no=It(ri),hm=b({},ri,{dataTransfer:0}),ym=It(hm),mm=b({},Fl,{relatedTarget:0}),es=It(mm),pm=b({},zn,{animationName:0,elapsedTime:0,pseudoElement:0}),gm=It(pm),vm=b({},zn,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),bm=It(vm),Sm=b({},zn,{data:0}),lo=It(Sm),Em={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},_m={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Am={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Tm(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Am[t])?!!e[t]:!1}function ns(){return Tm}var Rm=b({},Fl,{key:function(t){if(t.key){var e=Em[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=ui(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?_m[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ns,charCode:function(t){return t.type==="keypress"?ui(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?ui(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Om=It(Rm),xm=b({},ri,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),ao=It(xm),Nm=b({},Fl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ns}),wm=It(Nm),Dm=b({},zn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Cm=It(Dm),Um=b({},ri,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),zm=It(Um),Mm=b({},zn,{newState:0,oldState:0}),Bm=It(Mm),jm=[9,13,27,32],ls=Ge&&"CompositionEvent"in window,Pl=null;Ge&&"documentMode"in document&&(Pl=document.documentMode);var qm=Ge&&"TextEvent"in window&&!Pl,io=Ge&&(!ls||Pl&&8<Pl&&11>=Pl),uo=" ",so=!1;function co(t,e){switch(t){case"keyup":return jm.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ro(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var rl=!1;function Lm(t,e){switch(t){case"compositionend":return ro(e);case"keypress":return e.which!==32?null:(so=!0,uo);case"textInput":return t=e.data,t===uo&&so?null:t;default:return null}}function Hm(t,e){if(rl)return t==="compositionend"||!ls&&co(t,e)?(t=to(),ii=Pu=un=null,rl=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return io&&e.locale!=="ko"?null:e.data;default:return null}}var Ym={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function oo(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Ym[t.type]:e==="textarea"}function fo(t,e,n,l){sl?cl?cl.push(l):cl=[l]:sl=l,e=Wi(e,"onChange"),0<e.length&&(n=new ci("onChange","change",null,n,l),t.push({event:n,listeners:e}))}var Il=null,ta=null;function Xm(t){Kd(t,0)}function oi(t){var e=Kl(t);if(Zr(e))return t}function ho(t,e){if(t==="change")return e}var yo=!1;if(Ge){var as;if(Ge){var is="oninput"in document;if(!is){var mo=document.createElement("div");mo.setAttribute("oninput","return;"),is=typeof mo.oninput=="function"}as=is}else as=!1;yo=as&&(!document.documentMode||9<document.documentMode)}function po(){Il&&(Il.detachEvent("onpropertychange",go),ta=Il=null)}function go(t){if(t.propertyName==="value"&&oi(ta)){var e=[];fo(e,ta,t,Wu(t)),Ir(Xm,e)}}function Gm(t,e,n){t==="focusin"?(po(),Il=e,ta=n,Il.attachEvent("onpropertychange",go)):t==="focusout"&&po()}function Vm(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return oi(ta)}function km(t,e){if(t==="click")return oi(e)}function Qm(t,e){if(t==="input"||t==="change")return oi(e)}function Zm(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var re=typeof Object.is=="function"?Object.is:Zm;function ea(t,e){if(re(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),l=Object.keys(e);if(n.length!==l.length)return!1;for(l=0;l<n.length;l++){var i=n[l];if(!Mu.call(e,i)||!re(t[i],e[i]))return!1}return!0}function vo(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function bo(t,e){var n=vo(t);t=0;for(var l;n;){if(n.nodeType===3){if(l=t+n.textContent.length,t<=e&&l>=e)return{node:n,offset:e-t};t=l}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=vo(n)}}function So(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?So(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Eo(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=li(t.document);e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=li(t.document)}return e}function us(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var Km=Ge&&"documentMode"in document&&11>=document.documentMode,ol=null,ss=null,na=null,cs=!1;function _o(t,e,n){var l=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;cs||ol==null||ol!==li(l)||(l=ol,"selectionStart"in l&&us(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),na&&ea(na,l)||(na=l,l=Wi(ss,"onSelect"),0<l.length&&(e=new ci("onSelect","select",null,e,n),t.push({event:e,listeners:l}),e.target=ol)))}function Mn(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var fl={animationend:Mn("Animation","AnimationEnd"),animationiteration:Mn("Animation","AnimationIteration"),animationstart:Mn("Animation","AnimationStart"),transitionrun:Mn("Transition","TransitionRun"),transitionstart:Mn("Transition","TransitionStart"),transitioncancel:Mn("Transition","TransitionCancel"),transitionend:Mn("Transition","TransitionEnd")},rs={},Ao={};Ge&&(Ao=document.createElement("div").style,"AnimationEvent"in window||(delete fl.animationend.animation,delete fl.animationiteration.animation,delete fl.animationstart.animation),"TransitionEvent"in window||delete fl.transitionend.transition);function Bn(t){if(rs[t])return rs[t];if(!fl[t])return t;var e=fl[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in Ao)return rs[t]=e[n];return t}var To=Bn("animationend"),Ro=Bn("animationiteration"),Oo=Bn("animationstart"),Jm=Bn("transitionrun"),Wm=Bn("transitionstart"),Fm=Bn("transitioncancel"),xo=Bn("transitionend"),No=new Map,os="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");os.push("scrollEnd");function Oe(t,e){No.set(t,e),Un(e,[t])}var wo=new WeakMap;function ve(t,e){if(typeof t=="object"&&t!==null){var n=wo.get(t);return n!==void 0?n:(e={value:t,source:e,stack:kr(e)},wo.set(t,e),e)}return{value:t,source:e,stack:kr(e)}}var be=[],dl=0,fs=0;function fi(){for(var t=dl,e=fs=dl=0;e<t;){var n=be[e];be[e++]=null;var l=be[e];be[e++]=null;var i=be[e];be[e++]=null;var r=be[e];if(be[e++]=null,l!==null&&i!==null){var o=l.pending;o===null?i.next=i:(i.next=o.next,o.next=i),l.pending=i}r!==0&&Do(n,i,r)}}function di(t,e,n,l){be[dl++]=t,be[dl++]=e,be[dl++]=n,be[dl++]=l,fs|=l,t.lanes|=l,t=t.alternate,t!==null&&(t.lanes|=l)}function ds(t,e,n,l){return di(t,e,n,l),hi(t)}function hl(t,e){return di(t,null,null,e),hi(t)}function Do(t,e,n){t.lanes|=n;var l=t.alternate;l!==null&&(l.lanes|=n);for(var i=!1,r=t.return;r!==null;)r.childLanes|=n,l=r.alternate,l!==null&&(l.childLanes|=n),r.tag===22&&(t=r.stateNode,t===null||t._visibility&1||(i=!0)),t=r,r=r.return;return t.tag===3?(r=t.stateNode,i&&e!==null&&(i=31-ce(n),t=r.hiddenUpdates,l=t[i],l===null?t[i]=[e]:l.push(e),e.lane=n|536870912),r):null}function hi(t){if(50<xa)throw xa=0,vc=null,Error(c(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var yl={};function $m(t,e,n,l){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function oe(t,e,n,l){return new $m(t,e,n,l)}function hs(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Ve(t,e){var n=t.alternate;return n===null?(n=oe(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&65011712,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n.refCleanup=t.refCleanup,n}function Co(t,e){t.flags&=65011714;var n=t.alternate;return n===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function yi(t,e,n,l,i,r){var o=0;if(l=t,typeof t=="function")hs(t)&&(o=1);else if(typeof t=="string")o=Ip(t,n,Z.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case ie:return t=oe(31,n,e,i),t.elementType=ie,t.lanes=r,t;case B:return jn(n.children,i,r,e);case q:o=8,i|=24;break;case j:return t=oe(12,n,e,i|2),t.elementType=j,t.lanes=r,t;case $:return t=oe(13,n,e,i),t.elementType=$,t.lanes=r,t;case At:return t=oe(19,n,e,i),t.elementType=At,t.lanes=r,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case Q:case nt:o=10;break t;case I:o=9;break t;case Ot:o=11;break t;case xt:o=14;break t;case jt:o=16,l=null;break t}o=29,n=Error(c(130,t===null?"null":typeof t,"")),l=null}return e=oe(o,n,e,i),e.elementType=t,e.type=l,e.lanes=r,e}function jn(t,e,n,l){return t=oe(7,t,l,e),t.lanes=n,t}function ys(t,e,n){return t=oe(6,t,null,e),t.lanes=n,t}function ms(t,e,n){return e=oe(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var ml=[],pl=0,mi=null,pi=0,Se=[],Ee=0,qn=null,ke=1,Qe="";function Ln(t,e){ml[pl++]=pi,ml[pl++]=mi,mi=t,pi=e}function Uo(t,e,n){Se[Ee++]=ke,Se[Ee++]=Qe,Se[Ee++]=qn,qn=t;var l=ke;t=Qe;var i=32-ce(l)-1;l&=~(1<<i),n+=1;var r=32-ce(e)+i;if(30<r){var o=i-i%5;r=(l&(1<<o)-1).toString(32),l>>=o,i-=o,ke=1<<32-ce(e)+i|n<<i|l,Qe=r+t}else ke=1<<r|n<<i|l,Qe=t}function ps(t){t.return!==null&&(Ln(t,1),Uo(t,1,0))}function gs(t){for(;t===mi;)mi=ml[--pl],ml[pl]=null,pi=ml[--pl],ml[pl]=null;for(;t===qn;)qn=Se[--Ee],Se[Ee]=null,Qe=Se[--Ee],Se[Ee]=null,ke=Se[--Ee],Se[Ee]=null}var $t=null,Tt=null,ft=!1,Hn=null,Ue=!1,vs=Error(c(519));function Yn(t){var e=Error(c(418,""));throw ia(ve(e,t)),vs}function zo(t){var e=t.stateNode,n=t.type,l=t.memoizedProps;switch(e[Kt]=t,e[Pt]=l,n){case"dialog":ut("cancel",e),ut("close",e);break;case"iframe":case"object":case"embed":ut("load",e);break;case"video":case"audio":for(n=0;n<wa.length;n++)ut(wa[n],e);break;case"source":ut("error",e);break;case"img":case"image":case"link":ut("error",e),ut("load",e);break;case"details":ut("toggle",e);break;case"input":ut("invalid",e),Kr(e,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),ni(e);break;case"select":ut("invalid",e);break;case"textarea":ut("invalid",e),Wr(e,l.value,l.defaultValue,l.children),ni(e)}n=l.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||e.textContent===""+n||l.suppressHydrationWarning===!0||$d(e.textContent,n)?(l.popover!=null&&(ut("beforetoggle",e),ut("toggle",e)),l.onScroll!=null&&ut("scroll",e),l.onScrollEnd!=null&&ut("scrollend",e),l.onClick!=null&&(e.onclick=Fi),e=!0):e=!1,e||Yn(t)}function Mo(t){for($t=t.return;$t;)switch($t.tag){case 5:case 13:Ue=!1;return;case 27:case 3:Ue=!0;return;default:$t=$t.return}}function la(t){if(t!==$t)return!1;if(!ft)return Mo(t),ft=!0,!1;var e=t.tag,n;if((n=e!==3&&e!==27)&&((n=e===5)&&(n=t.type,n=!(n!=="form"&&n!=="button")||Mc(t.type,t.memoizedProps)),n=!n),n&&Tt&&Yn(t),Mo(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(c(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(n=t.data,n==="/$"){if(e===0){Tt=Ne(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++;t=t.nextSibling}Tt=null}}else e===27?(e=Tt,_n(t.type)?(t=Lc,Lc=null,Tt=t):Tt=e):Tt=$t?Ne(t.stateNode.nextSibling):null;return!0}function aa(){Tt=$t=null,ft=!1}function Bo(){var t=Hn;return t!==null&&(ne===null?ne=t:ne.push.apply(ne,t),Hn=null),t}function ia(t){Hn===null?Hn=[t]:Hn.push(t)}var bs=M(null),Xn=null,Ze=null;function sn(t,e,n){Y(bs,e._currentValue),e._currentValue=n}function Ke(t){t._currentValue=bs.current,G(bs)}function Ss(t,e,n){for(;t!==null;){var l=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,l!==null&&(l.childLanes|=e)):l!==null&&(l.childLanes&e)!==e&&(l.childLanes|=e),t===n)break;t=t.return}}function Es(t,e,n,l){var i=t.child;for(i!==null&&(i.return=t);i!==null;){var r=i.dependencies;if(r!==null){var o=i.child;r=r.firstContext;t:for(;r!==null;){var d=r;r=i;for(var m=0;m<e.length;m++)if(d.context===e[m]){r.lanes|=n,d=r.alternate,d!==null&&(d.lanes|=n),Ss(r.return,n,t),l||(o=null);break t}r=d.next}}else if(i.tag===18){if(o=i.return,o===null)throw Error(c(341));o.lanes|=n,r=o.alternate,r!==null&&(r.lanes|=n),Ss(o,n,t),o=null}else o=i.child;if(o!==null)o.return=i;else for(o=i;o!==null;){if(o===t){o=null;break}if(i=o.sibling,i!==null){i.return=o.return,o=i;break}o=o.return}i=o}}function ua(t,e,n,l){t=null;for(var i=e,r=!1;i!==null;){if(!r){if((i.flags&524288)!==0)r=!0;else if((i.flags&262144)!==0)break}if(i.tag===10){var o=i.alternate;if(o===null)throw Error(c(387));if(o=o.memoizedProps,o!==null){var d=i.type;re(i.pendingProps.value,o.value)||(t!==null?t.push(d):t=[d])}}else if(i===ue.current){if(o=i.alternate,o===null)throw Error(c(387));o.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(t!==null?t.push(Ba):t=[Ba])}i=i.return}t!==null&&Es(e,t,n,l),e.flags|=262144}function gi(t){for(t=t.firstContext;t!==null;){if(!re(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Gn(t){Xn=t,Ze=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function Jt(t){return jo(Xn,t)}function vi(t,e){return Xn===null&&Gn(t),jo(t,e)}function jo(t,e){var n=e._currentValue;if(e={context:e,memoizedValue:n,next:null},Ze===null){if(t===null)throw Error(c(308));Ze=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Ze=Ze.next=e;return n}var Pm=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(n,l){t.push(l)}};this.abort=function(){e.aborted=!0,t.forEach(function(n){return n()})}},Im=u.unstable_scheduleCallback,tp=u.unstable_NormalPriority,Mt={$$typeof:nt,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function _s(){return{controller:new Pm,data:new Map,refCount:0}}function sa(t){t.refCount--,t.refCount===0&&Im(tp,function(){t.controller.abort()})}var ca=null,As=0,gl=0,vl=null;function ep(t,e){if(ca===null){var n=ca=[];As=0,gl=Rc(),vl={status:"pending",value:void 0,then:function(l){n.push(l)}}}return As++,e.then(qo,qo),e}function qo(){if(--As===0&&ca!==null){vl!==null&&(vl.status="fulfilled");var t=ca;ca=null,gl=0,vl=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function np(t,e){var n=[],l={status:"pending",value:null,reason:null,then:function(i){n.push(i)}};return t.then(function(){l.status="fulfilled",l.value=e;for(var i=0;i<n.length;i++)(0,n[i])(e)},function(i){for(l.status="rejected",l.reason=i,i=0;i<n.length;i++)(0,n[i])(void 0)}),l}var Lo=D.S;D.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&ep(t,e),Lo!==null&&Lo(t,e)};var Vn=M(null);function Ts(){var t=Vn.current;return t!==null?t:bt.pooledCache}function bi(t,e){e===null?Y(Vn,Vn.current):Y(Vn,e.pool)}function Ho(){var t=Ts();return t===null?null:{parent:Mt._currentValue,pool:t}}var ra=Error(c(460)),Yo=Error(c(474)),Si=Error(c(542)),Rs={then:function(){}};function Xo(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Ei(){}function Go(t,e,n){switch(n=t[n],n===void 0?t.push(e):n!==e&&(e.then(Ei,Ei),e=n),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,ko(t),t;default:if(typeof e.status=="string")e.then(Ei,Ei);else{if(t=bt,t!==null&&100<t.shellSuspendCounter)throw Error(c(482));t=e,t.status="pending",t.then(function(l){if(e.status==="pending"){var i=e;i.status="fulfilled",i.value=l}},function(l){if(e.status==="pending"){var i=e;i.status="rejected",i.reason=l}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,ko(t),t}throw oa=e,ra}}var oa=null;function Vo(){if(oa===null)throw Error(c(459));var t=oa;return oa=null,t}function ko(t){if(t===ra||t===Si)throw Error(c(483))}var cn=!1;function Os(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function xs(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function rn(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function on(t,e,n){var l=t.updateQueue;if(l===null)return null;if(l=l.shared,(dt&2)!==0){var i=l.pending;return i===null?e.next=e:(e.next=i.next,i.next=e),l.pending=e,e=hi(t),Do(t,null,n),e}return di(t,l,e,n),hi(t)}function fa(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194048)!==0)){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,jr(t,n)}}function Ns(t,e){var n=t.updateQueue,l=t.alternate;if(l!==null&&(l=l.updateQueue,n===l)){var i=null,r=null;if(n=n.firstBaseUpdate,n!==null){do{var o={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};r===null?i=r=o:r=r.next=o,n=n.next}while(n!==null);r===null?i=r=e:r=r.next=e}else i=r=e;n={baseState:l.baseState,firstBaseUpdate:i,lastBaseUpdate:r,shared:l.shared,callbacks:l.callbacks},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}var ws=!1;function da(){if(ws){var t=vl;if(t!==null)throw t}}function ha(t,e,n,l){ws=!1;var i=t.updateQueue;cn=!1;var r=i.firstBaseUpdate,o=i.lastBaseUpdate,d=i.shared.pending;if(d!==null){i.shared.pending=null;var m=d,A=m.next;m.next=null,o===null?r=A:o.next=A,o=m;var w=t.alternate;w!==null&&(w=w.updateQueue,d=w.lastBaseUpdate,d!==o&&(d===null?w.firstBaseUpdate=A:d.next=A,w.lastBaseUpdate=m))}if(r!==null){var U=i.baseState;o=0,w=A=m=null,d=r;do{var R=d.lane&-536870913,O=R!==d.lane;if(O?(ct&R)===R:(l&R)===R){R!==0&&R===gl&&(ws=!0),w!==null&&(w=w.next={lane:0,tag:d.tag,payload:d.payload,callback:null,next:null});t:{var F=t,K=d;R=e;var pt=n;switch(K.tag){case 1:if(F=K.payload,typeof F=="function"){U=F.call(pt,U,R);break t}U=F;break t;case 3:F.flags=F.flags&-65537|128;case 0:if(F=K.payload,R=typeof F=="function"?F.call(pt,U,R):F,R==null)break t;U=b({},U,R);break t;case 2:cn=!0}}R=d.callback,R!==null&&(t.flags|=64,O&&(t.flags|=8192),O=i.callbacks,O===null?i.callbacks=[R]:O.push(R))}else O={lane:R,tag:d.tag,payload:d.payload,callback:d.callback,next:null},w===null?(A=w=O,m=U):w=w.next=O,o|=R;if(d=d.next,d===null){if(d=i.shared.pending,d===null)break;O=d,d=O.next,O.next=null,i.lastBaseUpdate=O,i.shared.pending=null}}while(!0);w===null&&(m=U),i.baseState=m,i.firstBaseUpdate=A,i.lastBaseUpdate=w,r===null&&(i.shared.lanes=0),vn|=o,t.lanes=o,t.memoizedState=U}}function Qo(t,e){if(typeof t!="function")throw Error(c(191,t));t.call(e)}function Zo(t,e){var n=t.callbacks;if(n!==null)for(t.callbacks=null,t=0;t<n.length;t++)Qo(n[t],e)}var bl=M(null),_i=M(0);function Ko(t,e){t=tn,Y(_i,t),Y(bl,e),tn=t|e.baseLanes}function Ds(){Y(_i,tn),Y(bl,bl.current)}function Cs(){tn=_i.current,G(bl),G(_i)}var fn=0,lt=null,yt=null,Ct=null,Ai=!1,Sl=!1,kn=!1,Ti=0,ya=0,El=null,lp=0;function Nt(){throw Error(c(321))}function Us(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!re(t[n],e[n]))return!1;return!0}function zs(t,e,n,l,i,r){return fn=r,lt=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,D.H=t===null||t.memoizedState===null?Cf:Uf,kn=!1,r=n(l,i),kn=!1,Sl&&(r=Wo(e,n,l,i)),Jo(t),r}function Jo(t){D.H=Di;var e=yt!==null&&yt.next!==null;if(fn=0,Ct=yt=lt=null,Ai=!1,ya=0,El=null,e)throw Error(c(300));t===null||Lt||(t=t.dependencies,t!==null&&gi(t)&&(Lt=!0))}function Wo(t,e,n,l){lt=t;var i=0;do{if(Sl&&(El=null),ya=0,Sl=!1,25<=i)throw Error(c(301));if(i+=1,Ct=yt=null,t.updateQueue!=null){var r=t.updateQueue;r.lastEffect=null,r.events=null,r.stores=null,r.memoCache!=null&&(r.memoCache.index=0)}D.H=op,r=e(n,l)}while(Sl);return r}function ap(){var t=D.H,e=t.useState()[0];return e=typeof e.then=="function"?ma(e):e,t=t.useState()[0],(yt!==null?yt.memoizedState:null)!==t&&(lt.flags|=1024),e}function Ms(){var t=Ti!==0;return Ti=0,t}function Bs(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function js(t){if(Ai){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}Ai=!1}fn=0,Ct=yt=lt=null,Sl=!1,ya=Ti=0,El=null}function te(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ct===null?lt.memoizedState=Ct=t:Ct=Ct.next=t,Ct}function Ut(){if(yt===null){var t=lt.alternate;t=t!==null?t.memoizedState:null}else t=yt.next;var e=Ct===null?lt.memoizedState:Ct.next;if(e!==null)Ct=e,yt=t;else{if(t===null)throw lt.alternate===null?Error(c(467)):Error(c(310));yt=t,t={memoizedState:yt.memoizedState,baseState:yt.baseState,baseQueue:yt.baseQueue,queue:yt.queue,next:null},Ct===null?lt.memoizedState=Ct=t:Ct=Ct.next=t}return Ct}function qs(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function ma(t){var e=ya;return ya+=1,El===null&&(El=[]),t=Go(El,t,e),e=lt,(Ct===null?e.memoizedState:Ct.next)===null&&(e=e.alternate,D.H=e===null||e.memoizedState===null?Cf:Uf),t}function Ri(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return ma(t);if(t.$$typeof===nt)return Jt(t)}throw Error(c(438,String(t)))}function Ls(t){var e=null,n=lt.updateQueue;if(n!==null&&(e=n.memoCache),e==null){var l=lt.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(e={data:l.data.map(function(i){return i.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),n===null&&(n=qs(),lt.updateQueue=n),n.memoCache=e,n=e.data[e.index],n===void 0)for(n=e.data[e.index]=Array(t),l=0;l<t;l++)n[l]=Pn;return e.index++,n}function Je(t,e){return typeof e=="function"?e(t):e}function Oi(t){var e=Ut();return Hs(e,yt,t)}function Hs(t,e,n){var l=t.queue;if(l===null)throw Error(c(311));l.lastRenderedReducer=n;var i=t.baseQueue,r=l.pending;if(r!==null){if(i!==null){var o=i.next;i.next=r.next,r.next=o}e.baseQueue=i=r,l.pending=null}if(r=t.baseState,i===null)t.memoizedState=r;else{e=i.next;var d=o=null,m=null,A=e,w=!1;do{var U=A.lane&-536870913;if(U!==A.lane?(ct&U)===U:(fn&U)===U){var R=A.revertLane;if(R===0)m!==null&&(m=m.next={lane:0,revertLane:0,action:A.action,hasEagerState:A.hasEagerState,eagerState:A.eagerState,next:null}),U===gl&&(w=!0);else if((fn&R)===R){A=A.next,R===gl&&(w=!0);continue}else U={lane:0,revertLane:A.revertLane,action:A.action,hasEagerState:A.hasEagerState,eagerState:A.eagerState,next:null},m===null?(d=m=U,o=r):m=m.next=U,lt.lanes|=R,vn|=R;U=A.action,kn&&n(r,U),r=A.hasEagerState?A.eagerState:n(r,U)}else R={lane:U,revertLane:A.revertLane,action:A.action,hasEagerState:A.hasEagerState,eagerState:A.eagerState,next:null},m===null?(d=m=R,o=r):m=m.next=R,lt.lanes|=U,vn|=U;A=A.next}while(A!==null&&A!==e);if(m===null?o=r:m.next=d,!re(r,t.memoizedState)&&(Lt=!0,w&&(n=vl,n!==null)))throw n;t.memoizedState=r,t.baseState=o,t.baseQueue=m,l.lastRenderedState=r}return i===null&&(l.lanes=0),[t.memoizedState,l.dispatch]}function Ys(t){var e=Ut(),n=e.queue;if(n===null)throw Error(c(311));n.lastRenderedReducer=t;var l=n.dispatch,i=n.pending,r=e.memoizedState;if(i!==null){n.pending=null;var o=i=i.next;do r=t(r,o.action),o=o.next;while(o!==i);re(r,e.memoizedState)||(Lt=!0),e.memoizedState=r,e.baseQueue===null&&(e.baseState=r),n.lastRenderedState=r}return[r,l]}function Fo(t,e,n){var l=lt,i=Ut(),r=ft;if(r){if(n===void 0)throw Error(c(407));n=n()}else n=e();var o=!re((yt||i).memoizedState,n);o&&(i.memoizedState=n,Lt=!0),i=i.queue;var d=Io.bind(null,l,i,t);if(pa(2048,8,d,[t]),i.getSnapshot!==e||o||Ct!==null&&Ct.memoizedState.tag&1){if(l.flags|=2048,_l(9,xi(),Po.bind(null,l,i,n,e),null),bt===null)throw Error(c(349));r||(fn&124)!==0||$o(l,e,n)}return n}function $o(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=lt.updateQueue,e===null?(e=qs(),lt.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function Po(t,e,n,l){e.value=n,e.getSnapshot=l,tf(e)&&ef(t)}function Io(t,e,n){return n(function(){tf(e)&&ef(t)})}function tf(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!re(t,n)}catch{return!0}}function ef(t){var e=hl(t,2);e!==null&&me(e,t,2)}function Xs(t){var e=te();if(typeof t=="function"){var n=t;if(t=n(),kn){ln(!0);try{n()}finally{ln(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Je,lastRenderedState:t},e}function nf(t,e,n,l){return t.baseState=n,Hs(t,yt,typeof l=="function"?l:Je)}function ip(t,e,n,l,i){if(wi(t))throw Error(c(485));if(t=e.action,t!==null){var r={payload:i,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(o){r.listeners.push(o)}};D.T!==null?n(!0):r.isTransition=!1,l(r),n=e.pending,n===null?(r.next=e.pending=r,lf(e,r)):(r.next=n.next,e.pending=n.next=r)}}function lf(t,e){var n=e.action,l=e.payload,i=t.state;if(e.isTransition){var r=D.T,o={};D.T=o;try{var d=n(i,l),m=D.S;m!==null&&m(o,d),af(t,e,d)}catch(A){Gs(t,e,A)}finally{D.T=r}}else try{r=n(i,l),af(t,e,r)}catch(A){Gs(t,e,A)}}function af(t,e,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(l){uf(t,e,l)},function(l){return Gs(t,e,l)}):uf(t,e,n)}function uf(t,e,n){e.status="fulfilled",e.value=n,sf(e),t.state=n,e=t.pending,e!==null&&(n=e.next,n===e?t.pending=null:(n=n.next,e.next=n,lf(t,n)))}function Gs(t,e,n){var l=t.pending;if(t.pending=null,l!==null){l=l.next;do e.status="rejected",e.reason=n,sf(e),e=e.next;while(e!==l)}t.action=null}function sf(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function cf(t,e){return e}function rf(t,e){if(ft){var n=bt.formState;if(n!==null){t:{var l=lt;if(ft){if(Tt){e:{for(var i=Tt,r=Ue;i.nodeType!==8;){if(!r){i=null;break e}if(i=Ne(i.nextSibling),i===null){i=null;break e}}r=i.data,i=r==="F!"||r==="F"?i:null}if(i){Tt=Ne(i.nextSibling),l=i.data==="F!";break t}}Yn(l)}l=!1}l&&(e=n[0])}}return n=te(),n.memoizedState=n.baseState=e,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:cf,lastRenderedState:e},n.queue=l,n=Nf.bind(null,lt,l),l.dispatch=n,l=Xs(!1),r=Ks.bind(null,lt,!1,l.queue),l=te(),i={state:e,dispatch:null,action:t,pending:null},l.queue=i,n=ip.bind(null,lt,i,r,n),i.dispatch=n,l.memoizedState=t,[e,n,!1]}function of(t){var e=Ut();return ff(e,yt,t)}function ff(t,e,n){if(e=Hs(t,e,cf)[0],t=Oi(Je)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var l=ma(e)}catch(o){throw o===ra?Si:o}else l=e;e=Ut();var i=e.queue,r=i.dispatch;return n!==e.memoizedState&&(lt.flags|=2048,_l(9,xi(),up.bind(null,i,n),null)),[l,r,t]}function up(t,e){t.action=e}function df(t){var e=Ut(),n=yt;if(n!==null)return ff(e,n,t);Ut(),e=e.memoizedState,n=Ut();var l=n.queue.dispatch;return n.memoizedState=t,[e,l,!1]}function _l(t,e,n,l){return t={tag:t,create:n,deps:l,inst:e,next:null},e=lt.updateQueue,e===null&&(e=qs(),lt.updateQueue=e),n=e.lastEffect,n===null?e.lastEffect=t.next=t:(l=n.next,n.next=t,t.next=l,e.lastEffect=t),t}function xi(){return{destroy:void 0,resource:void 0}}function hf(){return Ut().memoizedState}function Ni(t,e,n,l){var i=te();l=l===void 0?null:l,lt.flags|=t,i.memoizedState=_l(1|e,xi(),n,l)}function pa(t,e,n,l){var i=Ut();l=l===void 0?null:l;var r=i.memoizedState.inst;yt!==null&&l!==null&&Us(l,yt.memoizedState.deps)?i.memoizedState=_l(e,r,n,l):(lt.flags|=t,i.memoizedState=_l(1|e,r,n,l))}function yf(t,e){Ni(8390656,8,t,e)}function mf(t,e){pa(2048,8,t,e)}function pf(t,e){return pa(4,2,t,e)}function gf(t,e){return pa(4,4,t,e)}function vf(t,e){if(typeof e=="function"){t=t();var n=e(t);return function(){typeof n=="function"?n():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function bf(t,e,n){n=n!=null?n.concat([t]):null,pa(4,4,vf.bind(null,e,t),n)}function Vs(){}function Sf(t,e){var n=Ut();e=e===void 0?null:e;var l=n.memoizedState;return e!==null&&Us(e,l[1])?l[0]:(n.memoizedState=[t,e],t)}function Ef(t,e){var n=Ut();e=e===void 0?null:e;var l=n.memoizedState;if(e!==null&&Us(e,l[1]))return l[0];if(l=t(),kn){ln(!0);try{t()}finally{ln(!1)}}return n.memoizedState=[l,e],l}function ks(t,e,n){return n===void 0||(fn&1073741824)!==0?t.memoizedState=e:(t.memoizedState=n,t=Td(),lt.lanes|=t,vn|=t,n)}function _f(t,e,n,l){return re(n,e)?n:bl.current!==null?(t=ks(t,n,l),re(t,e)||(Lt=!0),t):(fn&42)===0?(Lt=!0,t.memoizedState=n):(t=Td(),lt.lanes|=t,vn|=t,e)}function Af(t,e,n,l,i){var r=X.p;X.p=r!==0&&8>r?r:8;var o=D.T,d={};D.T=d,Ks(t,!1,e,n);try{var m=i(),A=D.S;if(A!==null&&A(d,m),m!==null&&typeof m=="object"&&typeof m.then=="function"){var w=np(m,l);ga(t,e,w,ye(t))}else ga(t,e,l,ye(t))}catch(U){ga(t,e,{then:function(){},status:"rejected",reason:U},ye())}finally{X.p=r,D.T=o}}function sp(){}function Qs(t,e,n,l){if(t.tag!==5)throw Error(c(476));var i=Tf(t).queue;Af(t,i,e,W,n===null?sp:function(){return Rf(t),n(l)})}function Tf(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:W,baseState:W,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Je,lastRenderedState:W},next:null};var n={};return e.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Je,lastRenderedState:n},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function Rf(t){var e=Tf(t).next.queue;ga(t,e,{},ye())}function Zs(){return Jt(Ba)}function Of(){return Ut().memoizedState}function xf(){return Ut().memoizedState}function cp(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var n=ye();t=rn(n);var l=on(e,t,n);l!==null&&(me(l,e,n),fa(l,e,n)),e={cache:_s()},t.payload=e;return}e=e.return}}function rp(t,e,n){var l=ye();n={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},wi(t)?wf(e,n):(n=ds(t,e,n,l),n!==null&&(me(n,t,l),Df(n,e,l)))}function Nf(t,e,n){var l=ye();ga(t,e,n,l)}function ga(t,e,n,l){var i={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(wi(t))wf(e,i);else{var r=t.alternate;if(t.lanes===0&&(r===null||r.lanes===0)&&(r=e.lastRenderedReducer,r!==null))try{var o=e.lastRenderedState,d=r(o,n);if(i.hasEagerState=!0,i.eagerState=d,re(d,o))return di(t,e,i,0),bt===null&&fi(),!1}catch{}finally{}if(n=ds(t,e,i,l),n!==null)return me(n,t,l),Df(n,e,l),!0}return!1}function Ks(t,e,n,l){if(l={lane:2,revertLane:Rc(),action:l,hasEagerState:!1,eagerState:null,next:null},wi(t)){if(e)throw Error(c(479))}else e=ds(t,n,l,2),e!==null&&me(e,t,2)}function wi(t){var e=t.alternate;return t===lt||e!==null&&e===lt}function wf(t,e){Sl=Ai=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function Df(t,e,n){if((n&4194048)!==0){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,jr(t,n)}}var Di={readContext:Jt,use:Ri,useCallback:Nt,useContext:Nt,useEffect:Nt,useImperativeHandle:Nt,useLayoutEffect:Nt,useInsertionEffect:Nt,useMemo:Nt,useReducer:Nt,useRef:Nt,useState:Nt,useDebugValue:Nt,useDeferredValue:Nt,useTransition:Nt,useSyncExternalStore:Nt,useId:Nt,useHostTransitionStatus:Nt,useFormState:Nt,useActionState:Nt,useOptimistic:Nt,useMemoCache:Nt,useCacheRefresh:Nt},Cf={readContext:Jt,use:Ri,useCallback:function(t,e){return te().memoizedState=[t,e===void 0?null:e],t},useContext:Jt,useEffect:yf,useImperativeHandle:function(t,e,n){n=n!=null?n.concat([t]):null,Ni(4194308,4,vf.bind(null,e,t),n)},useLayoutEffect:function(t,e){return Ni(4194308,4,t,e)},useInsertionEffect:function(t,e){Ni(4,2,t,e)},useMemo:function(t,e){var n=te();e=e===void 0?null:e;var l=t();if(kn){ln(!0);try{t()}finally{ln(!1)}}return n.memoizedState=[l,e],l},useReducer:function(t,e,n){var l=te();if(n!==void 0){var i=n(e);if(kn){ln(!0);try{n(e)}finally{ln(!1)}}}else i=e;return l.memoizedState=l.baseState=i,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:i},l.queue=t,t=t.dispatch=rp.bind(null,lt,t),[l.memoizedState,t]},useRef:function(t){var e=te();return t={current:t},e.memoizedState=t},useState:function(t){t=Xs(t);var e=t.queue,n=Nf.bind(null,lt,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:Vs,useDeferredValue:function(t,e){var n=te();return ks(n,t,e)},useTransition:function(){var t=Xs(!1);return t=Af.bind(null,lt,t.queue,!0,!1),te().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var l=lt,i=te();if(ft){if(n===void 0)throw Error(c(407));n=n()}else{if(n=e(),bt===null)throw Error(c(349));(ct&124)!==0||$o(l,e,n)}i.memoizedState=n;var r={value:n,getSnapshot:e};return i.queue=r,yf(Io.bind(null,l,r,t),[t]),l.flags|=2048,_l(9,xi(),Po.bind(null,l,r,n,e),null),n},useId:function(){var t=te(),e=bt.identifierPrefix;if(ft){var n=Qe,l=ke;n=(l&~(1<<32-ce(l)-1)).toString(32)+n,e="«"+e+"R"+n,n=Ti++,0<n&&(e+="H"+n.toString(32)),e+="»"}else n=lp++,e="«"+e+"r"+n.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:Zs,useFormState:rf,useActionState:rf,useOptimistic:function(t){var e=te();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=Ks.bind(null,lt,!0,n),n.dispatch=e,[t,e]},useMemoCache:Ls,useCacheRefresh:function(){return te().memoizedState=cp.bind(null,lt)}},Uf={readContext:Jt,use:Ri,useCallback:Sf,useContext:Jt,useEffect:mf,useImperativeHandle:bf,useInsertionEffect:pf,useLayoutEffect:gf,useMemo:Ef,useReducer:Oi,useRef:hf,useState:function(){return Oi(Je)},useDebugValue:Vs,useDeferredValue:function(t,e){var n=Ut();return _f(n,yt.memoizedState,t,e)},useTransition:function(){var t=Oi(Je)[0],e=Ut().memoizedState;return[typeof t=="boolean"?t:ma(t),e]},useSyncExternalStore:Fo,useId:Of,useHostTransitionStatus:Zs,useFormState:of,useActionState:of,useOptimistic:function(t,e){var n=Ut();return nf(n,yt,t,e)},useMemoCache:Ls,useCacheRefresh:xf},op={readContext:Jt,use:Ri,useCallback:Sf,useContext:Jt,useEffect:mf,useImperativeHandle:bf,useInsertionEffect:pf,useLayoutEffect:gf,useMemo:Ef,useReducer:Ys,useRef:hf,useState:function(){return Ys(Je)},useDebugValue:Vs,useDeferredValue:function(t,e){var n=Ut();return yt===null?ks(n,t,e):_f(n,yt.memoizedState,t,e)},useTransition:function(){var t=Ys(Je)[0],e=Ut().memoizedState;return[typeof t=="boolean"?t:ma(t),e]},useSyncExternalStore:Fo,useId:Of,useHostTransitionStatus:Zs,useFormState:df,useActionState:df,useOptimistic:function(t,e){var n=Ut();return yt!==null?nf(n,yt,t,e):(n.baseState=t,[t,n.queue.dispatch])},useMemoCache:Ls,useCacheRefresh:xf},Al=null,va=0;function Ci(t){var e=va;return va+=1,Al===null&&(Al=[]),Go(Al,t,e)}function ba(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function Ui(t,e){throw e.$$typeof===N?Error(c(525)):(t=Object.prototype.toString.call(e),Error(c(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function zf(t){var e=t._init;return e(t._payload)}function Mf(t){function e(E,v){if(t){var _=E.deletions;_===null?(E.deletions=[v],E.flags|=16):_.push(v)}}function n(E,v){if(!t)return null;for(;v!==null;)e(E,v),v=v.sibling;return null}function l(E){for(var v=new Map;E!==null;)E.key!==null?v.set(E.key,E):v.set(E.index,E),E=E.sibling;return v}function i(E,v){return E=Ve(E,v),E.index=0,E.sibling=null,E}function r(E,v,_){return E.index=_,t?(_=E.alternate,_!==null?(_=_.index,_<v?(E.flags|=67108866,v):_):(E.flags|=67108866,v)):(E.flags|=1048576,v)}function o(E){return t&&E.alternate===null&&(E.flags|=67108866),E}function d(E,v,_,C){return v===null||v.tag!==6?(v=ys(_,E.mode,C),v.return=E,v):(v=i(v,_),v.return=E,v)}function m(E,v,_,C){var V=_.type;return V===B?w(E,v,_.props.children,C,_.key):v!==null&&(v.elementType===V||typeof V=="object"&&V!==null&&V.$$typeof===jt&&zf(V)===v.type)?(v=i(v,_.props),ba(v,_),v.return=E,v):(v=yi(_.type,_.key,_.props,null,E.mode,C),ba(v,_),v.return=E,v)}function A(E,v,_,C){return v===null||v.tag!==4||v.stateNode.containerInfo!==_.containerInfo||v.stateNode.implementation!==_.implementation?(v=ms(_,E.mode,C),v.return=E,v):(v=i(v,_.children||[]),v.return=E,v)}function w(E,v,_,C,V){return v===null||v.tag!==7?(v=jn(_,E.mode,C,V),v.return=E,v):(v=i(v,_),v.return=E,v)}function U(E,v,_){if(typeof v=="string"&&v!==""||typeof v=="number"||typeof v=="bigint")return v=ys(""+v,E.mode,_),v.return=E,v;if(typeof v=="object"&&v!==null){switch(v.$$typeof){case z:return _=yi(v.type,v.key,v.props,null,E.mode,_),ba(_,v),_.return=E,_;case H:return v=ms(v,E.mode,_),v.return=E,v;case jt:var C=v._init;return v=C(v._payload),U(E,v,_)}if(Zt(v)||Qt(v))return v=jn(v,E.mode,_,null),v.return=E,v;if(typeof v.then=="function")return U(E,Ci(v),_);if(v.$$typeof===nt)return U(E,vi(E,v),_);Ui(E,v)}return null}function R(E,v,_,C){var V=v!==null?v.key:null;if(typeof _=="string"&&_!==""||typeof _=="number"||typeof _=="bigint")return V!==null?null:d(E,v,""+_,C);if(typeof _=="object"&&_!==null){switch(_.$$typeof){case z:return _.key===V?m(E,v,_,C):null;case H:return _.key===V?A(E,v,_,C):null;case jt:return V=_._init,_=V(_._payload),R(E,v,_,C)}if(Zt(_)||Qt(_))return V!==null?null:w(E,v,_,C,null);if(typeof _.then=="function")return R(E,v,Ci(_),C);if(_.$$typeof===nt)return R(E,v,vi(E,_),C);Ui(E,_)}return null}function O(E,v,_,C,V){if(typeof C=="string"&&C!==""||typeof C=="number"||typeof C=="bigint")return E=E.get(_)||null,d(v,E,""+C,V);if(typeof C=="object"&&C!==null){switch(C.$$typeof){case z:return E=E.get(C.key===null?_:C.key)||null,m(v,E,C,V);case H:return E=E.get(C.key===null?_:C.key)||null,A(v,E,C,V);case jt:var at=C._init;return C=at(C._payload),O(E,v,_,C,V)}if(Zt(C)||Qt(C))return E=E.get(_)||null,w(v,E,C,V,null);if(typeof C.then=="function")return O(E,v,_,Ci(C),V);if(C.$$typeof===nt)return O(E,v,_,vi(v,C),V);Ui(v,C)}return null}function F(E,v,_,C){for(var V=null,at=null,k=v,J=v=0,Yt=null;k!==null&&J<_.length;J++){k.index>J?(Yt=k,k=null):Yt=k.sibling;var ot=R(E,k,_[J],C);if(ot===null){k===null&&(k=Yt);break}t&&k&&ot.alternate===null&&e(E,k),v=r(ot,v,J),at===null?V=ot:at.sibling=ot,at=ot,k=Yt}if(J===_.length)return n(E,k),ft&&Ln(E,J),V;if(k===null){for(;J<_.length;J++)k=U(E,_[J],C),k!==null&&(v=r(k,v,J),at===null?V=k:at.sibling=k,at=k);return ft&&Ln(E,J),V}for(k=l(k);J<_.length;J++)Yt=O(k,E,J,_[J],C),Yt!==null&&(t&&Yt.alternate!==null&&k.delete(Yt.key===null?J:Yt.key),v=r(Yt,v,J),at===null?V=Yt:at.sibling=Yt,at=Yt);return t&&k.forEach(function(xn){return e(E,xn)}),ft&&Ln(E,J),V}function K(E,v,_,C){if(_==null)throw Error(c(151));for(var V=null,at=null,k=v,J=v=0,Yt=null,ot=_.next();k!==null&&!ot.done;J++,ot=_.next()){k.index>J?(Yt=k,k=null):Yt=k.sibling;var xn=R(E,k,ot.value,C);if(xn===null){k===null&&(k=Yt);break}t&&k&&xn.alternate===null&&e(E,k),v=r(xn,v,J),at===null?V=xn:at.sibling=xn,at=xn,k=Yt}if(ot.done)return n(E,k),ft&&Ln(E,J),V;if(k===null){for(;!ot.done;J++,ot=_.next())ot=U(E,ot.value,C),ot!==null&&(v=r(ot,v,J),at===null?V=ot:at.sibling=ot,at=ot);return ft&&Ln(E,J),V}for(k=l(k);!ot.done;J++,ot=_.next())ot=O(k,E,J,ot.value,C),ot!==null&&(t&&ot.alternate!==null&&k.delete(ot.key===null?J:ot.key),v=r(ot,v,J),at===null?V=ot:at.sibling=ot,at=ot);return t&&k.forEach(function(f0){return e(E,f0)}),ft&&Ln(E,J),V}function pt(E,v,_,C){if(typeof _=="object"&&_!==null&&_.type===B&&_.key===null&&(_=_.props.children),typeof _=="object"&&_!==null){switch(_.$$typeof){case z:t:{for(var V=_.key;v!==null;){if(v.key===V){if(V=_.type,V===B){if(v.tag===7){n(E,v.sibling),C=i(v,_.props.children),C.return=E,E=C;break t}}else if(v.elementType===V||typeof V=="object"&&V!==null&&V.$$typeof===jt&&zf(V)===v.type){n(E,v.sibling),C=i(v,_.props),ba(C,_),C.return=E,E=C;break t}n(E,v);break}else e(E,v);v=v.sibling}_.type===B?(C=jn(_.props.children,E.mode,C,_.key),C.return=E,E=C):(C=yi(_.type,_.key,_.props,null,E.mode,C),ba(C,_),C.return=E,E=C)}return o(E);case H:t:{for(V=_.key;v!==null;){if(v.key===V)if(v.tag===4&&v.stateNode.containerInfo===_.containerInfo&&v.stateNode.implementation===_.implementation){n(E,v.sibling),C=i(v,_.children||[]),C.return=E,E=C;break t}else{n(E,v);break}else e(E,v);v=v.sibling}C=ms(_,E.mode,C),C.return=E,E=C}return o(E);case jt:return V=_._init,_=V(_._payload),pt(E,v,_,C)}if(Zt(_))return F(E,v,_,C);if(Qt(_)){if(V=Qt(_),typeof V!="function")throw Error(c(150));return _=V.call(_),K(E,v,_,C)}if(typeof _.then=="function")return pt(E,v,Ci(_),C);if(_.$$typeof===nt)return pt(E,v,vi(E,_),C);Ui(E,_)}return typeof _=="string"&&_!==""||typeof _=="number"||typeof _=="bigint"?(_=""+_,v!==null&&v.tag===6?(n(E,v.sibling),C=i(v,_),C.return=E,E=C):(n(E,v),C=ys(_,E.mode,C),C.return=E,E=C),o(E)):n(E,v)}return function(E,v,_,C){try{va=0;var V=pt(E,v,_,C);return Al=null,V}catch(k){if(k===ra||k===Si)throw k;var at=oe(29,k,null,E.mode);return at.lanes=C,at.return=E,at}finally{}}}var Tl=Mf(!0),Bf=Mf(!1),_e=M(null),ze=null;function dn(t){var e=t.alternate;Y(Bt,Bt.current&1),Y(_e,t),ze===null&&(e===null||bl.current!==null||e.memoizedState!==null)&&(ze=t)}function jf(t){if(t.tag===22){if(Y(Bt,Bt.current),Y(_e,t),ze===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(ze=t)}}else hn()}function hn(){Y(Bt,Bt.current),Y(_e,_e.current)}function We(t){G(_e),ze===t&&(ze=null),G(Bt)}var Bt=M(0);function zi(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||qc(n)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function Js(t,e,n,l){e=t.memoizedState,n=n(l,e),n=n==null?e:b({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var Ws={enqueueSetState:function(t,e,n){t=t._reactInternals;var l=ye(),i=rn(l);i.payload=e,n!=null&&(i.callback=n),e=on(t,i,l),e!==null&&(me(e,t,l),fa(e,t,l))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var l=ye(),i=rn(l);i.tag=1,i.payload=e,n!=null&&(i.callback=n),e=on(t,i,l),e!==null&&(me(e,t,l),fa(e,t,l))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=ye(),l=rn(n);l.tag=2,e!=null&&(l.callback=e),e=on(t,l,n),e!==null&&(me(e,t,n),fa(e,t,n))}};function qf(t,e,n,l,i,r,o){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(l,r,o):e.prototype&&e.prototype.isPureReactComponent?!ea(n,l)||!ea(i,r):!0}function Lf(t,e,n,l){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,l),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,l),e.state!==t&&Ws.enqueueReplaceState(e,e.state,null)}function Qn(t,e){var n=e;if("ref"in e){n={};for(var l in e)l!=="ref"&&(n[l]=e[l])}if(t=t.defaultProps){n===e&&(n=b({},n));for(var i in t)n[i]===void 0&&(n[i]=t[i])}return n}var Mi=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Hf(t){Mi(t)}function Yf(t){console.error(t)}function Xf(t){Mi(t)}function Bi(t,e){try{var n=t.onUncaughtError;n(e.value,{componentStack:e.stack})}catch(l){setTimeout(function(){throw l})}}function Gf(t,e,n){try{var l=t.onCaughtError;l(n.value,{componentStack:n.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(i){setTimeout(function(){throw i})}}function Fs(t,e,n){return n=rn(n),n.tag=3,n.payload={element:null},n.callback=function(){Bi(t,e)},n}function Vf(t){return t=rn(t),t.tag=3,t}function kf(t,e,n,l){var i=n.type.getDerivedStateFromError;if(typeof i=="function"){var r=l.value;t.payload=function(){return i(r)},t.callback=function(){Gf(e,n,l)}}var o=n.stateNode;o!==null&&typeof o.componentDidCatch=="function"&&(t.callback=function(){Gf(e,n,l),typeof i!="function"&&(bn===null?bn=new Set([this]):bn.add(this));var d=l.stack;this.componentDidCatch(l.value,{componentStack:d!==null?d:""})})}function fp(t,e,n,l,i){if(n.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(e=n.alternate,e!==null&&ua(e,n,i,!0),n=_e.current,n!==null){switch(n.tag){case 13:return ze===null?Sc():n.alternate===null&&Rt===0&&(Rt=3),n.flags&=-257,n.flags|=65536,n.lanes=i,l===Rs?n.flags|=16384:(e=n.updateQueue,e===null?n.updateQueue=new Set([l]):e.add(l),_c(t,l,i)),!1;case 22:return n.flags|=65536,l===Rs?n.flags|=16384:(e=n.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([l])},n.updateQueue=e):(n=e.retryQueue,n===null?e.retryQueue=new Set([l]):n.add(l)),_c(t,l,i)),!1}throw Error(c(435,n.tag))}return _c(t,l,i),Sc(),!1}if(ft)return e=_e.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=i,l!==vs&&(t=Error(c(422),{cause:l}),ia(ve(t,n)))):(l!==vs&&(e=Error(c(423),{cause:l}),ia(ve(e,n))),t=t.current.alternate,t.flags|=65536,i&=-i,t.lanes|=i,l=ve(l,n),i=Fs(t.stateNode,l,i),Ns(t,i),Rt!==4&&(Rt=2)),!1;var r=Error(c(520),{cause:l});if(r=ve(r,n),Oa===null?Oa=[r]:Oa.push(r),Rt!==4&&(Rt=2),e===null)return!0;l=ve(l,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=i&-i,n.lanes|=t,t=Fs(n.stateNode,l,t),Ns(n,t),!1;case 1:if(e=n.type,r=n.stateNode,(n.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||r!==null&&typeof r.componentDidCatch=="function"&&(bn===null||!bn.has(r))))return n.flags|=65536,i&=-i,n.lanes|=i,i=Vf(i),kf(i,t,n,l),Ns(n,i),!1}n=n.return}while(n!==null);return!1}var Qf=Error(c(461)),Lt=!1;function Gt(t,e,n,l){e.child=t===null?Bf(e,null,n,l):Tl(e,t.child,n,l)}function Zf(t,e,n,l,i){n=n.render;var r=e.ref;if("ref"in l){var o={};for(var d in l)d!=="ref"&&(o[d]=l[d])}else o=l;return Gn(e),l=zs(t,e,n,o,r,i),d=Ms(),t!==null&&!Lt?(Bs(t,e,i),Fe(t,e,i)):(ft&&d&&ps(e),e.flags|=1,Gt(t,e,l,i),e.child)}function Kf(t,e,n,l,i){if(t===null){var r=n.type;return typeof r=="function"&&!hs(r)&&r.defaultProps===void 0&&n.compare===null?(e.tag=15,e.type=r,Jf(t,e,r,l,i)):(t=yi(n.type,null,l,e,e.mode,i),t.ref=e.ref,t.return=e,e.child=t)}if(r=t.child,!ac(t,i)){var o=r.memoizedProps;if(n=n.compare,n=n!==null?n:ea,n(o,l)&&t.ref===e.ref)return Fe(t,e,i)}return e.flags|=1,t=Ve(r,l),t.ref=e.ref,t.return=e,e.child=t}function Jf(t,e,n,l,i){if(t!==null){var r=t.memoizedProps;if(ea(r,l)&&t.ref===e.ref)if(Lt=!1,e.pendingProps=l=r,ac(t,i))(t.flags&131072)!==0&&(Lt=!0);else return e.lanes=t.lanes,Fe(t,e,i)}return $s(t,e,n,l,i)}function Wf(t,e,n){var l=e.pendingProps,i=l.children,r=t!==null?t.memoizedState:null;if(l.mode==="hidden"){if((e.flags&128)!==0){if(l=r!==null?r.baseLanes|n:n,t!==null){for(i=e.child=t.child,r=0;i!==null;)r=r|i.lanes|i.childLanes,i=i.sibling;e.childLanes=r&~l}else e.childLanes=0,e.child=null;return Ff(t,e,l,n)}if((n&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&bi(e,r!==null?r.cachePool:null),r!==null?Ko(e,r):Ds(),jf(e);else return e.lanes=e.childLanes=536870912,Ff(t,e,r!==null?r.baseLanes|n:n,n)}else r!==null?(bi(e,r.cachePool),Ko(e,r),hn(),e.memoizedState=null):(t!==null&&bi(e,null),Ds(),hn());return Gt(t,e,i,n),e.child}function Ff(t,e,n,l){var i=Ts();return i=i===null?null:{parent:Mt._currentValue,pool:i},e.memoizedState={baseLanes:n,cachePool:i},t!==null&&bi(e,null),Ds(),jf(e),t!==null&&ua(t,e,l,!0),null}function ji(t,e){var n=e.ref;if(n===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(c(284));(t===null||t.ref!==n)&&(e.flags|=4194816)}}function $s(t,e,n,l,i){return Gn(e),n=zs(t,e,n,l,void 0,i),l=Ms(),t!==null&&!Lt?(Bs(t,e,i),Fe(t,e,i)):(ft&&l&&ps(e),e.flags|=1,Gt(t,e,n,i),e.child)}function $f(t,e,n,l,i,r){return Gn(e),e.updateQueue=null,n=Wo(e,l,n,i),Jo(t),l=Ms(),t!==null&&!Lt?(Bs(t,e,r),Fe(t,e,r)):(ft&&l&&ps(e),e.flags|=1,Gt(t,e,n,r),e.child)}function Pf(t,e,n,l,i){if(Gn(e),e.stateNode===null){var r=yl,o=n.contextType;typeof o=="object"&&o!==null&&(r=Jt(o)),r=new n(l,r),e.memoizedState=r.state!==null&&r.state!==void 0?r.state:null,r.updater=Ws,e.stateNode=r,r._reactInternals=e,r=e.stateNode,r.props=l,r.state=e.memoizedState,r.refs={},Os(e),o=n.contextType,r.context=typeof o=="object"&&o!==null?Jt(o):yl,r.state=e.memoizedState,o=n.getDerivedStateFromProps,typeof o=="function"&&(Js(e,n,o,l),r.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof r.getSnapshotBeforeUpdate=="function"||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(o=r.state,typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount(),o!==r.state&&Ws.enqueueReplaceState(r,r.state,null),ha(e,l,r,i),da(),r.state=e.memoizedState),typeof r.componentDidMount=="function"&&(e.flags|=4194308),l=!0}else if(t===null){r=e.stateNode;var d=e.memoizedProps,m=Qn(n,d);r.props=m;var A=r.context,w=n.contextType;o=yl,typeof w=="object"&&w!==null&&(o=Jt(w));var U=n.getDerivedStateFromProps;w=typeof U=="function"||typeof r.getSnapshotBeforeUpdate=="function",d=e.pendingProps!==d,w||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(d||A!==o)&&Lf(e,r,l,o),cn=!1;var R=e.memoizedState;r.state=R,ha(e,l,r,i),da(),A=e.memoizedState,d||R!==A||cn?(typeof U=="function"&&(Js(e,n,U,l),A=e.memoizedState),(m=cn||qf(e,n,m,l,R,A,o))?(w||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount()),typeof r.componentDidMount=="function"&&(e.flags|=4194308)):(typeof r.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=l,e.memoizedState=A),r.props=l,r.state=A,r.context=o,l=m):(typeof r.componentDidMount=="function"&&(e.flags|=4194308),l=!1)}else{r=e.stateNode,xs(t,e),o=e.memoizedProps,w=Qn(n,o),r.props=w,U=e.pendingProps,R=r.context,A=n.contextType,m=yl,typeof A=="object"&&A!==null&&(m=Jt(A)),d=n.getDerivedStateFromProps,(A=typeof d=="function"||typeof r.getSnapshotBeforeUpdate=="function")||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(o!==U||R!==m)&&Lf(e,r,l,m),cn=!1,R=e.memoizedState,r.state=R,ha(e,l,r,i),da();var O=e.memoizedState;o!==U||R!==O||cn||t!==null&&t.dependencies!==null&&gi(t.dependencies)?(typeof d=="function"&&(Js(e,n,d,l),O=e.memoizedState),(w=cn||qf(e,n,w,l,R,O,m)||t!==null&&t.dependencies!==null&&gi(t.dependencies))?(A||typeof r.UNSAFE_componentWillUpdate!="function"&&typeof r.componentWillUpdate!="function"||(typeof r.componentWillUpdate=="function"&&r.componentWillUpdate(l,O,m),typeof r.UNSAFE_componentWillUpdate=="function"&&r.UNSAFE_componentWillUpdate(l,O,m)),typeof r.componentDidUpdate=="function"&&(e.flags|=4),typeof r.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof r.componentDidUpdate!="function"||o===t.memoizedProps&&R===t.memoizedState||(e.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||o===t.memoizedProps&&R===t.memoizedState||(e.flags|=1024),e.memoizedProps=l,e.memoizedState=O),r.props=l,r.state=O,r.context=m,l=w):(typeof r.componentDidUpdate!="function"||o===t.memoizedProps&&R===t.memoizedState||(e.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||o===t.memoizedProps&&R===t.memoizedState||(e.flags|=1024),l=!1)}return r=l,ji(t,e),l=(e.flags&128)!==0,r||l?(r=e.stateNode,n=l&&typeof n.getDerivedStateFromError!="function"?null:r.render(),e.flags|=1,t!==null&&l?(e.child=Tl(e,t.child,null,i),e.child=Tl(e,null,n,i)):Gt(t,e,n,i),e.memoizedState=r.state,t=e.child):t=Fe(t,e,i),t}function If(t,e,n,l){return aa(),e.flags|=256,Gt(t,e,n,l),e.child}var Ps={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Is(t){return{baseLanes:t,cachePool:Ho()}}function tc(t,e,n){return t=t!==null?t.childLanes&~n:0,e&&(t|=Ae),t}function td(t,e,n){var l=e.pendingProps,i=!1,r=(e.flags&128)!==0,o;if((o=r)||(o=t!==null&&t.memoizedState===null?!1:(Bt.current&2)!==0),o&&(i=!0,e.flags&=-129),o=(e.flags&32)!==0,e.flags&=-33,t===null){if(ft){if(i?dn(e):hn(),ft){var d=Tt,m;if(m=d){t:{for(m=d,d=Ue;m.nodeType!==8;){if(!d){d=null;break t}if(m=Ne(m.nextSibling),m===null){d=null;break t}}d=m}d!==null?(e.memoizedState={dehydrated:d,treeContext:qn!==null?{id:ke,overflow:Qe}:null,retryLane:536870912,hydrationErrors:null},m=oe(18,null,null,0),m.stateNode=d,m.return=e,e.child=m,$t=e,Tt=null,m=!0):m=!1}m||Yn(e)}if(d=e.memoizedState,d!==null&&(d=d.dehydrated,d!==null))return qc(d)?e.lanes=32:e.lanes=536870912,null;We(e)}return d=l.children,l=l.fallback,i?(hn(),i=e.mode,d=qi({mode:"hidden",children:d},i),l=jn(l,i,n,null),d.return=e,l.return=e,d.sibling=l,e.child=d,i=e.child,i.memoizedState=Is(n),i.childLanes=tc(t,o,n),e.memoizedState=Ps,l):(dn(e),ec(e,d))}if(m=t.memoizedState,m!==null&&(d=m.dehydrated,d!==null)){if(r)e.flags&256?(dn(e),e.flags&=-257,e=nc(t,e,n)):e.memoizedState!==null?(hn(),e.child=t.child,e.flags|=128,e=null):(hn(),i=l.fallback,d=e.mode,l=qi({mode:"visible",children:l.children},d),i=jn(i,d,n,null),i.flags|=2,l.return=e,i.return=e,l.sibling=i,e.child=l,Tl(e,t.child,null,n),l=e.child,l.memoizedState=Is(n),l.childLanes=tc(t,o,n),e.memoizedState=Ps,e=i);else if(dn(e),qc(d)){if(o=d.nextSibling&&d.nextSibling.dataset,o)var A=o.dgst;o=A,l=Error(c(419)),l.stack="",l.digest=o,ia({value:l,source:null,stack:null}),e=nc(t,e,n)}else if(Lt||ua(t,e,n,!1),o=(n&t.childLanes)!==0,Lt||o){if(o=bt,o!==null&&(l=n&-n,l=(l&42)!==0?1:Lu(l),l=(l&(o.suspendedLanes|n))!==0?0:l,l!==0&&l!==m.retryLane))throw m.retryLane=l,hl(t,l),me(o,t,l),Qf;d.data==="$?"||Sc(),e=nc(t,e,n)}else d.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=m.treeContext,Tt=Ne(d.nextSibling),$t=e,ft=!0,Hn=null,Ue=!1,t!==null&&(Se[Ee++]=ke,Se[Ee++]=Qe,Se[Ee++]=qn,ke=t.id,Qe=t.overflow,qn=e),e=ec(e,l.children),e.flags|=4096);return e}return i?(hn(),i=l.fallback,d=e.mode,m=t.child,A=m.sibling,l=Ve(m,{mode:"hidden",children:l.children}),l.subtreeFlags=m.subtreeFlags&65011712,A!==null?i=Ve(A,i):(i=jn(i,d,n,null),i.flags|=2),i.return=e,l.return=e,l.sibling=i,e.child=l,l=i,i=e.child,d=t.child.memoizedState,d===null?d=Is(n):(m=d.cachePool,m!==null?(A=Mt._currentValue,m=m.parent!==A?{parent:A,pool:A}:m):m=Ho(),d={baseLanes:d.baseLanes|n,cachePool:m}),i.memoizedState=d,i.childLanes=tc(t,o,n),e.memoizedState=Ps,l):(dn(e),n=t.child,t=n.sibling,n=Ve(n,{mode:"visible",children:l.children}),n.return=e,n.sibling=null,t!==null&&(o=e.deletions,o===null?(e.deletions=[t],e.flags|=16):o.push(t)),e.child=n,e.memoizedState=null,n)}function ec(t,e){return e=qi({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function qi(t,e){return t=oe(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function nc(t,e,n){return Tl(e,t.child,null,n),t=ec(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function ed(t,e,n){t.lanes|=e;var l=t.alternate;l!==null&&(l.lanes|=e),Ss(t.return,e,n)}function lc(t,e,n,l,i){var r=t.memoizedState;r===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:l,tail:n,tailMode:i}:(r.isBackwards=e,r.rendering=null,r.renderingStartTime=0,r.last=l,r.tail=n,r.tailMode=i)}function nd(t,e,n){var l=e.pendingProps,i=l.revealOrder,r=l.tail;if(Gt(t,e,l.children,n),l=Bt.current,(l&2)!==0)l=l&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&ed(t,n,e);else if(t.tag===19)ed(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}l&=1}switch(Y(Bt,l),i){case"forwards":for(n=e.child,i=null;n!==null;)t=n.alternate,t!==null&&zi(t)===null&&(i=n),n=n.sibling;n=i,n===null?(i=e.child,e.child=null):(i=n.sibling,n.sibling=null),lc(e,!1,i,n,r);break;case"backwards":for(n=null,i=e.child,e.child=null;i!==null;){if(t=i.alternate,t!==null&&zi(t)===null){e.child=i;break}t=i.sibling,i.sibling=n,n=i,i=t}lc(e,!0,n,null,r);break;case"together":lc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Fe(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),vn|=e.lanes,(n&e.childLanes)===0)if(t!==null){if(ua(t,e,n,!1),(n&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(c(153));if(e.child!==null){for(t=e.child,n=Ve(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=Ve(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function ac(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&gi(t)))}function dp(t,e,n){switch(e.tag){case 3:St(e,e.stateNode.containerInfo),sn(e,Mt,t.memoizedState.cache),aa();break;case 27:case 5:zu(e);break;case 4:St(e,e.stateNode.containerInfo);break;case 10:sn(e,e.type,e.memoizedProps.value);break;case 13:var l=e.memoizedState;if(l!==null)return l.dehydrated!==null?(dn(e),e.flags|=128,null):(n&e.child.childLanes)!==0?td(t,e,n):(dn(e),t=Fe(t,e,n),t!==null?t.sibling:null);dn(e);break;case 19:var i=(t.flags&128)!==0;if(l=(n&e.childLanes)!==0,l||(ua(t,e,n,!1),l=(n&e.childLanes)!==0),i){if(l)return nd(t,e,n);e.flags|=128}if(i=e.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),Y(Bt,Bt.current),l)break;return null;case 22:case 23:return e.lanes=0,Wf(t,e,n);case 24:sn(e,Mt,t.memoizedState.cache)}return Fe(t,e,n)}function ld(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps)Lt=!0;else{if(!ac(t,n)&&(e.flags&128)===0)return Lt=!1,dp(t,e,n);Lt=(t.flags&131072)!==0}else Lt=!1,ft&&(e.flags&1048576)!==0&&Uo(e,pi,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var l=e.elementType,i=l._init;if(l=i(l._payload),e.type=l,typeof l=="function")hs(l)?(t=Qn(l,t),e.tag=1,e=Pf(null,e,l,t,n)):(e.tag=0,e=$s(null,e,l,t,n));else{if(l!=null){if(i=l.$$typeof,i===Ot){e.tag=11,e=Zf(null,e,l,t,n);break t}else if(i===xt){e.tag=14,e=Kf(null,e,l,t,n);break t}}throw e=Dn(l)||l,Error(c(306,e,""))}}return e;case 0:return $s(t,e,e.type,e.pendingProps,n);case 1:return l=e.type,i=Qn(l,e.pendingProps),Pf(t,e,l,i,n);case 3:t:{if(St(e,e.stateNode.containerInfo),t===null)throw Error(c(387));l=e.pendingProps;var r=e.memoizedState;i=r.element,xs(t,e),ha(e,l,null,n);var o=e.memoizedState;if(l=o.cache,sn(e,Mt,l),l!==r.cache&&Es(e,[Mt],n,!0),da(),l=o.element,r.isDehydrated)if(r={element:l,isDehydrated:!1,cache:o.cache},e.updateQueue.baseState=r,e.memoizedState=r,e.flags&256){e=If(t,e,l,n);break t}else if(l!==i){i=ve(Error(c(424)),e),ia(i),e=If(t,e,l,n);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Tt=Ne(t.firstChild),$t=e,ft=!0,Hn=null,Ue=!0,n=Bf(e,null,l,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(aa(),l===i){e=Fe(t,e,n);break t}Gt(t,e,l,n)}e=e.child}return e;case 26:return ji(t,e),t===null?(n=sh(e.type,null,e.pendingProps,null))?e.memoizedState=n:ft||(n=e.type,t=e.pendingProps,l=$i(P.current).createElement(n),l[Kt]=e,l[Pt]=t,kt(l,n,t),qt(l),e.stateNode=l):e.memoizedState=sh(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return zu(e),t===null&&ft&&(l=e.stateNode=ah(e.type,e.pendingProps,P.current),$t=e,Ue=!0,i=Tt,_n(e.type)?(Lc=i,Tt=Ne(l.firstChild)):Tt=i),Gt(t,e,e.pendingProps.children,n),ji(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&ft&&((i=l=Tt)&&(l=Yp(l,e.type,e.pendingProps,Ue),l!==null?(e.stateNode=l,$t=e,Tt=Ne(l.firstChild),Ue=!1,i=!0):i=!1),i||Yn(e)),zu(e),i=e.type,r=e.pendingProps,o=t!==null?t.memoizedProps:null,l=r.children,Mc(i,r)?l=null:o!==null&&Mc(i,o)&&(e.flags|=32),e.memoizedState!==null&&(i=zs(t,e,ap,null,null,n),Ba._currentValue=i),ji(t,e),Gt(t,e,l,n),e.child;case 6:return t===null&&ft&&((t=n=Tt)&&(n=Xp(n,e.pendingProps,Ue),n!==null?(e.stateNode=n,$t=e,Tt=null,t=!0):t=!1),t||Yn(e)),null;case 13:return td(t,e,n);case 4:return St(e,e.stateNode.containerInfo),l=e.pendingProps,t===null?e.child=Tl(e,null,l,n):Gt(t,e,l,n),e.child;case 11:return Zf(t,e,e.type,e.pendingProps,n);case 7:return Gt(t,e,e.pendingProps,n),e.child;case 8:return Gt(t,e,e.pendingProps.children,n),e.child;case 12:return Gt(t,e,e.pendingProps.children,n),e.child;case 10:return l=e.pendingProps,sn(e,e.type,l.value),Gt(t,e,l.children,n),e.child;case 9:return i=e.type._context,l=e.pendingProps.children,Gn(e),i=Jt(i),l=l(i),e.flags|=1,Gt(t,e,l,n),e.child;case 14:return Kf(t,e,e.type,e.pendingProps,n);case 15:return Jf(t,e,e.type,e.pendingProps,n);case 19:return nd(t,e,n);case 31:return l=e.pendingProps,n=e.mode,l={mode:l.mode,children:l.children},t===null?(n=qi(l,n),n.ref=e.ref,e.child=n,n.return=e,e=n):(n=Ve(t.child,l),n.ref=e.ref,e.child=n,n.return=e,e=n),e;case 22:return Wf(t,e,n);case 24:return Gn(e),l=Jt(Mt),t===null?(i=Ts(),i===null&&(i=bt,r=_s(),i.pooledCache=r,r.refCount++,r!==null&&(i.pooledCacheLanes|=n),i=r),e.memoizedState={parent:l,cache:i},Os(e),sn(e,Mt,i)):((t.lanes&n)!==0&&(xs(t,e),ha(e,null,null,n),da()),i=t.memoizedState,r=e.memoizedState,i.parent!==l?(i={parent:l,cache:l},e.memoizedState=i,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=i),sn(e,Mt,l)):(l=r.cache,sn(e,Mt,l),l!==i.cache&&Es(e,[Mt],n,!0))),Gt(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(c(156,e.tag))}function $e(t){t.flags|=4}function ad(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!dh(e)){if(e=_e.current,e!==null&&((ct&4194048)===ct?ze!==null:(ct&62914560)!==ct&&(ct&536870912)===0||e!==ze))throw oa=Rs,Yo;t.flags|=8192}}function Li(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Mr():536870912,t.lanes|=e,Nl|=e)}function Sa(t,e){if(!ft)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:l.sibling=null}}function _t(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,l=0;if(e)for(var i=t.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags&65011712,l|=i.flags&65011712,i.return=t,i=i.sibling;else for(i=t.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags,l|=i.flags,i.return=t,i=i.sibling;return t.subtreeFlags|=l,t.childLanes=n,e}function hp(t,e,n){var l=e.pendingProps;switch(gs(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return _t(e),null;case 1:return _t(e),null;case 3:return n=e.stateNode,l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),Ke(Mt),nn(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(t===null||t.child===null)&&(la(e)?$e(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,Bo())),_t(e),null;case 26:return n=e.memoizedState,t===null?($e(e),n!==null?(_t(e),ad(e,n)):(_t(e),e.flags&=-16777217)):n?n!==t.memoizedState?($e(e),_t(e),ad(e,n)):(_t(e),e.flags&=-16777217):(t.memoizedProps!==l&&$e(e),_t(e),e.flags&=-16777217),null;case 27:Wa(e),n=P.current;var i=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==l&&$e(e);else{if(!l){if(e.stateNode===null)throw Error(c(166));return _t(e),null}t=Z.current,la(e)?zo(e):(t=ah(i,l,n),e.stateNode=t,$e(e))}return _t(e),null;case 5:if(Wa(e),n=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==l&&$e(e);else{if(!l){if(e.stateNode===null)throw Error(c(166));return _t(e),null}if(t=Z.current,la(e))zo(e);else{switch(i=$i(P.current),t){case 1:t=i.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:t=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":t=i.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":t=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":t=i.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof l.is=="string"?i.createElement("select",{is:l.is}):i.createElement("select"),l.multiple?t.multiple=!0:l.size&&(t.size=l.size);break;default:t=typeof l.is=="string"?i.createElement(n,{is:l.is}):i.createElement(n)}}t[Kt]=e,t[Pt]=l;t:for(i=e.child;i!==null;){if(i.tag===5||i.tag===6)t.appendChild(i.stateNode);else if(i.tag!==4&&i.tag!==27&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===e)break t;for(;i.sibling===null;){if(i.return===null||i.return===e)break t;i=i.return}i.sibling.return=i.return,i=i.sibling}e.stateNode=t;t:switch(kt(t,n,l),n){case"button":case"input":case"select":case"textarea":t=!!l.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&$e(e)}}return _t(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==l&&$e(e);else{if(typeof l!="string"&&e.stateNode===null)throw Error(c(166));if(t=P.current,la(e)){if(t=e.stateNode,n=e.memoizedProps,l=null,i=$t,i!==null)switch(i.tag){case 27:case 5:l=i.memoizedProps}t[Kt]=e,t=!!(t.nodeValue===n||l!==null&&l.suppressHydrationWarning===!0||$d(t.nodeValue,n)),t||Yn(e)}else t=$i(t).createTextNode(l),t[Kt]=e,e.stateNode=t}return _t(e),null;case 13:if(l=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(i=la(e),l!==null&&l.dehydrated!==null){if(t===null){if(!i)throw Error(c(318));if(i=e.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(c(317));i[Kt]=e}else aa(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;_t(e),i=!1}else i=Bo(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=i),i=!0;if(!i)return e.flags&256?(We(e),e):(We(e),null)}if(We(e),(e.flags&128)!==0)return e.lanes=n,e;if(n=l!==null,t=t!==null&&t.memoizedState!==null,n){l=e.child,i=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(i=l.alternate.memoizedState.cachePool.pool);var r=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(r=l.memoizedState.cachePool.pool),r!==i&&(l.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),Li(e,e.updateQueue),_t(e),null;case 4:return nn(),t===null&&wc(e.stateNode.containerInfo),_t(e),null;case 10:return Ke(e.type),_t(e),null;case 19:if(G(Bt),i=e.memoizedState,i===null)return _t(e),null;if(l=(e.flags&128)!==0,r=i.rendering,r===null)if(l)Sa(i,!1);else{if(Rt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(r=zi(t),r!==null){for(e.flags|=128,Sa(i,!1),t=r.updateQueue,e.updateQueue=t,Li(e,t),e.subtreeFlags=0,t=n,n=e.child;n!==null;)Co(n,t),n=n.sibling;return Y(Bt,Bt.current&1|2),e.child}t=t.sibling}i.tail!==null&&Ce()>Xi&&(e.flags|=128,l=!0,Sa(i,!1),e.lanes=4194304)}else{if(!l)if(t=zi(r),t!==null){if(e.flags|=128,l=!0,t=t.updateQueue,e.updateQueue=t,Li(e,t),Sa(i,!0),i.tail===null&&i.tailMode==="hidden"&&!r.alternate&&!ft)return _t(e),null}else 2*Ce()-i.renderingStartTime>Xi&&n!==536870912&&(e.flags|=128,l=!0,Sa(i,!1),e.lanes=4194304);i.isBackwards?(r.sibling=e.child,e.child=r):(t=i.last,t!==null?t.sibling=r:e.child=r,i.last=r)}return i.tail!==null?(e=i.tail,i.rendering=e,i.tail=e.sibling,i.renderingStartTime=Ce(),e.sibling=null,t=Bt.current,Y(Bt,l?t&1|2:t&1),e):(_t(e),null);case 22:case 23:return We(e),Cs(),l=e.memoizedState!==null,t!==null?t.memoizedState!==null!==l&&(e.flags|=8192):l&&(e.flags|=8192),l?(n&536870912)!==0&&(e.flags&128)===0&&(_t(e),e.subtreeFlags&6&&(e.flags|=8192)):_t(e),n=e.updateQueue,n!==null&&Li(e,n.retryQueue),n=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),l=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),l!==n&&(e.flags|=2048),t!==null&&G(Vn),null;case 24:return n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),Ke(Mt),_t(e),null;case 25:return null;case 30:return null}throw Error(c(156,e.tag))}function yp(t,e){switch(gs(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Ke(Mt),nn(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return Wa(e),null;case 13:if(We(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(c(340));aa()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return G(Bt),null;case 4:return nn(),null;case 10:return Ke(e.type),null;case 22:case 23:return We(e),Cs(),t!==null&&G(Vn),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return Ke(Mt),null;case 25:return null;default:return null}}function id(t,e){switch(gs(e),e.tag){case 3:Ke(Mt),nn();break;case 26:case 27:case 5:Wa(e);break;case 4:nn();break;case 13:We(e);break;case 19:G(Bt);break;case 10:Ke(e.type);break;case 22:case 23:We(e),Cs(),t!==null&&G(Vn);break;case 24:Ke(Mt)}}function Ea(t,e){try{var n=e.updateQueue,l=n!==null?n.lastEffect:null;if(l!==null){var i=l.next;n=i;do{if((n.tag&t)===t){l=void 0;var r=n.create,o=n.inst;l=r(),o.destroy=l}n=n.next}while(n!==i)}}catch(d){vt(e,e.return,d)}}function yn(t,e,n){try{var l=e.updateQueue,i=l!==null?l.lastEffect:null;if(i!==null){var r=i.next;l=r;do{if((l.tag&t)===t){var o=l.inst,d=o.destroy;if(d!==void 0){o.destroy=void 0,i=e;var m=n,A=d;try{A()}catch(w){vt(i,m,w)}}}l=l.next}while(l!==r)}}catch(w){vt(e,e.return,w)}}function ud(t){var e=t.updateQueue;if(e!==null){var n=t.stateNode;try{Zo(e,n)}catch(l){vt(t,t.return,l)}}}function sd(t,e,n){n.props=Qn(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(l){vt(t,e,l)}}function _a(t,e){try{var n=t.ref;if(n!==null){switch(t.tag){case 26:case 27:case 5:var l=t.stateNode;break;case 30:l=t.stateNode;break;default:l=t.stateNode}typeof n=="function"?t.refCleanup=n(l):n.current=l}}catch(i){vt(t,e,i)}}function Me(t,e){var n=t.ref,l=t.refCleanup;if(n!==null)if(typeof l=="function")try{l()}catch(i){vt(t,e,i)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(i){vt(t,e,i)}else n.current=null}function cd(t){var e=t.type,n=t.memoizedProps,l=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":n.autoFocus&&l.focus();break t;case"img":n.src?l.src=n.src:n.srcSet&&(l.srcset=n.srcSet)}}catch(i){vt(t,t.return,i)}}function ic(t,e,n){try{var l=t.stateNode;Bp(l,t.type,n,e),l[Pt]=e}catch(i){vt(t,t.return,i)}}function rd(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&_n(t.type)||t.tag===4}function uc(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||rd(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&_n(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function sc(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(t,e):(e=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,e.appendChild(t),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=Fi));else if(l!==4&&(l===27&&_n(t.type)&&(n=t.stateNode,e=null),t=t.child,t!==null))for(sc(t,e,n),t=t.sibling;t!==null;)sc(t,e,n),t=t.sibling}function Hi(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(l!==4&&(l===27&&_n(t.type)&&(n=t.stateNode),t=t.child,t!==null))for(Hi(t,e,n),t=t.sibling;t!==null;)Hi(t,e,n),t=t.sibling}function od(t){var e=t.stateNode,n=t.memoizedProps;try{for(var l=t.type,i=e.attributes;i.length;)e.removeAttributeNode(i[0]);kt(e,l,n),e[Kt]=t,e[Pt]=n}catch(r){vt(t,t.return,r)}}var Pe=!1,wt=!1,cc=!1,fd=typeof WeakSet=="function"?WeakSet:Set,Ht=null;function mp(t,e){if(t=t.containerInfo,Uc=lu,t=Eo(t),us(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var l=n.getSelection&&n.getSelection();if(l&&l.rangeCount!==0){n=l.anchorNode;var i=l.anchorOffset,r=l.focusNode;l=l.focusOffset;try{n.nodeType,r.nodeType}catch{n=null;break t}var o=0,d=-1,m=-1,A=0,w=0,U=t,R=null;e:for(;;){for(var O;U!==n||i!==0&&U.nodeType!==3||(d=o+i),U!==r||l!==0&&U.nodeType!==3||(m=o+l),U.nodeType===3&&(o+=U.nodeValue.length),(O=U.firstChild)!==null;)R=U,U=O;for(;;){if(U===t)break e;if(R===n&&++A===i&&(d=o),R===r&&++w===l&&(m=o),(O=U.nextSibling)!==null)break;U=R,R=U.parentNode}U=O}n=d===-1||m===-1?null:{start:d,end:m}}else n=null}n=n||{start:0,end:0}}else n=null;for(zc={focusedElem:t,selectionRange:n},lu=!1,Ht=e;Ht!==null;)if(e=Ht,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Ht=t;else for(;Ht!==null;){switch(e=Ht,r=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&r!==null){t=void 0,n=e,i=r.memoizedProps,r=r.memoizedState,l=n.stateNode;try{var F=Qn(n.type,i,n.elementType===n.type);t=l.getSnapshotBeforeUpdate(F,r),l.__reactInternalSnapshotBeforeUpdate=t}catch(K){vt(n,n.return,K)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,n=t.nodeType,n===9)jc(t);else if(n===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":jc(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(c(163))}if(t=e.sibling,t!==null){t.return=e.return,Ht=t;break}Ht=e.return}}function dd(t,e,n){var l=n.flags;switch(n.tag){case 0:case 11:case 15:mn(t,n),l&4&&Ea(5,n);break;case 1:if(mn(t,n),l&4)if(t=n.stateNode,e===null)try{t.componentDidMount()}catch(o){vt(n,n.return,o)}else{var i=Qn(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(i,e,t.__reactInternalSnapshotBeforeUpdate)}catch(o){vt(n,n.return,o)}}l&64&&ud(n),l&512&&_a(n,n.return);break;case 3:if(mn(t,n),l&64&&(t=n.updateQueue,t!==null)){if(e=null,n.child!==null)switch(n.child.tag){case 27:case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}try{Zo(t,e)}catch(o){vt(n,n.return,o)}}break;case 27:e===null&&l&4&&od(n);case 26:case 5:mn(t,n),e===null&&l&4&&cd(n),l&512&&_a(n,n.return);break;case 12:mn(t,n);break;case 13:mn(t,n),l&4&&md(t,n),l&64&&(t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(n=Tp.bind(null,n),Gp(t,n))));break;case 22:if(l=n.memoizedState!==null||Pe,!l){e=e!==null&&e.memoizedState!==null||wt,i=Pe;var r=wt;Pe=l,(wt=e)&&!r?pn(t,n,(n.subtreeFlags&8772)!==0):mn(t,n),Pe=i,wt=r}break;case 30:break;default:mn(t,n)}}function hd(t){var e=t.alternate;e!==null&&(t.alternate=null,hd(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&Xu(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Et=null,ee=!1;function Ie(t,e,n){for(n=n.child;n!==null;)yd(t,e,n),n=n.sibling}function yd(t,e,n){if(se&&typeof se.onCommitFiberUnmount=="function")try{se.onCommitFiberUnmount(Vl,n)}catch{}switch(n.tag){case 26:wt||Me(n,e),Ie(t,e,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:wt||Me(n,e);var l=Et,i=ee;_n(n.type)&&(Et=n.stateNode,ee=!1),Ie(t,e,n),Ca(n.stateNode),Et=l,ee=i;break;case 5:wt||Me(n,e);case 6:if(l=Et,i=ee,Et=null,Ie(t,e,n),Et=l,ee=i,Et!==null)if(ee)try{(Et.nodeType===9?Et.body:Et.nodeName==="HTML"?Et.ownerDocument.body:Et).removeChild(n.stateNode)}catch(r){vt(n,e,r)}else try{Et.removeChild(n.stateNode)}catch(r){vt(n,e,r)}break;case 18:Et!==null&&(ee?(t=Et,nh(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,n.stateNode),Ha(t)):nh(Et,n.stateNode));break;case 4:l=Et,i=ee,Et=n.stateNode.containerInfo,ee=!0,Ie(t,e,n),Et=l,ee=i;break;case 0:case 11:case 14:case 15:wt||yn(2,n,e),wt||yn(4,n,e),Ie(t,e,n);break;case 1:wt||(Me(n,e),l=n.stateNode,typeof l.componentWillUnmount=="function"&&sd(n,e,l)),Ie(t,e,n);break;case 21:Ie(t,e,n);break;case 22:wt=(l=wt)||n.memoizedState!==null,Ie(t,e,n),wt=l;break;default:Ie(t,e,n)}}function md(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{Ha(t)}catch(n){vt(e,e.return,n)}}function pp(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new fd),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new fd),e;default:throw Error(c(435,t.tag))}}function rc(t,e){var n=pp(t);e.forEach(function(l){var i=Rp.bind(null,t,l);n.has(l)||(n.add(l),l.then(i,i))})}function fe(t,e){var n=e.deletions;if(n!==null)for(var l=0;l<n.length;l++){var i=n[l],r=t,o=e,d=o;t:for(;d!==null;){switch(d.tag){case 27:if(_n(d.type)){Et=d.stateNode,ee=!1;break t}break;case 5:Et=d.stateNode,ee=!1;break t;case 3:case 4:Et=d.stateNode.containerInfo,ee=!0;break t}d=d.return}if(Et===null)throw Error(c(160));yd(r,o,i),Et=null,ee=!1,r=i.alternate,r!==null&&(r.return=null),i.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)pd(e,t),e=e.sibling}var xe=null;function pd(t,e){var n=t.alternate,l=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:fe(e,t),de(t),l&4&&(yn(3,t,t.return),Ea(3,t),yn(5,t,t.return));break;case 1:fe(e,t),de(t),l&512&&(wt||n===null||Me(n,n.return)),l&64&&Pe&&(t=t.updateQueue,t!==null&&(l=t.callbacks,l!==null&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=n===null?l:n.concat(l))));break;case 26:var i=xe;if(fe(e,t),de(t),l&512&&(wt||n===null||Me(n,n.return)),l&4){var r=n!==null?n.memoizedState:null;if(l=t.memoizedState,n===null)if(l===null)if(t.stateNode===null){t:{l=t.type,n=t.memoizedProps,i=i.ownerDocument||i;e:switch(l){case"title":r=i.getElementsByTagName("title")[0],(!r||r[Zl]||r[Kt]||r.namespaceURI==="http://www.w3.org/2000/svg"||r.hasAttribute("itemprop"))&&(r=i.createElement(l),i.head.insertBefore(r,i.querySelector("head > title"))),kt(r,l,n),r[Kt]=t,qt(r),l=r;break t;case"link":var o=oh("link","href",i).get(l+(n.href||""));if(o){for(var d=0;d<o.length;d++)if(r=o[d],r.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&r.getAttribute("rel")===(n.rel==null?null:n.rel)&&r.getAttribute("title")===(n.title==null?null:n.title)&&r.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){o.splice(d,1);break e}}r=i.createElement(l),kt(r,l,n),i.head.appendChild(r);break;case"meta":if(o=oh("meta","content",i).get(l+(n.content||""))){for(d=0;d<o.length;d++)if(r=o[d],r.getAttribute("content")===(n.content==null?null:""+n.content)&&r.getAttribute("name")===(n.name==null?null:n.name)&&r.getAttribute("property")===(n.property==null?null:n.property)&&r.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&r.getAttribute("charset")===(n.charSet==null?null:n.charSet)){o.splice(d,1);break e}}r=i.createElement(l),kt(r,l,n),i.head.appendChild(r);break;default:throw Error(c(468,l))}r[Kt]=t,qt(r),l=r}t.stateNode=l}else fh(i,t.type,t.stateNode);else t.stateNode=rh(i,l,t.memoizedProps);else r!==l?(r===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):r.count--,l===null?fh(i,t.type,t.stateNode):rh(i,l,t.memoizedProps)):l===null&&t.stateNode!==null&&ic(t,t.memoizedProps,n.memoizedProps)}break;case 27:fe(e,t),de(t),l&512&&(wt||n===null||Me(n,n.return)),n!==null&&l&4&&ic(t,t.memoizedProps,n.memoizedProps);break;case 5:if(fe(e,t),de(t),l&512&&(wt||n===null||Me(n,n.return)),t.flags&32){i=t.stateNode;try{ul(i,"")}catch(O){vt(t,t.return,O)}}l&4&&t.stateNode!=null&&(i=t.memoizedProps,ic(t,i,n!==null?n.memoizedProps:i)),l&1024&&(cc=!0);break;case 6:if(fe(e,t),de(t),l&4){if(t.stateNode===null)throw Error(c(162));l=t.memoizedProps,n=t.stateNode;try{n.nodeValue=l}catch(O){vt(t,t.return,O)}}break;case 3:if(tu=null,i=xe,xe=Pi(e.containerInfo),fe(e,t),xe=i,de(t),l&4&&n!==null&&n.memoizedState.isDehydrated)try{Ha(e.containerInfo)}catch(O){vt(t,t.return,O)}cc&&(cc=!1,gd(t));break;case 4:l=xe,xe=Pi(t.stateNode.containerInfo),fe(e,t),de(t),xe=l;break;case 12:fe(e,t),de(t);break;case 13:fe(e,t),de(t),t.child.flags&8192&&t.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(mc=Ce()),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,rc(t,l)));break;case 22:i=t.memoizedState!==null;var m=n!==null&&n.memoizedState!==null,A=Pe,w=wt;if(Pe=A||i,wt=w||m,fe(e,t),wt=w,Pe=A,de(t),l&8192)t:for(e=t.stateNode,e._visibility=i?e._visibility&-2:e._visibility|1,i&&(n===null||m||Pe||wt||Zn(t)),n=null,e=t;;){if(e.tag===5||e.tag===26){if(n===null){m=n=e;try{if(r=m.stateNode,i)o=r.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none";else{d=m.stateNode;var U=m.memoizedProps.style,R=U!=null&&U.hasOwnProperty("display")?U.display:null;d.style.display=R==null||typeof R=="boolean"?"":(""+R).trim()}}catch(O){vt(m,m.return,O)}}}else if(e.tag===6){if(n===null){m=e;try{m.stateNode.nodeValue=i?"":m.memoizedProps}catch(O){vt(m,m.return,O)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}l&4&&(l=t.updateQueue,l!==null&&(n=l.retryQueue,n!==null&&(l.retryQueue=null,rc(t,n))));break;case 19:fe(e,t),de(t),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,rc(t,l)));break;case 30:break;case 21:break;default:fe(e,t),de(t)}}function de(t){var e=t.flags;if(e&2){try{for(var n,l=t.return;l!==null;){if(rd(l)){n=l;break}l=l.return}if(n==null)throw Error(c(160));switch(n.tag){case 27:var i=n.stateNode,r=uc(t);Hi(t,r,i);break;case 5:var o=n.stateNode;n.flags&32&&(ul(o,""),n.flags&=-33);var d=uc(t);Hi(t,d,o);break;case 3:case 4:var m=n.stateNode.containerInfo,A=uc(t);sc(t,A,m);break;default:throw Error(c(161))}}catch(w){vt(t,t.return,w)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function gd(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;gd(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function mn(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)dd(t,e.alternate,e),e=e.sibling}function Zn(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:yn(4,e,e.return),Zn(e);break;case 1:Me(e,e.return);var n=e.stateNode;typeof n.componentWillUnmount=="function"&&sd(e,e.return,n),Zn(e);break;case 27:Ca(e.stateNode);case 26:case 5:Me(e,e.return),Zn(e);break;case 22:e.memoizedState===null&&Zn(e);break;case 30:Zn(e);break;default:Zn(e)}t=t.sibling}}function pn(t,e,n){for(n=n&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var l=e.alternate,i=t,r=e,o=r.flags;switch(r.tag){case 0:case 11:case 15:pn(i,r,n),Ea(4,r);break;case 1:if(pn(i,r,n),l=r,i=l.stateNode,typeof i.componentDidMount=="function")try{i.componentDidMount()}catch(A){vt(l,l.return,A)}if(l=r,i=l.updateQueue,i!==null){var d=l.stateNode;try{var m=i.shared.hiddenCallbacks;if(m!==null)for(i.shared.hiddenCallbacks=null,i=0;i<m.length;i++)Qo(m[i],d)}catch(A){vt(l,l.return,A)}}n&&o&64&&ud(r),_a(r,r.return);break;case 27:od(r);case 26:case 5:pn(i,r,n),n&&l===null&&o&4&&cd(r),_a(r,r.return);break;case 12:pn(i,r,n);break;case 13:pn(i,r,n),n&&o&4&&md(i,r);break;case 22:r.memoizedState===null&&pn(i,r,n),_a(r,r.return);break;case 30:break;default:pn(i,r,n)}e=e.sibling}}function oc(t,e){var n=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==n&&(t!=null&&t.refCount++,n!=null&&sa(n))}function fc(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&sa(t))}function Be(t,e,n,l){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)vd(t,e,n,l),e=e.sibling}function vd(t,e,n,l){var i=e.flags;switch(e.tag){case 0:case 11:case 15:Be(t,e,n,l),i&2048&&Ea(9,e);break;case 1:Be(t,e,n,l);break;case 3:Be(t,e,n,l),i&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&sa(t)));break;case 12:if(i&2048){Be(t,e,n,l),t=e.stateNode;try{var r=e.memoizedProps,o=r.id,d=r.onPostCommit;typeof d=="function"&&d(o,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(m){vt(e,e.return,m)}}else Be(t,e,n,l);break;case 13:Be(t,e,n,l);break;case 23:break;case 22:r=e.stateNode,o=e.alternate,e.memoizedState!==null?r._visibility&2?Be(t,e,n,l):Aa(t,e):r._visibility&2?Be(t,e,n,l):(r._visibility|=2,Rl(t,e,n,l,(e.subtreeFlags&10256)!==0)),i&2048&&oc(o,e);break;case 24:Be(t,e,n,l),i&2048&&fc(e.alternate,e);break;default:Be(t,e,n,l)}}function Rl(t,e,n,l,i){for(i=i&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var r=t,o=e,d=n,m=l,A=o.flags;switch(o.tag){case 0:case 11:case 15:Rl(r,o,d,m,i),Ea(8,o);break;case 23:break;case 22:var w=o.stateNode;o.memoizedState!==null?w._visibility&2?Rl(r,o,d,m,i):Aa(r,o):(w._visibility|=2,Rl(r,o,d,m,i)),i&&A&2048&&oc(o.alternate,o);break;case 24:Rl(r,o,d,m,i),i&&A&2048&&fc(o.alternate,o);break;default:Rl(r,o,d,m,i)}e=e.sibling}}function Aa(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var n=t,l=e,i=l.flags;switch(l.tag){case 22:Aa(n,l),i&2048&&oc(l.alternate,l);break;case 24:Aa(n,l),i&2048&&fc(l.alternate,l);break;default:Aa(n,l)}e=e.sibling}}var Ta=8192;function Ol(t){if(t.subtreeFlags&Ta)for(t=t.child;t!==null;)bd(t),t=t.sibling}function bd(t){switch(t.tag){case 26:Ol(t),t.flags&Ta&&t.memoizedState!==null&&e0(xe,t.memoizedState,t.memoizedProps);break;case 5:Ol(t);break;case 3:case 4:var e=xe;xe=Pi(t.stateNode.containerInfo),Ol(t),xe=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Ta,Ta=16777216,Ol(t),Ta=e):Ol(t));break;default:Ol(t)}}function Sd(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Ra(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];Ht=l,_d(l,t)}Sd(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Ed(t),t=t.sibling}function Ed(t){switch(t.tag){case 0:case 11:case 15:Ra(t),t.flags&2048&&yn(9,t,t.return);break;case 3:Ra(t);break;case 12:Ra(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Yi(t)):Ra(t);break;default:Ra(t)}}function Yi(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];Ht=l,_d(l,t)}Sd(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:yn(8,e,e.return),Yi(e);break;case 22:n=e.stateNode,n._visibility&2&&(n._visibility&=-3,Yi(e));break;default:Yi(e)}t=t.sibling}}function _d(t,e){for(;Ht!==null;){var n=Ht;switch(n.tag){case 0:case 11:case 15:yn(8,n,e);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var l=n.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:sa(n.memoizedState.cache)}if(l=n.child,l!==null)l.return=n,Ht=l;else t:for(n=t;Ht!==null;){l=Ht;var i=l.sibling,r=l.return;if(hd(l),l===n){Ht=null;break t}if(i!==null){i.return=r,Ht=i;break t}Ht=r}}}var gp={getCacheForType:function(t){var e=Jt(Mt),n=e.data.get(t);return n===void 0&&(n=t(),e.data.set(t,n)),n}},vp=typeof WeakMap=="function"?WeakMap:Map,dt=0,bt=null,it=null,ct=0,ht=0,he=null,gn=!1,xl=!1,dc=!1,tn=0,Rt=0,vn=0,Kn=0,hc=0,Ae=0,Nl=0,Oa=null,ne=null,yc=!1,mc=0,Xi=1/0,Gi=null,bn=null,Vt=0,Sn=null,wl=null,Dl=0,pc=0,gc=null,Ad=null,xa=0,vc=null;function ye(){if((dt&2)!==0&&ct!==0)return ct&-ct;if(D.T!==null){var t=gl;return t!==0?t:Rc()}return qr()}function Td(){Ae===0&&(Ae=(ct&536870912)===0||ft?zr():536870912);var t=_e.current;return t!==null&&(t.flags|=32),Ae}function me(t,e,n){(t===bt&&(ht===2||ht===9)||t.cancelPendingCommit!==null)&&(Cl(t,0),En(t,ct,Ae,!1)),Ql(t,n),((dt&2)===0||t!==bt)&&(t===bt&&((dt&2)===0&&(Kn|=n),Rt===4&&En(t,ct,Ae,!1)),je(t))}function Rd(t,e,n){if((dt&6)!==0)throw Error(c(327));var l=!n&&(e&124)===0&&(e&t.expiredLanes)===0||kl(t,e),i=l?Ep(t,e):Ec(t,e,!0),r=l;do{if(i===0){xl&&!l&&En(t,e,0,!1);break}else{if(n=t.current.alternate,r&&!bp(n)){i=Ec(t,e,!1),r=!1;continue}if(i===2){if(r=e,t.errorRecoveryDisabledLanes&r)var o=0;else o=t.pendingLanes&-536870913,o=o!==0?o:o&536870912?536870912:0;if(o!==0){e=o;t:{var d=t;i=Oa;var m=d.current.memoizedState.isDehydrated;if(m&&(Cl(d,o).flags|=256),o=Ec(d,o,!1),o!==2){if(dc&&!m){d.errorRecoveryDisabledLanes|=r,Kn|=r,i=4;break t}r=ne,ne=i,r!==null&&(ne===null?ne=r:ne.push.apply(ne,r))}i=o}if(r=!1,i!==2)continue}}if(i===1){Cl(t,0),En(t,e,0,!0);break}t:{switch(l=t,r=i,r){case 0:case 1:throw Error(c(345));case 4:if((e&4194048)!==e)break;case 6:En(l,e,Ae,!gn);break t;case 2:ne=null;break;case 3:case 5:break;default:throw Error(c(329))}if((e&62914560)===e&&(i=mc+300-Ce(),10<i)){if(En(l,e,Ae,!gn),Ia(l,0,!0)!==0)break t;l.timeoutHandle=th(Od.bind(null,l,n,ne,Gi,yc,e,Ae,Kn,Nl,gn,r,2,-0,0),i);break t}Od(l,n,ne,Gi,yc,e,Ae,Kn,Nl,gn,r,0,-0,0)}}break}while(!0);je(t)}function Od(t,e,n,l,i,r,o,d,m,A,w,U,R,O){if(t.timeoutHandle=-1,U=e.subtreeFlags,(U&8192||(U&16785408)===16785408)&&(Ma={stylesheets:null,count:0,unsuspend:t0},bd(e),U=n0(),U!==null)){t.cancelPendingCommit=U(zd.bind(null,t,e,r,n,l,i,o,d,m,w,1,R,O)),En(t,r,o,!A);return}zd(t,e,r,n,l,i,o,d,m)}function bp(t){for(var e=t;;){var n=e.tag;if((n===0||n===11||n===15)&&e.flags&16384&&(n=e.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var l=0;l<n.length;l++){var i=n[l],r=i.getSnapshot;i=i.value;try{if(!re(r(),i))return!1}catch{return!1}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function En(t,e,n,l){e&=~hc,e&=~Kn,t.suspendedLanes|=e,t.pingedLanes&=~e,l&&(t.warmLanes|=e),l=t.expirationTimes;for(var i=e;0<i;){var r=31-ce(i),o=1<<r;l[r]=-1,i&=~o}n!==0&&Br(t,n,e)}function Vi(){return(dt&6)===0?(Na(0),!1):!0}function bc(){if(it!==null){if(ht===0)var t=it.return;else t=it,Ze=Xn=null,js(t),Al=null,va=0,t=it;for(;t!==null;)id(t.alternate,t),t=t.return;it=null}}function Cl(t,e){var n=t.timeoutHandle;n!==-1&&(t.timeoutHandle=-1,qp(n)),n=t.cancelPendingCommit,n!==null&&(t.cancelPendingCommit=null,n()),bc(),bt=t,it=n=Ve(t.current,null),ct=e,ht=0,he=null,gn=!1,xl=kl(t,e),dc=!1,Nl=Ae=hc=Kn=vn=Rt=0,ne=Oa=null,yc=!1,(e&8)!==0&&(e|=e&32);var l=t.entangledLanes;if(l!==0)for(t=t.entanglements,l&=e;0<l;){var i=31-ce(l),r=1<<i;e|=t[i],l&=~r}return tn=e,fi(),n}function xd(t,e){lt=null,D.H=Di,e===ra||e===Si?(e=Vo(),ht=3):e===Yo?(e=Vo(),ht=4):ht=e===Qf?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,he=e,it===null&&(Rt=1,Bi(t,ve(e,t.current)))}function Nd(){var t=D.H;return D.H=Di,t===null?Di:t}function wd(){var t=D.A;return D.A=gp,t}function Sc(){Rt=4,gn||(ct&4194048)!==ct&&_e.current!==null||(xl=!0),(vn&134217727)===0&&(Kn&134217727)===0||bt===null||En(bt,ct,Ae,!1)}function Ec(t,e,n){var l=dt;dt|=2;var i=Nd(),r=wd();(bt!==t||ct!==e)&&(Gi=null,Cl(t,e)),e=!1;var o=Rt;t:do try{if(ht!==0&&it!==null){var d=it,m=he;switch(ht){case 8:bc(),o=6;break t;case 3:case 2:case 9:case 6:_e.current===null&&(e=!0);var A=ht;if(ht=0,he=null,Ul(t,d,m,A),n&&xl){o=0;break t}break;default:A=ht,ht=0,he=null,Ul(t,d,m,A)}}Sp(),o=Rt;break}catch(w){xd(t,w)}while(!0);return e&&t.shellSuspendCounter++,Ze=Xn=null,dt=l,D.H=i,D.A=r,it===null&&(bt=null,ct=0,fi()),o}function Sp(){for(;it!==null;)Dd(it)}function Ep(t,e){var n=dt;dt|=2;var l=Nd(),i=wd();bt!==t||ct!==e?(Gi=null,Xi=Ce()+500,Cl(t,e)):xl=kl(t,e);t:do try{if(ht!==0&&it!==null){e=it;var r=he;e:switch(ht){case 1:ht=0,he=null,Ul(t,e,r,1);break;case 2:case 9:if(Xo(r)){ht=0,he=null,Cd(e);break}e=function(){ht!==2&&ht!==9||bt!==t||(ht=7),je(t)},r.then(e,e);break t;case 3:ht=7;break t;case 4:ht=5;break t;case 7:Xo(r)?(ht=0,he=null,Cd(e)):(ht=0,he=null,Ul(t,e,r,7));break;case 5:var o=null;switch(it.tag){case 26:o=it.memoizedState;case 5:case 27:var d=it;if(!o||dh(o)){ht=0,he=null;var m=d.sibling;if(m!==null)it=m;else{var A=d.return;A!==null?(it=A,ki(A)):it=null}break e}}ht=0,he=null,Ul(t,e,r,5);break;case 6:ht=0,he=null,Ul(t,e,r,6);break;case 8:bc(),Rt=6;break t;default:throw Error(c(462))}}_p();break}catch(w){xd(t,w)}while(!0);return Ze=Xn=null,D.H=l,D.A=i,dt=n,it!==null?0:(bt=null,ct=0,fi(),Rt)}function _p(){for(;it!==null&&!ky();)Dd(it)}function Dd(t){var e=ld(t.alternate,t,tn);t.memoizedProps=t.pendingProps,e===null?ki(t):it=e}function Cd(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=$f(n,e,e.pendingProps,e.type,void 0,ct);break;case 11:e=$f(n,e,e.pendingProps,e.type.render,e.ref,ct);break;case 5:js(e);default:id(n,e),e=it=Co(e,tn),e=ld(n,e,tn)}t.memoizedProps=t.pendingProps,e===null?ki(t):it=e}function Ul(t,e,n,l){Ze=Xn=null,js(e),Al=null,va=0;var i=e.return;try{if(fp(t,i,e,n,ct)){Rt=1,Bi(t,ve(n,t.current)),it=null;return}}catch(r){if(i!==null)throw it=i,r;Rt=1,Bi(t,ve(n,t.current)),it=null;return}e.flags&32768?(ft||l===1?t=!0:xl||(ct&536870912)!==0?t=!1:(gn=t=!0,(l===2||l===9||l===3||l===6)&&(l=_e.current,l!==null&&l.tag===13&&(l.flags|=16384))),Ud(e,t)):ki(e)}function ki(t){var e=t;do{if((e.flags&32768)!==0){Ud(e,gn);return}t=e.return;var n=hp(e.alternate,e,tn);if(n!==null){it=n;return}if(e=e.sibling,e!==null){it=e;return}it=e=t}while(e!==null);Rt===0&&(Rt=5)}function Ud(t,e){do{var n=yp(t.alternate,t);if(n!==null){n.flags&=32767,it=n;return}if(n=t.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&(t=t.sibling,t!==null)){it=t;return}it=t=n}while(t!==null);Rt=6,it=null}function zd(t,e,n,l,i,r,o,d,m){t.cancelPendingCommit=null;do Qi();while(Vt!==0);if((dt&6)!==0)throw Error(c(327));if(e!==null){if(e===t.current)throw Error(c(177));if(r=e.lanes|e.childLanes,r|=fs,tm(t,n,r,o,d,m),t===bt&&(it=bt=null,ct=0),wl=e,Sn=t,Dl=n,pc=r,gc=i,Ad=l,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,Op(Fa,function(){return Ld(),null})):(t.callbackNode=null,t.callbackPriority=0),l=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||l){l=D.T,D.T=null,i=X.p,X.p=2,o=dt,dt|=4;try{mp(t,e,n)}finally{dt=o,X.p=i,D.T=l}}Vt=1,Md(),Bd(),jd()}}function Md(){if(Vt===1){Vt=0;var t=Sn,e=wl,n=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||n){n=D.T,D.T=null;var l=X.p;X.p=2;var i=dt;dt|=4;try{pd(e,t);var r=zc,o=Eo(t.containerInfo),d=r.focusedElem,m=r.selectionRange;if(o!==d&&d&&d.ownerDocument&&So(d.ownerDocument.documentElement,d)){if(m!==null&&us(d)){var A=m.start,w=m.end;if(w===void 0&&(w=A),"selectionStart"in d)d.selectionStart=A,d.selectionEnd=Math.min(w,d.value.length);else{var U=d.ownerDocument||document,R=U&&U.defaultView||window;if(R.getSelection){var O=R.getSelection(),F=d.textContent.length,K=Math.min(m.start,F),pt=m.end===void 0?K:Math.min(m.end,F);!O.extend&&K>pt&&(o=pt,pt=K,K=o);var E=bo(d,K),v=bo(d,pt);if(E&&v&&(O.rangeCount!==1||O.anchorNode!==E.node||O.anchorOffset!==E.offset||O.focusNode!==v.node||O.focusOffset!==v.offset)){var _=U.createRange();_.setStart(E.node,E.offset),O.removeAllRanges(),K>pt?(O.addRange(_),O.extend(v.node,v.offset)):(_.setEnd(v.node,v.offset),O.addRange(_))}}}}for(U=[],O=d;O=O.parentNode;)O.nodeType===1&&U.push({element:O,left:O.scrollLeft,top:O.scrollTop});for(typeof d.focus=="function"&&d.focus(),d=0;d<U.length;d++){var C=U[d];C.element.scrollLeft=C.left,C.element.scrollTop=C.top}}lu=!!Uc,zc=Uc=null}finally{dt=i,X.p=l,D.T=n}}t.current=e,Vt=2}}function Bd(){if(Vt===2){Vt=0;var t=Sn,e=wl,n=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||n){n=D.T,D.T=null;var l=X.p;X.p=2;var i=dt;dt|=4;try{dd(t,e.alternate,e)}finally{dt=i,X.p=l,D.T=n}}Vt=3}}function jd(){if(Vt===4||Vt===3){Vt=0,Qy();var t=Sn,e=wl,n=Dl,l=Ad;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Vt=5:(Vt=0,wl=Sn=null,qd(t,t.pendingLanes));var i=t.pendingLanes;if(i===0&&(bn=null),Hu(n),e=e.stateNode,se&&typeof se.onCommitFiberRoot=="function")try{se.onCommitFiberRoot(Vl,e,void 0,(e.current.flags&128)===128)}catch{}if(l!==null){e=D.T,i=X.p,X.p=2,D.T=null;try{for(var r=t.onRecoverableError,o=0;o<l.length;o++){var d=l[o];r(d.value,{componentStack:d.stack})}}finally{D.T=e,X.p=i}}(Dl&3)!==0&&Qi(),je(t),i=t.pendingLanes,(n&4194090)!==0&&(i&42)!==0?t===vc?xa++:(xa=0,vc=t):xa=0,Na(0)}}function qd(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,sa(e)))}function Qi(t){return Md(),Bd(),jd(),Ld()}function Ld(){if(Vt!==5)return!1;var t=Sn,e=pc;pc=0;var n=Hu(Dl),l=D.T,i=X.p;try{X.p=32>n?32:n,D.T=null,n=gc,gc=null;var r=Sn,o=Dl;if(Vt=0,wl=Sn=null,Dl=0,(dt&6)!==0)throw Error(c(331));var d=dt;if(dt|=4,Ed(r.current),vd(r,r.current,o,n),dt=d,Na(0,!1),se&&typeof se.onPostCommitFiberRoot=="function")try{se.onPostCommitFiberRoot(Vl,r)}catch{}return!0}finally{X.p=i,D.T=l,qd(t,e)}}function Hd(t,e,n){e=ve(n,e),e=Fs(t.stateNode,e,2),t=on(t,e,2),t!==null&&(Ql(t,2),je(t))}function vt(t,e,n){if(t.tag===3)Hd(t,t,n);else for(;e!==null;){if(e.tag===3){Hd(e,t,n);break}else if(e.tag===1){var l=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(bn===null||!bn.has(l))){t=ve(n,t),n=Vf(2),l=on(e,n,2),l!==null&&(kf(n,l,e,t),Ql(l,2),je(l));break}}e=e.return}}function _c(t,e,n){var l=t.pingCache;if(l===null){l=t.pingCache=new vp;var i=new Set;l.set(e,i)}else i=l.get(e),i===void 0&&(i=new Set,l.set(e,i));i.has(n)||(dc=!0,i.add(n),t=Ap.bind(null,t,e,n),e.then(t,t))}function Ap(t,e,n){var l=t.pingCache;l!==null&&l.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,bt===t&&(ct&n)===n&&(Rt===4||Rt===3&&(ct&62914560)===ct&&300>Ce()-mc?(dt&2)===0&&Cl(t,0):hc|=n,Nl===ct&&(Nl=0)),je(t)}function Yd(t,e){e===0&&(e=Mr()),t=hl(t,e),t!==null&&(Ql(t,e),je(t))}function Tp(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),Yd(t,n)}function Rp(t,e){var n=0;switch(t.tag){case 13:var l=t.stateNode,i=t.memoizedState;i!==null&&(n=i.retryLane);break;case 19:l=t.stateNode;break;case 22:l=t.stateNode._retryCache;break;default:throw Error(c(314))}l!==null&&l.delete(e),Yd(t,n)}function Op(t,e){return Bu(t,e)}var Zi=null,zl=null,Ac=!1,Ki=!1,Tc=!1,Jn=0;function je(t){t!==zl&&t.next===null&&(zl===null?Zi=zl=t:zl=zl.next=t),Ki=!0,Ac||(Ac=!0,Np())}function Na(t,e){if(!Tc&&Ki){Tc=!0;do for(var n=!1,l=Zi;l!==null;){if(t!==0){var i=l.pendingLanes;if(i===0)var r=0;else{var o=l.suspendedLanes,d=l.pingedLanes;r=(1<<31-ce(42|t)+1)-1,r&=i&~(o&~d),r=r&201326741?r&201326741|1:r?r|2:0}r!==0&&(n=!0,kd(l,r))}else r=ct,r=Ia(l,l===bt?r:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(r&3)===0||kl(l,r)||(n=!0,kd(l,r));l=l.next}while(n);Tc=!1}}function xp(){Xd()}function Xd(){Ki=Ac=!1;var t=0;Jn!==0&&(jp()&&(t=Jn),Jn=0);for(var e=Ce(),n=null,l=Zi;l!==null;){var i=l.next,r=Gd(l,e);r===0?(l.next=null,n===null?Zi=i:n.next=i,i===null&&(zl=n)):(n=l,(t!==0||(r&3)!==0)&&(Ki=!0)),l=i}Na(t)}function Gd(t,e){for(var n=t.suspendedLanes,l=t.pingedLanes,i=t.expirationTimes,r=t.pendingLanes&-62914561;0<r;){var o=31-ce(r),d=1<<o,m=i[o];m===-1?((d&n)===0||(d&l)!==0)&&(i[o]=Iy(d,e)):m<=e&&(t.expiredLanes|=d),r&=~d}if(e=bt,n=ct,n=Ia(t,t===e?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l=t.callbackNode,n===0||t===e&&(ht===2||ht===9)||t.cancelPendingCommit!==null)return l!==null&&l!==null&&ju(l),t.callbackNode=null,t.callbackPriority=0;if((n&3)===0||kl(t,n)){if(e=n&-n,e===t.callbackPriority)return e;switch(l!==null&&ju(l),Hu(n)){case 2:case 8:n=Cr;break;case 32:n=Fa;break;case 268435456:n=Ur;break;default:n=Fa}return l=Vd.bind(null,t),n=Bu(n,l),t.callbackPriority=e,t.callbackNode=n,e}return l!==null&&l!==null&&ju(l),t.callbackPriority=2,t.callbackNode=null,2}function Vd(t,e){if(Vt!==0&&Vt!==5)return t.callbackNode=null,t.callbackPriority=0,null;var n=t.callbackNode;if(Qi()&&t.callbackNode!==n)return null;var l=ct;return l=Ia(t,t===bt?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l===0?null:(Rd(t,l,e),Gd(t,Ce()),t.callbackNode!=null&&t.callbackNode===n?Vd.bind(null,t):null)}function kd(t,e){if(Qi())return null;Rd(t,e,!0)}function Np(){Lp(function(){(dt&6)!==0?Bu(Dr,xp):Xd()})}function Rc(){return Jn===0&&(Jn=zr()),Jn}function Qd(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:ai(""+t)}function Zd(t,e){var n=e.ownerDocument.createElement("input");return n.name=e.name,n.value=e.value,t.id&&n.setAttribute("form",t.id),e.parentNode.insertBefore(n,e),t=new FormData(t),n.parentNode.removeChild(n),t}function wp(t,e,n,l,i){if(e==="submit"&&n&&n.stateNode===i){var r=Qd((i[Pt]||null).action),o=l.submitter;o&&(e=(e=o[Pt]||null)?Qd(e.formAction):o.getAttribute("formAction"),e!==null&&(r=e,o=null));var d=new ci("action","action",null,l,i);t.push({event:d,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(Jn!==0){var m=o?Zd(i,o):new FormData(i);Qs(n,{pending:!0,data:m,method:i.method,action:r},null,m)}}else typeof r=="function"&&(d.preventDefault(),m=o?Zd(i,o):new FormData(i),Qs(n,{pending:!0,data:m,method:i.method,action:r},r,m))},currentTarget:i}]})}}for(var Oc=0;Oc<os.length;Oc++){var xc=os[Oc],Dp=xc.toLowerCase(),Cp=xc[0].toUpperCase()+xc.slice(1);Oe(Dp,"on"+Cp)}Oe(To,"onAnimationEnd"),Oe(Ro,"onAnimationIteration"),Oe(Oo,"onAnimationStart"),Oe("dblclick","onDoubleClick"),Oe("focusin","onFocus"),Oe("focusout","onBlur"),Oe(Jm,"onTransitionRun"),Oe(Wm,"onTransitionStart"),Oe(Fm,"onTransitionCancel"),Oe(xo,"onTransitionEnd"),ll("onMouseEnter",["mouseout","mouseover"]),ll("onMouseLeave",["mouseout","mouseover"]),ll("onPointerEnter",["pointerout","pointerover"]),ll("onPointerLeave",["pointerout","pointerover"]),Un("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Un("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Un("onBeforeInput",["compositionend","keypress","textInput","paste"]),Un("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Un("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Un("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var wa="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Up=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(wa));function Kd(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var l=t[n],i=l.event;l=l.listeners;t:{var r=void 0;if(e)for(var o=l.length-1;0<=o;o--){var d=l[o],m=d.instance,A=d.currentTarget;if(d=d.listener,m!==r&&i.isPropagationStopped())break t;r=d,i.currentTarget=A;try{r(i)}catch(w){Mi(w)}i.currentTarget=null,r=m}else for(o=0;o<l.length;o++){if(d=l[o],m=d.instance,A=d.currentTarget,d=d.listener,m!==r&&i.isPropagationStopped())break t;r=d,i.currentTarget=A;try{r(i)}catch(w){Mi(w)}i.currentTarget=null,r=m}}}}function ut(t,e){var n=e[Yu];n===void 0&&(n=e[Yu]=new Set);var l=t+"__bubble";n.has(l)||(Jd(e,t,2,!1),n.add(l))}function Nc(t,e,n){var l=0;e&&(l|=4),Jd(n,t,l,e)}var Ji="_reactListening"+Math.random().toString(36).slice(2);function wc(t){if(!t[Ji]){t[Ji]=!0,Hr.forEach(function(n){n!=="selectionchange"&&(Up.has(n)||Nc(n,!1,t),Nc(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Ji]||(e[Ji]=!0,Nc("selectionchange",!1,e))}}function Jd(t,e,n,l){switch(vh(e)){case 2:var i=i0;break;case 8:i=u0;break;default:i=Vc}n=i.bind(null,e,n,t),i=void 0,!$u||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(i=!0),l?i!==void 0?t.addEventListener(e,n,{capture:!0,passive:i}):t.addEventListener(e,n,!0):i!==void 0?t.addEventListener(e,n,{passive:i}):t.addEventListener(e,n,!1)}function Dc(t,e,n,l,i){var r=l;if((e&1)===0&&(e&2)===0&&l!==null)t:for(;;){if(l===null)return;var o=l.tag;if(o===3||o===4){var d=l.stateNode.containerInfo;if(d===i)break;if(o===4)for(o=l.return;o!==null;){var m=o.tag;if((m===3||m===4)&&o.stateNode.containerInfo===i)return;o=o.return}for(;d!==null;){if(o=tl(d),o===null)return;if(m=o.tag,m===5||m===6||m===26||m===27){l=r=o;continue t}d=d.parentNode}}l=l.return}Ir(function(){var A=r,w=Wu(n),U=[];t:{var R=No.get(t);if(R!==void 0){var O=ci,F=t;switch(t){case"keypress":if(ui(n)===0)break t;case"keydown":case"keyup":O=Om;break;case"focusin":F="focus",O=es;break;case"focusout":F="blur",O=es;break;case"beforeblur":case"afterblur":O=es;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":O=no;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":O=ym;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":O=wm;break;case To:case Ro:case Oo:O=gm;break;case xo:O=Cm;break;case"scroll":case"scrollend":O=dm;break;case"wheel":O=zm;break;case"copy":case"cut":case"paste":O=bm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":O=ao;break;case"toggle":case"beforetoggle":O=Bm}var K=(e&4)!==0,pt=!K&&(t==="scroll"||t==="scrollend"),E=K?R!==null?R+"Capture":null:R;K=[];for(var v=A,_;v!==null;){var C=v;if(_=C.stateNode,C=C.tag,C!==5&&C!==26&&C!==27||_===null||E===null||(C=Jl(v,E),C!=null&&K.push(Da(v,C,_))),pt)break;v=v.return}0<K.length&&(R=new O(R,F,null,n,w),U.push({event:R,listeners:K}))}}if((e&7)===0){t:{if(R=t==="mouseover"||t==="pointerover",O=t==="mouseout"||t==="pointerout",R&&n!==Ju&&(F=n.relatedTarget||n.fromElement)&&(tl(F)||F[In]))break t;if((O||R)&&(R=w.window===w?w:(R=w.ownerDocument)?R.defaultView||R.parentWindow:window,O?(F=n.relatedTarget||n.toElement,O=A,F=F?tl(F):null,F!==null&&(pt=h(F),K=F.tag,F!==pt||K!==5&&K!==27&&K!==6)&&(F=null)):(O=null,F=A),O!==F)){if(K=no,C="onMouseLeave",E="onMouseEnter",v="mouse",(t==="pointerout"||t==="pointerover")&&(K=ao,C="onPointerLeave",E="onPointerEnter",v="pointer"),pt=O==null?R:Kl(O),_=F==null?R:Kl(F),R=new K(C,v+"leave",O,n,w),R.target=pt,R.relatedTarget=_,C=null,tl(w)===A&&(K=new K(E,v+"enter",F,n,w),K.target=_,K.relatedTarget=pt,C=K),pt=C,O&&F)e:{for(K=O,E=F,v=0,_=K;_;_=Ml(_))v++;for(_=0,C=E;C;C=Ml(C))_++;for(;0<v-_;)K=Ml(K),v--;for(;0<_-v;)E=Ml(E),_--;for(;v--;){if(K===E||E!==null&&K===E.alternate)break e;K=Ml(K),E=Ml(E)}K=null}else K=null;O!==null&&Wd(U,R,O,K,!1),F!==null&&pt!==null&&Wd(U,pt,F,K,!0)}}t:{if(R=A?Kl(A):window,O=R.nodeName&&R.nodeName.toLowerCase(),O==="select"||O==="input"&&R.type==="file")var V=ho;else if(oo(R))if(yo)V=Qm;else{V=Vm;var at=Gm}else O=R.nodeName,!O||O.toLowerCase()!=="input"||R.type!=="checkbox"&&R.type!=="radio"?A&&Ku(A.elementType)&&(V=ho):V=km;if(V&&(V=V(t,A))){fo(U,V,n,w);break t}at&&at(t,R,A),t==="focusout"&&A&&R.type==="number"&&A.memoizedProps.value!=null&&Zu(R,"number",R.value)}switch(at=A?Kl(A):window,t){case"focusin":(oo(at)||at.contentEditable==="true")&&(ol=at,ss=A,na=null);break;case"focusout":na=ss=ol=null;break;case"mousedown":cs=!0;break;case"contextmenu":case"mouseup":case"dragend":cs=!1,_o(U,n,w);break;case"selectionchange":if(Km)break;case"keydown":case"keyup":_o(U,n,w)}var k;if(ls)t:{switch(t){case"compositionstart":var J="onCompositionStart";break t;case"compositionend":J="onCompositionEnd";break t;case"compositionupdate":J="onCompositionUpdate";break t}J=void 0}else rl?co(t,n)&&(J="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(J="onCompositionStart");J&&(io&&n.locale!=="ko"&&(rl||J!=="onCompositionStart"?J==="onCompositionEnd"&&rl&&(k=to()):(un=w,Pu="value"in un?un.value:un.textContent,rl=!0)),at=Wi(A,J),0<at.length&&(J=new lo(J,t,null,n,w),U.push({event:J,listeners:at}),k?J.data=k:(k=ro(n),k!==null&&(J.data=k)))),(k=qm?Lm(t,n):Hm(t,n))&&(J=Wi(A,"onBeforeInput"),0<J.length&&(at=new lo("onBeforeInput","beforeinput",null,n,w),U.push({event:at,listeners:J}),at.data=k)),wp(U,t,A,n,w)}Kd(U,e)})}function Da(t,e,n){return{instance:t,listener:e,currentTarget:n}}function Wi(t,e){for(var n=e+"Capture",l=[];t!==null;){var i=t,r=i.stateNode;if(i=i.tag,i!==5&&i!==26&&i!==27||r===null||(i=Jl(t,n),i!=null&&l.unshift(Da(t,i,r)),i=Jl(t,e),i!=null&&l.push(Da(t,i,r))),t.tag===3)return l;t=t.return}return[]}function Ml(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Wd(t,e,n,l,i){for(var r=e._reactName,o=[];n!==null&&n!==l;){var d=n,m=d.alternate,A=d.stateNode;if(d=d.tag,m!==null&&m===l)break;d!==5&&d!==26&&d!==27||A===null||(m=A,i?(A=Jl(n,r),A!=null&&o.unshift(Da(n,A,m))):i||(A=Jl(n,r),A!=null&&o.push(Da(n,A,m)))),n=n.return}o.length!==0&&t.push({event:e,listeners:o})}var zp=/\r\n?/g,Mp=/\u0000|\uFFFD/g;function Fd(t){return(typeof t=="string"?t:""+t).replace(zp,`
`).replace(Mp,"")}function $d(t,e){return e=Fd(e),Fd(t)===e}function Fi(){}function mt(t,e,n,l,i,r){switch(n){case"children":typeof l=="string"?e==="body"||e==="textarea"&&l===""||ul(t,l):(typeof l=="number"||typeof l=="bigint")&&e!=="body"&&ul(t,""+l);break;case"className":ei(t,"class",l);break;case"tabIndex":ei(t,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":ei(t,n,l);break;case"style":$r(t,l,r);break;case"data":if(e!=="object"){ei(t,"data",l);break}case"src":case"href":if(l===""&&(e!=="a"||n!=="href")){t.removeAttribute(n);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=ai(""+l),t.setAttribute(n,l);break;case"action":case"formAction":if(typeof l=="function"){t.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof r=="function"&&(n==="formAction"?(e!=="input"&&mt(t,e,"name",i.name,i,null),mt(t,e,"formEncType",i.formEncType,i,null),mt(t,e,"formMethod",i.formMethod,i,null),mt(t,e,"formTarget",i.formTarget,i,null)):(mt(t,e,"encType",i.encType,i,null),mt(t,e,"method",i.method,i,null),mt(t,e,"target",i.target,i,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=ai(""+l),t.setAttribute(n,l);break;case"onClick":l!=null&&(t.onclick=Fi);break;case"onScroll":l!=null&&ut("scroll",t);break;case"onScrollEnd":l!=null&&ut("scrollend",t);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(c(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(c(60));t.innerHTML=n}}break;case"multiple":t.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":t.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){t.removeAttribute("xlink:href");break}n=ai(""+l),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""+l):t.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""):t.removeAttribute(n);break;case"capture":case"download":l===!0?t.setAttribute(n,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,l):t.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?t.setAttribute(n,l):t.removeAttribute(n);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?t.removeAttribute(n):t.setAttribute(n,l);break;case"popover":ut("beforetoggle",t),ut("toggle",t),ti(t,"popover",l);break;case"xlinkActuate":Xe(t,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":Xe(t,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":Xe(t,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":Xe(t,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":Xe(t,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":Xe(t,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":Xe(t,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":Xe(t,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":Xe(t,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":ti(t,"is",l);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=om.get(n)||n,ti(t,n,l))}}function Cc(t,e,n,l,i,r){switch(n){case"style":$r(t,l,r);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(c(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(c(60));t.innerHTML=n}}break;case"children":typeof l=="string"?ul(t,l):(typeof l=="number"||typeof l=="bigint")&&ul(t,""+l);break;case"onScroll":l!=null&&ut("scroll",t);break;case"onScrollEnd":l!=null&&ut("scrollend",t);break;case"onClick":l!=null&&(t.onclick=Fi);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Yr.hasOwnProperty(n))t:{if(n[0]==="o"&&n[1]==="n"&&(i=n.endsWith("Capture"),e=n.slice(2,i?n.length-7:void 0),r=t[Pt]||null,r=r!=null?r[n]:null,typeof r=="function"&&t.removeEventListener(e,r,i),typeof l=="function")){typeof r!="function"&&r!==null&&(n in t?t[n]=null:t.hasAttribute(n)&&t.removeAttribute(n)),t.addEventListener(e,l,i);break t}n in t?t[n]=l:l===!0?t.setAttribute(n,""):ti(t,n,l)}}}function kt(t,e,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ut("error",t),ut("load",t);var l=!1,i=!1,r;for(r in n)if(n.hasOwnProperty(r)){var o=n[r];if(o!=null)switch(r){case"src":l=!0;break;case"srcSet":i=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(c(137,e));default:mt(t,e,r,o,n,null)}}i&&mt(t,e,"srcSet",n.srcSet,n,null),l&&mt(t,e,"src",n.src,n,null);return;case"input":ut("invalid",t);var d=r=o=i=null,m=null,A=null;for(l in n)if(n.hasOwnProperty(l)){var w=n[l];if(w!=null)switch(l){case"name":i=w;break;case"type":o=w;break;case"checked":m=w;break;case"defaultChecked":A=w;break;case"value":r=w;break;case"defaultValue":d=w;break;case"children":case"dangerouslySetInnerHTML":if(w!=null)throw Error(c(137,e));break;default:mt(t,e,l,w,n,null)}}Kr(t,r,d,m,A,o,i,!1),ni(t);return;case"select":ut("invalid",t),l=o=r=null;for(i in n)if(n.hasOwnProperty(i)&&(d=n[i],d!=null))switch(i){case"value":r=d;break;case"defaultValue":o=d;break;case"multiple":l=d;default:mt(t,e,i,d,n,null)}e=r,n=o,t.multiple=!!l,e!=null?il(t,!!l,e,!1):n!=null&&il(t,!!l,n,!0);return;case"textarea":ut("invalid",t),r=i=l=null;for(o in n)if(n.hasOwnProperty(o)&&(d=n[o],d!=null))switch(o){case"value":l=d;break;case"defaultValue":i=d;break;case"children":r=d;break;case"dangerouslySetInnerHTML":if(d!=null)throw Error(c(91));break;default:mt(t,e,o,d,n,null)}Wr(t,l,i,r),ni(t);return;case"option":for(m in n)if(n.hasOwnProperty(m)&&(l=n[m],l!=null))switch(m){case"selected":t.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:mt(t,e,m,l,n,null)}return;case"dialog":ut("beforetoggle",t),ut("toggle",t),ut("cancel",t),ut("close",t);break;case"iframe":case"object":ut("load",t);break;case"video":case"audio":for(l=0;l<wa.length;l++)ut(wa[l],t);break;case"image":ut("error",t),ut("load",t);break;case"details":ut("toggle",t);break;case"embed":case"source":case"link":ut("error",t),ut("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(A in n)if(n.hasOwnProperty(A)&&(l=n[A],l!=null))switch(A){case"children":case"dangerouslySetInnerHTML":throw Error(c(137,e));default:mt(t,e,A,l,n,null)}return;default:if(Ku(e)){for(w in n)n.hasOwnProperty(w)&&(l=n[w],l!==void 0&&Cc(t,e,w,l,n,void 0));return}}for(d in n)n.hasOwnProperty(d)&&(l=n[d],l!=null&&mt(t,e,d,l,n,null))}function Bp(t,e,n,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,r=null,o=null,d=null,m=null,A=null,w=null;for(O in n){var U=n[O];if(n.hasOwnProperty(O)&&U!=null)switch(O){case"checked":break;case"value":break;case"defaultValue":m=U;default:l.hasOwnProperty(O)||mt(t,e,O,null,l,U)}}for(var R in l){var O=l[R];if(U=n[R],l.hasOwnProperty(R)&&(O!=null||U!=null))switch(R){case"type":r=O;break;case"name":i=O;break;case"checked":A=O;break;case"defaultChecked":w=O;break;case"value":o=O;break;case"defaultValue":d=O;break;case"children":case"dangerouslySetInnerHTML":if(O!=null)throw Error(c(137,e));break;default:O!==U&&mt(t,e,R,O,l,U)}}Qu(t,o,d,m,A,w,r,i);return;case"select":O=o=d=R=null;for(r in n)if(m=n[r],n.hasOwnProperty(r)&&m!=null)switch(r){case"value":break;case"multiple":O=m;default:l.hasOwnProperty(r)||mt(t,e,r,null,l,m)}for(i in l)if(r=l[i],m=n[i],l.hasOwnProperty(i)&&(r!=null||m!=null))switch(i){case"value":R=r;break;case"defaultValue":d=r;break;case"multiple":o=r;default:r!==m&&mt(t,e,i,r,l,m)}e=d,n=o,l=O,R!=null?il(t,!!n,R,!1):!!l!=!!n&&(e!=null?il(t,!!n,e,!0):il(t,!!n,n?[]:"",!1));return;case"textarea":O=R=null;for(d in n)if(i=n[d],n.hasOwnProperty(d)&&i!=null&&!l.hasOwnProperty(d))switch(d){case"value":break;case"children":break;default:mt(t,e,d,null,l,i)}for(o in l)if(i=l[o],r=n[o],l.hasOwnProperty(o)&&(i!=null||r!=null))switch(o){case"value":R=i;break;case"defaultValue":O=i;break;case"children":break;case"dangerouslySetInnerHTML":if(i!=null)throw Error(c(91));break;default:i!==r&&mt(t,e,o,i,l,r)}Jr(t,R,O);return;case"option":for(var F in n)if(R=n[F],n.hasOwnProperty(F)&&R!=null&&!l.hasOwnProperty(F))switch(F){case"selected":t.selected=!1;break;default:mt(t,e,F,null,l,R)}for(m in l)if(R=l[m],O=n[m],l.hasOwnProperty(m)&&R!==O&&(R!=null||O!=null))switch(m){case"selected":t.selected=R&&typeof R!="function"&&typeof R!="symbol";break;default:mt(t,e,m,R,l,O)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var K in n)R=n[K],n.hasOwnProperty(K)&&R!=null&&!l.hasOwnProperty(K)&&mt(t,e,K,null,l,R);for(A in l)if(R=l[A],O=n[A],l.hasOwnProperty(A)&&R!==O&&(R!=null||O!=null))switch(A){case"children":case"dangerouslySetInnerHTML":if(R!=null)throw Error(c(137,e));break;default:mt(t,e,A,R,l,O)}return;default:if(Ku(e)){for(var pt in n)R=n[pt],n.hasOwnProperty(pt)&&R!==void 0&&!l.hasOwnProperty(pt)&&Cc(t,e,pt,void 0,l,R);for(w in l)R=l[w],O=n[w],!l.hasOwnProperty(w)||R===O||R===void 0&&O===void 0||Cc(t,e,w,R,l,O);return}}for(var E in n)R=n[E],n.hasOwnProperty(E)&&R!=null&&!l.hasOwnProperty(E)&&mt(t,e,E,null,l,R);for(U in l)R=l[U],O=n[U],!l.hasOwnProperty(U)||R===O||R==null&&O==null||mt(t,e,U,R,l,O)}var Uc=null,zc=null;function $i(t){return t.nodeType===9?t:t.ownerDocument}function Pd(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Id(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function Mc(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var Bc=null;function jp(){var t=window.event;return t&&t.type==="popstate"?t===Bc?!1:(Bc=t,!0):(Bc=null,!1)}var th=typeof setTimeout=="function"?setTimeout:void 0,qp=typeof clearTimeout=="function"?clearTimeout:void 0,eh=typeof Promise=="function"?Promise:void 0,Lp=typeof queueMicrotask=="function"?queueMicrotask:typeof eh<"u"?function(t){return eh.resolve(null).then(t).catch(Hp)}:th;function Hp(t){setTimeout(function(){throw t})}function _n(t){return t==="head"}function nh(t,e){var n=e,l=0,i=0;do{var r=n.nextSibling;if(t.removeChild(n),r&&r.nodeType===8)if(n=r.data,n==="/$"){if(0<l&&8>l){n=l;var o=t.ownerDocument;if(n&1&&Ca(o.documentElement),n&2&&Ca(o.body),n&4)for(n=o.head,Ca(n),o=n.firstChild;o;){var d=o.nextSibling,m=o.nodeName;o[Zl]||m==="SCRIPT"||m==="STYLE"||m==="LINK"&&o.rel.toLowerCase()==="stylesheet"||n.removeChild(o),o=d}}if(i===0){t.removeChild(r),Ha(e);return}i--}else n==="$"||n==="$?"||n==="$!"?i++:l=n.charCodeAt(0)-48;else l=0;n=r}while(n);Ha(e)}function jc(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var n=e;switch(e=e.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":jc(n),Xu(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}t.removeChild(n)}}function Yp(t,e,n,l){for(;t.nodeType===1;){var i=n;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!l&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(l){if(!t[Zl])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(r=t.getAttribute("rel"),r==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(r!==i.rel||t.getAttribute("href")!==(i.href==null||i.href===""?null:i.href)||t.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin)||t.getAttribute("title")!==(i.title==null?null:i.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(r=t.getAttribute("src"),(r!==(i.src==null?null:i.src)||t.getAttribute("type")!==(i.type==null?null:i.type)||t.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin))&&r&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var r=i.name==null?null:""+i.name;if(i.type==="hidden"&&t.getAttribute("name")===r)return t}else return t;if(t=Ne(t.nextSibling),t===null)break}return null}function Xp(t,e,n){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!n||(t=Ne(t.nextSibling),t===null))return null;return t}function qc(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function Gp(t,e){var n=t.ownerDocument;if(t.data!=="$?"||n.readyState==="complete")e();else{var l=function(){e(),n.removeEventListener("DOMContentLoaded",l)};n.addEventListener("DOMContentLoaded",l),t._reactRetry=l}}function Ne(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var Lc=null;function lh(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}function ah(t,e,n){switch(e=$i(n),t){case"html":if(t=e.documentElement,!t)throw Error(c(452));return t;case"head":if(t=e.head,!t)throw Error(c(453));return t;case"body":if(t=e.body,!t)throw Error(c(454));return t;default:throw Error(c(451))}}function Ca(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);Xu(t)}var Te=new Map,ih=new Set;function Pi(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var en=X.d;X.d={f:Vp,r:kp,D:Qp,C:Zp,L:Kp,m:Jp,X:Fp,S:Wp,M:$p};function Vp(){var t=en.f(),e=Vi();return t||e}function kp(t){var e=el(t);e!==null&&e.tag===5&&e.type==="form"?Rf(e):en.r(t)}var Bl=typeof document>"u"?null:document;function uh(t,e,n){var l=Bl;if(l&&typeof e=="string"&&e){var i=ge(e);i='link[rel="'+t+'"][href="'+i+'"]',typeof n=="string"&&(i+='[crossorigin="'+n+'"]'),ih.has(i)||(ih.add(i),t={rel:t,crossOrigin:n,href:e},l.querySelector(i)===null&&(e=l.createElement("link"),kt(e,"link",t),qt(e),l.head.appendChild(e)))}}function Qp(t){en.D(t),uh("dns-prefetch",t,null)}function Zp(t,e){en.C(t,e),uh("preconnect",t,e)}function Kp(t,e,n){en.L(t,e,n);var l=Bl;if(l&&t&&e){var i='link[rel="preload"][as="'+ge(e)+'"]';e==="image"&&n&&n.imageSrcSet?(i+='[imagesrcset="'+ge(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(i+='[imagesizes="'+ge(n.imageSizes)+'"]')):i+='[href="'+ge(t)+'"]';var r=i;switch(e){case"style":r=jl(t);break;case"script":r=ql(t)}Te.has(r)||(t=b({rel:"preload",href:e==="image"&&n&&n.imageSrcSet?void 0:t,as:e},n),Te.set(r,t),l.querySelector(i)!==null||e==="style"&&l.querySelector(Ua(r))||e==="script"&&l.querySelector(za(r))||(e=l.createElement("link"),kt(e,"link",t),qt(e),l.head.appendChild(e)))}}function Jp(t,e){en.m(t,e);var n=Bl;if(n&&t){var l=e&&typeof e.as=="string"?e.as:"script",i='link[rel="modulepreload"][as="'+ge(l)+'"][href="'+ge(t)+'"]',r=i;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":r=ql(t)}if(!Te.has(r)&&(t=b({rel:"modulepreload",href:t},e),Te.set(r,t),n.querySelector(i)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(za(r)))return}l=n.createElement("link"),kt(l,"link",t),qt(l),n.head.appendChild(l)}}}function Wp(t,e,n){en.S(t,e,n);var l=Bl;if(l&&t){var i=nl(l).hoistableStyles,r=jl(t);e=e||"default";var o=i.get(r);if(!o){var d={loading:0,preload:null};if(o=l.querySelector(Ua(r)))d.loading=5;else{t=b({rel:"stylesheet",href:t,"data-precedence":e},n),(n=Te.get(r))&&Hc(t,n);var m=o=l.createElement("link");qt(m),kt(m,"link",t),m._p=new Promise(function(A,w){m.onload=A,m.onerror=w}),m.addEventListener("load",function(){d.loading|=1}),m.addEventListener("error",function(){d.loading|=2}),d.loading|=4,Ii(o,e,l)}o={type:"stylesheet",instance:o,count:1,state:d},i.set(r,o)}}}function Fp(t,e){en.X(t,e);var n=Bl;if(n&&t){var l=nl(n).hoistableScripts,i=ql(t),r=l.get(i);r||(r=n.querySelector(za(i)),r||(t=b({src:t,async:!0},e),(e=Te.get(i))&&Yc(t,e),r=n.createElement("script"),qt(r),kt(r,"link",t),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},l.set(i,r))}}function $p(t,e){en.M(t,e);var n=Bl;if(n&&t){var l=nl(n).hoistableScripts,i=ql(t),r=l.get(i);r||(r=n.querySelector(za(i)),r||(t=b({src:t,async:!0,type:"module"},e),(e=Te.get(i))&&Yc(t,e),r=n.createElement("script"),qt(r),kt(r,"link",t),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},l.set(i,r))}}function sh(t,e,n,l){var i=(i=P.current)?Pi(i):null;if(!i)throw Error(c(446));switch(t){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(e=jl(n.href),n=nl(i).hoistableStyles,l=n.get(e),l||(l={type:"style",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){t=jl(n.href);var r=nl(i).hoistableStyles,o=r.get(t);if(o||(i=i.ownerDocument||i,o={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},r.set(t,o),(r=i.querySelector(Ua(t)))&&!r._p&&(o.instance=r,o.state.loading=5),Te.has(t)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Te.set(t,n),r||Pp(i,t,n,o.state))),e&&l===null)throw Error(c(528,""));return o}if(e&&l!==null)throw Error(c(529,""));return null;case"script":return e=n.async,n=n.src,typeof n=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=ql(n),n=nl(i).hoistableScripts,l=n.get(e),l||(l={type:"script",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(c(444,t))}}function jl(t){return'href="'+ge(t)+'"'}function Ua(t){return'link[rel="stylesheet"]['+t+"]"}function ch(t){return b({},t,{"data-precedence":t.precedence,precedence:null})}function Pp(t,e,n,l){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?l.loading=1:(e=t.createElement("link"),l.preload=e,e.addEventListener("load",function(){return l.loading|=1}),e.addEventListener("error",function(){return l.loading|=2}),kt(e,"link",n),qt(e),t.head.appendChild(e))}function ql(t){return'[src="'+ge(t)+'"]'}function za(t){return"script[async]"+t}function rh(t,e,n){if(e.count++,e.instance===null)switch(e.type){case"style":var l=t.querySelector('style[data-href~="'+ge(n.href)+'"]');if(l)return e.instance=l,qt(l),l;var i=b({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return l=(t.ownerDocument||t).createElement("style"),qt(l),kt(l,"style",i),Ii(l,n.precedence,t),e.instance=l;case"stylesheet":i=jl(n.href);var r=t.querySelector(Ua(i));if(r)return e.state.loading|=4,e.instance=r,qt(r),r;l=ch(n),(i=Te.get(i))&&Hc(l,i),r=(t.ownerDocument||t).createElement("link"),qt(r);var o=r;return o._p=new Promise(function(d,m){o.onload=d,o.onerror=m}),kt(r,"link",l),e.state.loading|=4,Ii(r,n.precedence,t),e.instance=r;case"script":return r=ql(n.src),(i=t.querySelector(za(r)))?(e.instance=i,qt(i),i):(l=n,(i=Te.get(r))&&(l=b({},n),Yc(l,i)),t=t.ownerDocument||t,i=t.createElement("script"),qt(i),kt(i,"link",l),t.head.appendChild(i),e.instance=i);case"void":return null;default:throw Error(c(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(l=e.instance,e.state.loading|=4,Ii(l,n.precedence,t));return e.instance}function Ii(t,e,n){for(var l=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=l.length?l[l.length-1]:null,r=i,o=0;o<l.length;o++){var d=l[o];if(d.dataset.precedence===e)r=d;else if(r!==i)break}r?r.parentNode.insertBefore(t,r.nextSibling):(e=n.nodeType===9?n.head:n,e.insertBefore(t,e.firstChild))}function Hc(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function Yc(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var tu=null;function oh(t,e,n){if(tu===null){var l=new Map,i=tu=new Map;i.set(n,l)}else i=tu,l=i.get(n),l||(l=new Map,i.set(n,l));if(l.has(t))return l;for(l.set(t,null),n=n.getElementsByTagName(t),i=0;i<n.length;i++){var r=n[i];if(!(r[Zl]||r[Kt]||t==="link"&&r.getAttribute("rel")==="stylesheet")&&r.namespaceURI!=="http://www.w3.org/2000/svg"){var o=r.getAttribute(e)||"";o=t+o;var d=l.get(o);d?d.push(r):l.set(o,[r])}}return l}function fh(t,e,n){t=t.ownerDocument||t,t.head.insertBefore(n,e==="title"?t.querySelector("head > title"):null)}function Ip(t,e,n){if(n===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function dh(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Ma=null;function t0(){}function e0(t,e,n){if(Ma===null)throw Error(c(475));var l=Ma;if(e.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var i=jl(n.href),r=t.querySelector(Ua(i));if(r){t=r._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(l.count++,l=eu.bind(l),t.then(l,l)),e.state.loading|=4,e.instance=r,qt(r);return}r=t.ownerDocument||t,n=ch(n),(i=Te.get(i))&&Hc(n,i),r=r.createElement("link"),qt(r);var o=r;o._p=new Promise(function(d,m){o.onload=d,o.onerror=m}),kt(r,"link",n),e.instance=r}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(l.count++,e=eu.bind(l),t.addEventListener("load",e),t.addEventListener("error",e))}}function n0(){if(Ma===null)throw Error(c(475));var t=Ma;return t.stylesheets&&t.count===0&&Xc(t,t.stylesheets),0<t.count?function(e){var n=setTimeout(function(){if(t.stylesheets&&Xc(t,t.stylesheets),t.unsuspend){var l=t.unsuspend;t.unsuspend=null,l()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(n)}}:null}function eu(){if(this.count--,this.count===0){if(this.stylesheets)Xc(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var nu=null;function Xc(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,nu=new Map,e.forEach(l0,t),nu=null,eu.call(t))}function l0(t,e){if(!(e.state.loading&4)){var n=nu.get(t);if(n)var l=n.get(null);else{n=new Map,nu.set(t,n);for(var i=t.querySelectorAll("link[data-precedence],style[data-precedence]"),r=0;r<i.length;r++){var o=i[r];(o.nodeName==="LINK"||o.getAttribute("media")!=="not all")&&(n.set(o.dataset.precedence,o),l=o)}l&&n.set(null,l)}i=e.instance,o=i.getAttribute("data-precedence"),r=n.get(o)||l,r===l&&n.set(null,i),n.set(o,i),this.count++,l=eu.bind(this),i.addEventListener("load",l),i.addEventListener("error",l),r?r.parentNode.insertBefore(i,r.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(i,t.firstChild)),e.state.loading|=4}}var Ba={$$typeof:nt,Provider:null,Consumer:null,_currentValue:W,_currentValue2:W,_threadCount:0};function a0(t,e,n,l,i,r,o,d){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=qu(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=qu(0),this.hiddenUpdates=qu(null),this.identifierPrefix=l,this.onUncaughtError=i,this.onCaughtError=r,this.onRecoverableError=o,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=d,this.incompleteTransitions=new Map}function hh(t,e,n,l,i,r,o,d,m,A,w,U){return t=new a0(t,e,n,o,d,m,A,U),e=1,r===!0&&(e|=24),r=oe(3,null,null,e),t.current=r,r.stateNode=t,e=_s(),e.refCount++,t.pooledCache=e,e.refCount++,r.memoizedState={element:l,isDehydrated:n,cache:e},Os(r),t}function yh(t){return t?(t=yl,t):yl}function mh(t,e,n,l,i,r){i=yh(i),l.context===null?l.context=i:l.pendingContext=i,l=rn(e),l.payload={element:n},r=r===void 0?null:r,r!==null&&(l.callback=r),n=on(t,l,e),n!==null&&(me(n,t,e),fa(n,t,e))}function ph(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function Gc(t,e){ph(t,e),(t=t.alternate)&&ph(t,e)}function gh(t){if(t.tag===13){var e=hl(t,67108864);e!==null&&me(e,t,67108864),Gc(t,67108864)}}var lu=!0;function i0(t,e,n,l){var i=D.T;D.T=null;var r=X.p;try{X.p=2,Vc(t,e,n,l)}finally{X.p=r,D.T=i}}function u0(t,e,n,l){var i=D.T;D.T=null;var r=X.p;try{X.p=8,Vc(t,e,n,l)}finally{X.p=r,D.T=i}}function Vc(t,e,n,l){if(lu){var i=kc(l);if(i===null)Dc(t,e,l,au,n),bh(t,l);else if(c0(i,t,e,n,l))l.stopPropagation();else if(bh(t,l),e&4&&-1<s0.indexOf(t)){for(;i!==null;){var r=el(i);if(r!==null)switch(r.tag){case 3:if(r=r.stateNode,r.current.memoizedState.isDehydrated){var o=Cn(r.pendingLanes);if(o!==0){var d=r;for(d.pendingLanes|=2,d.entangledLanes|=2;o;){var m=1<<31-ce(o);d.entanglements[1]|=m,o&=~m}je(r),(dt&6)===0&&(Xi=Ce()+500,Na(0))}}break;case 13:d=hl(r,2),d!==null&&me(d,r,2),Vi(),Gc(r,2)}if(r=kc(l),r===null&&Dc(t,e,l,au,n),r===i)break;i=r}i!==null&&l.stopPropagation()}else Dc(t,e,l,null,n)}}function kc(t){return t=Wu(t),Qc(t)}var au=null;function Qc(t){if(au=null,t=tl(t),t!==null){var e=h(t);if(e===null)t=null;else{var n=e.tag;if(n===13){if(t=y(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return au=t,null}function vh(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Zy()){case Dr:return 2;case Cr:return 8;case Fa:case Ky:return 32;case Ur:return 268435456;default:return 32}default:return 32}}var Zc=!1,An=null,Tn=null,Rn=null,ja=new Map,qa=new Map,On=[],s0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function bh(t,e){switch(t){case"focusin":case"focusout":An=null;break;case"dragenter":case"dragleave":Tn=null;break;case"mouseover":case"mouseout":Rn=null;break;case"pointerover":case"pointerout":ja.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":qa.delete(e.pointerId)}}function La(t,e,n,l,i,r){return t===null||t.nativeEvent!==r?(t={blockedOn:e,domEventName:n,eventSystemFlags:l,nativeEvent:r,targetContainers:[i]},e!==null&&(e=el(e),e!==null&&gh(e)),t):(t.eventSystemFlags|=l,e=t.targetContainers,i!==null&&e.indexOf(i)===-1&&e.push(i),t)}function c0(t,e,n,l,i){switch(e){case"focusin":return An=La(An,t,e,n,l,i),!0;case"dragenter":return Tn=La(Tn,t,e,n,l,i),!0;case"mouseover":return Rn=La(Rn,t,e,n,l,i),!0;case"pointerover":var r=i.pointerId;return ja.set(r,La(ja.get(r)||null,t,e,n,l,i)),!0;case"gotpointercapture":return r=i.pointerId,qa.set(r,La(qa.get(r)||null,t,e,n,l,i)),!0}return!1}function Sh(t){var e=tl(t.target);if(e!==null){var n=h(e);if(n!==null){if(e=n.tag,e===13){if(e=y(n),e!==null){t.blockedOn=e,em(t.priority,function(){if(n.tag===13){var l=ye();l=Lu(l);var i=hl(n,l);i!==null&&me(i,n,l),Gc(n,l)}});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function iu(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=kc(t.nativeEvent);if(n===null){n=t.nativeEvent;var l=new n.constructor(n.type,n);Ju=l,n.target.dispatchEvent(l),Ju=null}else return e=el(n),e!==null&&gh(e),t.blockedOn=n,!1;e.shift()}return!0}function Eh(t,e,n){iu(t)&&n.delete(e)}function r0(){Zc=!1,An!==null&&iu(An)&&(An=null),Tn!==null&&iu(Tn)&&(Tn=null),Rn!==null&&iu(Rn)&&(Rn=null),ja.forEach(Eh),qa.forEach(Eh)}function uu(t,e){t.blockedOn===e&&(t.blockedOn=null,Zc||(Zc=!0,u.unstable_scheduleCallback(u.unstable_NormalPriority,r0)))}var su=null;function _h(t){su!==t&&(su=t,u.unstable_scheduleCallback(u.unstable_NormalPriority,function(){su===t&&(su=null);for(var e=0;e<t.length;e+=3){var n=t[e],l=t[e+1],i=t[e+2];if(typeof l!="function"){if(Qc(l||n)===null)continue;break}var r=el(n);r!==null&&(t.splice(e,3),e-=3,Qs(r,{pending:!0,data:i,method:n.method,action:l},l,i))}}))}function Ha(t){function e(m){return uu(m,t)}An!==null&&uu(An,t),Tn!==null&&uu(Tn,t),Rn!==null&&uu(Rn,t),ja.forEach(e),qa.forEach(e);for(var n=0;n<On.length;n++){var l=On[n];l.blockedOn===t&&(l.blockedOn=null)}for(;0<On.length&&(n=On[0],n.blockedOn===null);)Sh(n),n.blockedOn===null&&On.shift();if(n=(t.ownerDocument||t).$$reactFormReplay,n!=null)for(l=0;l<n.length;l+=3){var i=n[l],r=n[l+1],o=i[Pt]||null;if(typeof r=="function")o||_h(n);else if(o){var d=null;if(r&&r.hasAttribute("formAction")){if(i=r,o=r[Pt]||null)d=o.formAction;else if(Qc(i)!==null)continue}else d=o.action;typeof d=="function"?n[l+1]=d:(n.splice(l,3),l-=3),_h(n)}}}function Kc(t){this._internalRoot=t}cu.prototype.render=Kc.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(c(409));var n=e.current,l=ye();mh(n,l,t,e,null,null)},cu.prototype.unmount=Kc.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;mh(t.current,2,null,t,null,null),Vi(),e[In]=null}};function cu(t){this._internalRoot=t}cu.prototype.unstable_scheduleHydration=function(t){if(t){var e=qr();t={blockedOn:null,target:t,priority:e};for(var n=0;n<On.length&&e!==0&&e<On[n].priority;n++);On.splice(n,0,t),n===0&&Sh(t)}};var Ah=a.version;if(Ah!=="19.1.0")throw Error(c(527,Ah,"19.1.0"));X.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(c(188)):(t=Object.keys(t).join(","),Error(c(268,t)));return t=T(e),t=t!==null?p(t):null,t=t===null?null:t.stateNode,t};var o0={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:D,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ru=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ru.isDisabled&&ru.supportsFiber)try{Vl=ru.inject(o0),se=ru}catch{}}return Xa.createRoot=function(t,e){if(!f(t))throw Error(c(299));var n=!1,l="",i=Hf,r=Yf,o=Xf,d=null;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(l=e.identifierPrefix),e.onUncaughtError!==void 0&&(i=e.onUncaughtError),e.onCaughtError!==void 0&&(r=e.onCaughtError),e.onRecoverableError!==void 0&&(o=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(d=e.unstable_transitionCallbacks)),e=hh(t,1,!1,null,null,n,l,i,r,o,d,null),t[In]=e.current,wc(t),new Kc(e)},Xa.hydrateRoot=function(t,e,n){if(!f(t))throw Error(c(299));var l=!1,i="",r=Hf,o=Yf,d=Xf,m=null,A=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onUncaughtError!==void 0&&(r=n.onUncaughtError),n.onCaughtError!==void 0&&(o=n.onCaughtError),n.onRecoverableError!==void 0&&(d=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(m=n.unstable_transitionCallbacks),n.formState!==void 0&&(A=n.formState)),e=hh(t,1,!0,e,n??null,l,i,r,o,d,m,A),e.context=yh(null),n=e.current,l=ye(),l=Lu(l),i=rn(l),i.callback=null,on(n,i,l),n=l,e.current.lanes=n,Ql(e,n),je(e),t[In]=e.current,wc(t),new cu(e)},Xa.version="19.1.0",Xa}var Mh;function E0(){if(Mh)return Fc.exports;Mh=1;function u(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(u)}catch(a){console.error(a)}}return u(),Fc.exports=S0(),Fc.exports}var _0=E0();function ly(u,a){return function(){return u.apply(a,arguments)}}const{toString:A0}=Object.prototype,{getPrototypeOf:_r}=Object,{iterator:Tu,toStringTag:ay}=Symbol,Ru=(u=>a=>{const s=A0.call(a);return u[s]||(u[s]=s.slice(8,-1).toLowerCase())})(Object.create(null)),De=u=>(u=u.toLowerCase(),a=>Ru(a)===u),Ou=u=>a=>typeof a===u,{isArray:Yl}=Array,Qa=Ou("undefined");function T0(u){return u!==null&&!Qa(u)&&u.constructor!==null&&!Qa(u.constructor)&&le(u.constructor.isBuffer)&&u.constructor.isBuffer(u)}const iy=De("ArrayBuffer");function R0(u){let a;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?a=ArrayBuffer.isView(u):a=u&&u.buffer&&iy(u.buffer),a}const O0=Ou("string"),le=Ou("function"),uy=Ou("number"),xu=u=>u!==null&&typeof u=="object",x0=u=>u===!0||u===!1,hu=u=>{if(Ru(u)!=="object")return!1;const a=_r(u);return(a===null||a===Object.prototype||Object.getPrototypeOf(a)===null)&&!(ay in u)&&!(Tu in u)},N0=De("Date"),w0=De("File"),D0=De("Blob"),C0=De("FileList"),U0=u=>xu(u)&&le(u.pipe),z0=u=>{let a;return u&&(typeof FormData=="function"&&u instanceof FormData||le(u.append)&&((a=Ru(u))==="formdata"||a==="object"&&le(u.toString)&&u.toString()==="[object FormData]"))},M0=De("URLSearchParams"),[B0,j0,q0,L0]=["ReadableStream","Request","Response","Headers"].map(De),H0=u=>u.trim?u.trim():u.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Ka(u,a,{allOwnKeys:s=!1}={}){if(u===null||typeof u>"u")return;let c,f;if(typeof u!="object"&&(u=[u]),Yl(u))for(c=0,f=u.length;c<f;c++)a.call(null,u[c],c,u);else{const h=s?Object.getOwnPropertyNames(u):Object.keys(u),y=h.length;let S;for(c=0;c<y;c++)S=h[c],a.call(null,u[S],S,u)}}function sy(u,a){a=a.toLowerCase();const s=Object.keys(u);let c=s.length,f;for(;c-- >0;)if(f=s[c],a===f.toLowerCase())return f;return null}const Wn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,cy=u=>!Qa(u)&&u!==Wn;function cr(){const{caseless:u}=cy(this)&&this||{},a={},s=(c,f)=>{const h=u&&sy(a,f)||f;hu(a[h])&&hu(c)?a[h]=cr(a[h],c):hu(c)?a[h]=cr({},c):Yl(c)?a[h]=c.slice():a[h]=c};for(let c=0,f=arguments.length;c<f;c++)arguments[c]&&Ka(arguments[c],s);return a}const Y0=(u,a,s,{allOwnKeys:c}={})=>(Ka(a,(f,h)=>{s&&le(f)?u[h]=ly(f,s):u[h]=f},{allOwnKeys:c}),u),X0=u=>(u.charCodeAt(0)===65279&&(u=u.slice(1)),u),G0=(u,a,s,c)=>{u.prototype=Object.create(a.prototype,c),u.prototype.constructor=u,Object.defineProperty(u,"super",{value:a.prototype}),s&&Object.assign(u.prototype,s)},V0=(u,a,s,c)=>{let f,h,y;const S={};if(a=a||{},u==null)return a;do{for(f=Object.getOwnPropertyNames(u),h=f.length;h-- >0;)y=f[h],(!c||c(y,u,a))&&!S[y]&&(a[y]=u[y],S[y]=!0);u=s!==!1&&_r(u)}while(u&&(!s||s(u,a))&&u!==Object.prototype);return a},k0=(u,a,s)=>{u=String(u),(s===void 0||s>u.length)&&(s=u.length),s-=a.length;const c=u.indexOf(a,s);return c!==-1&&c===s},Q0=u=>{if(!u)return null;if(Yl(u))return u;let a=u.length;if(!uy(a))return null;const s=new Array(a);for(;a-- >0;)s[a]=u[a];return s},Z0=(u=>a=>u&&a instanceof u)(typeof Uint8Array<"u"&&_r(Uint8Array)),K0=(u,a)=>{const c=(u&&u[Tu]).call(u);let f;for(;(f=c.next())&&!f.done;){const h=f.value;a.call(u,h[0],h[1])}},J0=(u,a)=>{let s;const c=[];for(;(s=u.exec(a))!==null;)c.push(s);return c},W0=De("HTMLFormElement"),F0=u=>u.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(s,c,f){return c.toUpperCase()+f}),Bh=(({hasOwnProperty:u})=>(a,s)=>u.call(a,s))(Object.prototype),$0=De("RegExp"),ry=(u,a)=>{const s=Object.getOwnPropertyDescriptors(u),c={};Ka(s,(f,h)=>{let y;(y=a(f,h,u))!==!1&&(c[h]=y||f)}),Object.defineProperties(u,c)},P0=u=>{ry(u,(a,s)=>{if(le(u)&&["arguments","caller","callee"].indexOf(s)!==-1)return!1;const c=u[s];if(le(c)){if(a.enumerable=!1,"writable"in a){a.writable=!1;return}a.set||(a.set=()=>{throw Error("Can not rewrite read-only method '"+s+"'")})}})},I0=(u,a)=>{const s={},c=f=>{f.forEach(h=>{s[h]=!0})};return Yl(u)?c(u):c(String(u).split(a)),s},tg=()=>{},eg=(u,a)=>u!=null&&Number.isFinite(u=+u)?u:a;function ng(u){return!!(u&&le(u.append)&&u[ay]==="FormData"&&u[Tu])}const lg=u=>{const a=new Array(10),s=(c,f)=>{if(xu(c)){if(a.indexOf(c)>=0)return;if(!("toJSON"in c)){a[f]=c;const h=Yl(c)?[]:{};return Ka(c,(y,S)=>{const T=s(y,f+1);!Qa(T)&&(h[S]=T)}),a[f]=void 0,h}}return c};return s(u,0)},ag=De("AsyncFunction"),ig=u=>u&&(xu(u)||le(u))&&le(u.then)&&le(u.catch),oy=((u,a)=>u?setImmediate:a?((s,c)=>(Wn.addEventListener("message",({source:f,data:h})=>{f===Wn&&h===s&&c.length&&c.shift()()},!1),f=>{c.push(f),Wn.postMessage(s,"*")}))(`axios@${Math.random()}`,[]):s=>setTimeout(s))(typeof setImmediate=="function",le(Wn.postMessage)),ug=typeof queueMicrotask<"u"?queueMicrotask.bind(Wn):typeof process<"u"&&process.nextTick||oy,sg=u=>u!=null&&le(u[Tu]),x={isArray:Yl,isArrayBuffer:iy,isBuffer:T0,isFormData:z0,isArrayBufferView:R0,isString:O0,isNumber:uy,isBoolean:x0,isObject:xu,isPlainObject:hu,isReadableStream:B0,isRequest:j0,isResponse:q0,isHeaders:L0,isUndefined:Qa,isDate:N0,isFile:w0,isBlob:D0,isRegExp:$0,isFunction:le,isStream:U0,isURLSearchParams:M0,isTypedArray:Z0,isFileList:C0,forEach:Ka,merge:cr,extend:Y0,trim:H0,stripBOM:X0,inherits:G0,toFlatObject:V0,kindOf:Ru,kindOfTest:De,endsWith:k0,toArray:Q0,forEachEntry:K0,matchAll:J0,isHTMLForm:W0,hasOwnProperty:Bh,hasOwnProp:Bh,reduceDescriptors:ry,freezeMethods:P0,toObjectSet:I0,toCamelCase:F0,noop:tg,toFiniteNumber:eg,findKey:sy,global:Wn,isContextDefined:cy,isSpecCompliantForm:ng,toJSONObject:lg,isAsyncFn:ag,isThenable:ig,setImmediate:oy,asap:ug,isIterable:sg};function tt(u,a,s,c,f){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=u,this.name="AxiosError",a&&(this.code=a),s&&(this.config=s),c&&(this.request=c),f&&(this.response=f,this.status=f.status?f.status:null)}x.inherits(tt,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:x.toJSONObject(this.config),code:this.code,status:this.status}}});const fy=tt.prototype,dy={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(u=>{dy[u]={value:u}});Object.defineProperties(tt,dy);Object.defineProperty(fy,"isAxiosError",{value:!0});tt.from=(u,a,s,c,f,h)=>{const y=Object.create(fy);return x.toFlatObject(u,y,function(T){return T!==Error.prototype},S=>S!=="isAxiosError"),tt.call(y,u.message,a,s,c,f),y.cause=u,y.name=u.name,h&&Object.assign(y,h),y};const cg=null;function rr(u){return x.isPlainObject(u)||x.isArray(u)}function hy(u){return x.endsWith(u,"[]")?u.slice(0,-2):u}function jh(u,a,s){return u?u.concat(a).map(function(f,h){return f=hy(f),!s&&h?"["+f+"]":f}).join(s?".":""):a}function rg(u){return x.isArray(u)&&!u.some(rr)}const og=x.toFlatObject(x,{},null,function(a){return/^is[A-Z]/.test(a)});function Nu(u,a,s){if(!x.isObject(u))throw new TypeError("target must be an object");a=a||new FormData,s=x.toFlatObject(s,{metaTokens:!0,dots:!1,indexes:!1},!1,function(q,j){return!x.isUndefined(j[q])});const c=s.metaTokens,f=s.visitor||b,h=s.dots,y=s.indexes,T=(s.Blob||typeof Blob<"u"&&Blob)&&x.isSpecCompliantForm(a);if(!x.isFunction(f))throw new TypeError("visitor must be a function");function p(B){if(B===null)return"";if(x.isDate(B))return B.toISOString();if(x.isBoolean(B))return B.toString();if(!T&&x.isBlob(B))throw new tt("Blob is not supported. Use a Buffer instead.");return x.isArrayBuffer(B)||x.isTypedArray(B)?T&&typeof Blob=="function"?new Blob([B]):Buffer.from(B):B}function b(B,q,j){let Q=B;if(B&&!j&&typeof B=="object"){if(x.endsWith(q,"{}"))q=c?q:q.slice(0,-2),B=JSON.stringify(B);else if(x.isArray(B)&&rg(B)||(x.isFileList(B)||x.endsWith(q,"[]"))&&(Q=x.toArray(B)))return q=hy(q),Q.forEach(function(nt,Ot){!(x.isUndefined(nt)||nt===null)&&a.append(y===!0?jh([q],Ot,h):y===null?q:q+"[]",p(nt))}),!1}return rr(B)?!0:(a.append(jh(j,q,h),p(B)),!1)}const N=[],z=Object.assign(og,{defaultVisitor:b,convertValue:p,isVisitable:rr});function H(B,q){if(!x.isUndefined(B)){if(N.indexOf(B)!==-1)throw Error("Circular reference detected in "+q.join("."));N.push(B),x.forEach(B,function(Q,I){(!(x.isUndefined(Q)||Q===null)&&f.call(a,Q,x.isString(I)?I.trim():I,q,z))===!0&&H(Q,q?q.concat(I):[I])}),N.pop()}}if(!x.isObject(u))throw new TypeError("data must be an object");return H(u),a}function qh(u){const a={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(u).replace(/[!'()~]|%20|%00/g,function(c){return a[c]})}function Ar(u,a){this._pairs=[],u&&Nu(u,this,a)}const yy=Ar.prototype;yy.append=function(a,s){this._pairs.push([a,s])};yy.toString=function(a){const s=a?function(c){return a.call(this,c,qh)}:qh;return this._pairs.map(function(f){return s(f[0])+"="+s(f[1])},"").join("&")};function fg(u){return encodeURIComponent(u).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function my(u,a,s){if(!a)return u;const c=s&&s.encode||fg;x.isFunction(s)&&(s={serialize:s});const f=s&&s.serialize;let h;if(f?h=f(a,s):h=x.isURLSearchParams(a)?a.toString():new Ar(a,s).toString(c),h){const y=u.indexOf("#");y!==-1&&(u=u.slice(0,y)),u+=(u.indexOf("?")===-1?"?":"&")+h}return u}class Lh{constructor(){this.handlers=[]}use(a,s,c){return this.handlers.push({fulfilled:a,rejected:s,synchronous:c?c.synchronous:!1,runWhen:c?c.runWhen:null}),this.handlers.length-1}eject(a){this.handlers[a]&&(this.handlers[a]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(a){x.forEach(this.handlers,function(c){c!==null&&a(c)})}}const py={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},dg=typeof URLSearchParams<"u"?URLSearchParams:Ar,hg=typeof FormData<"u"?FormData:null,yg=typeof Blob<"u"?Blob:null,mg={isBrowser:!0,classes:{URLSearchParams:dg,FormData:hg,Blob:yg},protocols:["http","https","file","blob","url","data"]},Tr=typeof window<"u"&&typeof document<"u",or=typeof navigator=="object"&&navigator||void 0,pg=Tr&&(!or||["ReactNative","NativeScript","NS"].indexOf(or.product)<0),gg=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",vg=Tr&&window.location.href||"http://localhost",bg=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Tr,hasStandardBrowserEnv:pg,hasStandardBrowserWebWorkerEnv:gg,navigator:or,origin:vg},Symbol.toStringTag,{value:"Module"})),Ft={...bg,...mg};function Sg(u,a){return Nu(u,new Ft.classes.URLSearchParams,Object.assign({visitor:function(s,c,f,h){return Ft.isNode&&x.isBuffer(s)?(this.append(c,s.toString("base64")),!1):h.defaultVisitor.apply(this,arguments)}},a))}function Eg(u){return x.matchAll(/\w+|\[(\w*)]/g,u).map(a=>a[0]==="[]"?"":a[1]||a[0])}function _g(u){const a={},s=Object.keys(u);let c;const f=s.length;let h;for(c=0;c<f;c++)h=s[c],a[h]=u[h];return a}function gy(u){function a(s,c,f,h){let y=s[h++];if(y==="__proto__")return!0;const S=Number.isFinite(+y),T=h>=s.length;return y=!y&&x.isArray(f)?f.length:y,T?(x.hasOwnProp(f,y)?f[y]=[f[y],c]:f[y]=c,!S):((!f[y]||!x.isObject(f[y]))&&(f[y]=[]),a(s,c,f[y],h)&&x.isArray(f[y])&&(f[y]=_g(f[y])),!S)}if(x.isFormData(u)&&x.isFunction(u.entries)){const s={};return x.forEachEntry(u,(c,f)=>{a(Eg(c),f,s,0)}),s}return null}function Ag(u,a,s){if(x.isString(u))try{return(a||JSON.parse)(u),x.trim(u)}catch(c){if(c.name!=="SyntaxError")throw c}return(s||JSON.stringify)(u)}const Ja={transitional:py,adapter:["xhr","http","fetch"],transformRequest:[function(a,s){const c=s.getContentType()||"",f=c.indexOf("application/json")>-1,h=x.isObject(a);if(h&&x.isHTMLForm(a)&&(a=new FormData(a)),x.isFormData(a))return f?JSON.stringify(gy(a)):a;if(x.isArrayBuffer(a)||x.isBuffer(a)||x.isStream(a)||x.isFile(a)||x.isBlob(a)||x.isReadableStream(a))return a;if(x.isArrayBufferView(a))return a.buffer;if(x.isURLSearchParams(a))return s.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),a.toString();let S;if(h){if(c.indexOf("application/x-www-form-urlencoded")>-1)return Sg(a,this.formSerializer).toString();if((S=x.isFileList(a))||c.indexOf("multipart/form-data")>-1){const T=this.env&&this.env.FormData;return Nu(S?{"files[]":a}:a,T&&new T,this.formSerializer)}}return h||f?(s.setContentType("application/json",!1),Ag(a)):a}],transformResponse:[function(a){const s=this.transitional||Ja.transitional,c=s&&s.forcedJSONParsing,f=this.responseType==="json";if(x.isResponse(a)||x.isReadableStream(a))return a;if(a&&x.isString(a)&&(c&&!this.responseType||f)){const y=!(s&&s.silentJSONParsing)&&f;try{return JSON.parse(a)}catch(S){if(y)throw S.name==="SyntaxError"?tt.from(S,tt.ERR_BAD_RESPONSE,this,null,this.response):S}}return a}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Ft.classes.FormData,Blob:Ft.classes.Blob},validateStatus:function(a){return a>=200&&a<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};x.forEach(["delete","get","head","post","put","patch"],u=>{Ja.headers[u]={}});const Tg=x.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Rg=u=>{const a={};let s,c,f;return u&&u.split(`
`).forEach(function(y){f=y.indexOf(":"),s=y.substring(0,f).trim().toLowerCase(),c=y.substring(f+1).trim(),!(!s||a[s]&&Tg[s])&&(s==="set-cookie"?a[s]?a[s].push(c):a[s]=[c]:a[s]=a[s]?a[s]+", "+c:c)}),a},Hh=Symbol("internals");function Ga(u){return u&&String(u).trim().toLowerCase()}function yu(u){return u===!1||u==null?u:x.isArray(u)?u.map(yu):String(u)}function Og(u){const a=Object.create(null),s=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let c;for(;c=s.exec(u);)a[c[1]]=c[2];return a}const xg=u=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(u.trim());function tr(u,a,s,c,f){if(x.isFunction(c))return c.call(this,a,s);if(f&&(a=s),!!x.isString(a)){if(x.isString(c))return a.indexOf(c)!==-1;if(x.isRegExp(c))return c.test(a)}}function Ng(u){return u.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(a,s,c)=>s.toUpperCase()+c)}function wg(u,a){const s=x.toCamelCase(" "+a);["get","set","has"].forEach(c=>{Object.defineProperty(u,c+s,{value:function(f,h,y){return this[c].call(this,a,f,h,y)},configurable:!0})})}let ae=class{constructor(a){a&&this.set(a)}set(a,s,c){const f=this;function h(S,T,p){const b=Ga(T);if(!b)throw new Error("header name must be a non-empty string");const N=x.findKey(f,b);(!N||f[N]===void 0||p===!0||p===void 0&&f[N]!==!1)&&(f[N||T]=yu(S))}const y=(S,T)=>x.forEach(S,(p,b)=>h(p,b,T));if(x.isPlainObject(a)||a instanceof this.constructor)y(a,s);else if(x.isString(a)&&(a=a.trim())&&!xg(a))y(Rg(a),s);else if(x.isObject(a)&&x.isIterable(a)){let S={},T,p;for(const b of a){if(!x.isArray(b))throw TypeError("Object iterator must return a key-value pair");S[p=b[0]]=(T=S[p])?x.isArray(T)?[...T,b[1]]:[T,b[1]]:b[1]}y(S,s)}else a!=null&&h(s,a,c);return this}get(a,s){if(a=Ga(a),a){const c=x.findKey(this,a);if(c){const f=this[c];if(!s)return f;if(s===!0)return Og(f);if(x.isFunction(s))return s.call(this,f,c);if(x.isRegExp(s))return s.exec(f);throw new TypeError("parser must be boolean|regexp|function")}}}has(a,s){if(a=Ga(a),a){const c=x.findKey(this,a);return!!(c&&this[c]!==void 0&&(!s||tr(this,this[c],c,s)))}return!1}delete(a,s){const c=this;let f=!1;function h(y){if(y=Ga(y),y){const S=x.findKey(c,y);S&&(!s||tr(c,c[S],S,s))&&(delete c[S],f=!0)}}return x.isArray(a)?a.forEach(h):h(a),f}clear(a){const s=Object.keys(this);let c=s.length,f=!1;for(;c--;){const h=s[c];(!a||tr(this,this[h],h,a,!0))&&(delete this[h],f=!0)}return f}normalize(a){const s=this,c={};return x.forEach(this,(f,h)=>{const y=x.findKey(c,h);if(y){s[y]=yu(f),delete s[h];return}const S=a?Ng(h):String(h).trim();S!==h&&delete s[h],s[S]=yu(f),c[S]=!0}),this}concat(...a){return this.constructor.concat(this,...a)}toJSON(a){const s=Object.create(null);return x.forEach(this,(c,f)=>{c!=null&&c!==!1&&(s[f]=a&&x.isArray(c)?c.join(", "):c)}),s}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([a,s])=>a+": "+s).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(a){return a instanceof this?a:new this(a)}static concat(a,...s){const c=new this(a);return s.forEach(f=>c.set(f)),c}static accessor(a){const c=(this[Hh]=this[Hh]={accessors:{}}).accessors,f=this.prototype;function h(y){const S=Ga(y);c[S]||(wg(f,y),c[S]=!0)}return x.isArray(a)?a.forEach(h):h(a),this}};ae.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);x.reduceDescriptors(ae.prototype,({value:u},a)=>{let s=a[0].toUpperCase()+a.slice(1);return{get:()=>u,set(c){this[s]=c}}});x.freezeMethods(ae);function er(u,a){const s=this||Ja,c=a||s,f=ae.from(c.headers);let h=c.data;return x.forEach(u,function(S){h=S.call(s,h,f.normalize(),a?a.status:void 0)}),f.normalize(),h}function vy(u){return!!(u&&u.__CANCEL__)}function Xl(u,a,s){tt.call(this,u??"canceled",tt.ERR_CANCELED,a,s),this.name="CanceledError"}x.inherits(Xl,tt,{__CANCEL__:!0});function by(u,a,s){const c=s.config.validateStatus;!s.status||!c||c(s.status)?u(s):a(new tt("Request failed with status code "+s.status,[tt.ERR_BAD_REQUEST,tt.ERR_BAD_RESPONSE][Math.floor(s.status/100)-4],s.config,s.request,s))}function Dg(u){const a=/^([-+\w]{1,25})(:?\/\/|:)/.exec(u);return a&&a[1]||""}function Cg(u,a){u=u||10;const s=new Array(u),c=new Array(u);let f=0,h=0,y;return a=a!==void 0?a:1e3,function(T){const p=Date.now(),b=c[h];y||(y=p),s[f]=T,c[f]=p;let N=h,z=0;for(;N!==f;)z+=s[N++],N=N%u;if(f=(f+1)%u,f===h&&(h=(h+1)%u),p-y<a)return;const H=b&&p-b;return H?Math.round(z*1e3/H):void 0}}function Ug(u,a){let s=0,c=1e3/a,f,h;const y=(p,b=Date.now())=>{s=b,f=null,h&&(clearTimeout(h),h=null),u.apply(null,p)};return[(...p)=>{const b=Date.now(),N=b-s;N>=c?y(p,b):(f=p,h||(h=setTimeout(()=>{h=null,y(f)},c-N)))},()=>f&&y(f)]}const _u=(u,a,s=3)=>{let c=0;const f=Cg(50,250);return Ug(h=>{const y=h.loaded,S=h.lengthComputable?h.total:void 0,T=y-c,p=f(T),b=y<=S;c=y;const N={loaded:y,total:S,progress:S?y/S:void 0,bytes:T,rate:p||void 0,estimated:p&&S&&b?(S-y)/p:void 0,event:h,lengthComputable:S!=null,[a?"download":"upload"]:!0};u(N)},s)},Yh=(u,a)=>{const s=u!=null;return[c=>a[0]({lengthComputable:s,total:u,loaded:c}),a[1]]},Xh=u=>(...a)=>x.asap(()=>u(...a)),zg=Ft.hasStandardBrowserEnv?((u,a)=>s=>(s=new URL(s,Ft.origin),u.protocol===s.protocol&&u.host===s.host&&(a||u.port===s.port)))(new URL(Ft.origin),Ft.navigator&&/(msie|trident)/i.test(Ft.navigator.userAgent)):()=>!0,Mg=Ft.hasStandardBrowserEnv?{write(u,a,s,c,f,h){const y=[u+"="+encodeURIComponent(a)];x.isNumber(s)&&y.push("expires="+new Date(s).toGMTString()),x.isString(c)&&y.push("path="+c),x.isString(f)&&y.push("domain="+f),h===!0&&y.push("secure"),document.cookie=y.join("; ")},read(u){const a=document.cookie.match(new RegExp("(^|;\\s*)("+u+")=([^;]*)"));return a?decodeURIComponent(a[3]):null},remove(u){this.write(u,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Bg(u){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(u)}function jg(u,a){return a?u.replace(/\/?\/$/,"")+"/"+a.replace(/^\/+/,""):u}function Sy(u,a,s){let c=!Bg(a);return u&&(c||s==!1)?jg(u,a):a}const Gh=u=>u instanceof ae?{...u}:u;function $n(u,a){a=a||{};const s={};function c(p,b,N,z){return x.isPlainObject(p)&&x.isPlainObject(b)?x.merge.call({caseless:z},p,b):x.isPlainObject(b)?x.merge({},b):x.isArray(b)?b.slice():b}function f(p,b,N,z){if(x.isUndefined(b)){if(!x.isUndefined(p))return c(void 0,p,N,z)}else return c(p,b,N,z)}function h(p,b){if(!x.isUndefined(b))return c(void 0,b)}function y(p,b){if(x.isUndefined(b)){if(!x.isUndefined(p))return c(void 0,p)}else return c(void 0,b)}function S(p,b,N){if(N in a)return c(p,b);if(N in u)return c(void 0,p)}const T={url:h,method:h,data:h,baseURL:y,transformRequest:y,transformResponse:y,paramsSerializer:y,timeout:y,timeoutMessage:y,withCredentials:y,withXSRFToken:y,adapter:y,responseType:y,xsrfCookieName:y,xsrfHeaderName:y,onUploadProgress:y,onDownloadProgress:y,decompress:y,maxContentLength:y,maxBodyLength:y,beforeRedirect:y,transport:y,httpAgent:y,httpsAgent:y,cancelToken:y,socketPath:y,responseEncoding:y,validateStatus:S,headers:(p,b,N)=>f(Gh(p),Gh(b),N,!0)};return x.forEach(Object.keys(Object.assign({},u,a)),function(b){const N=T[b]||f,z=N(u[b],a[b],b);x.isUndefined(z)&&N!==S||(s[b]=z)}),s}const Ey=u=>{const a=$n({},u);let{data:s,withXSRFToken:c,xsrfHeaderName:f,xsrfCookieName:h,headers:y,auth:S}=a;a.headers=y=ae.from(y),a.url=my(Sy(a.baseURL,a.url,a.allowAbsoluteUrls),u.params,u.paramsSerializer),S&&y.set("Authorization","Basic "+btoa((S.username||"")+":"+(S.password?unescape(encodeURIComponent(S.password)):"")));let T;if(x.isFormData(s)){if(Ft.hasStandardBrowserEnv||Ft.hasStandardBrowserWebWorkerEnv)y.setContentType(void 0);else if((T=y.getContentType())!==!1){const[p,...b]=T?T.split(";").map(N=>N.trim()).filter(Boolean):[];y.setContentType([p||"multipart/form-data",...b].join("; "))}}if(Ft.hasStandardBrowserEnv&&(c&&x.isFunction(c)&&(c=c(a)),c||c!==!1&&zg(a.url))){const p=f&&h&&Mg.read(h);p&&y.set(f,p)}return a},qg=typeof XMLHttpRequest<"u",Lg=qg&&function(u){return new Promise(function(s,c){const f=Ey(u);let h=f.data;const y=ae.from(f.headers).normalize();let{responseType:S,onUploadProgress:T,onDownloadProgress:p}=f,b,N,z,H,B;function q(){H&&H(),B&&B(),f.cancelToken&&f.cancelToken.unsubscribe(b),f.signal&&f.signal.removeEventListener("abort",b)}let j=new XMLHttpRequest;j.open(f.method.toUpperCase(),f.url,!0),j.timeout=f.timeout;function Q(){if(!j)return;const nt=ae.from("getAllResponseHeaders"in j&&j.getAllResponseHeaders()),$={data:!S||S==="text"||S==="json"?j.responseText:j.response,status:j.status,statusText:j.statusText,headers:nt,config:u,request:j};by(function(xt){s(xt),q()},function(xt){c(xt),q()},$),j=null}"onloadend"in j?j.onloadend=Q:j.onreadystatechange=function(){!j||j.readyState!==4||j.status===0&&!(j.responseURL&&j.responseURL.indexOf("file:")===0)||setTimeout(Q)},j.onabort=function(){j&&(c(new tt("Request aborted",tt.ECONNABORTED,u,j)),j=null)},j.onerror=function(){c(new tt("Network Error",tt.ERR_NETWORK,u,j)),j=null},j.ontimeout=function(){let Ot=f.timeout?"timeout of "+f.timeout+"ms exceeded":"timeout exceeded";const $=f.transitional||py;f.timeoutErrorMessage&&(Ot=f.timeoutErrorMessage),c(new tt(Ot,$.clarifyTimeoutError?tt.ETIMEDOUT:tt.ECONNABORTED,u,j)),j=null},h===void 0&&y.setContentType(null),"setRequestHeader"in j&&x.forEach(y.toJSON(),function(Ot,$){j.setRequestHeader($,Ot)}),x.isUndefined(f.withCredentials)||(j.withCredentials=!!f.withCredentials),S&&S!=="json"&&(j.responseType=f.responseType),p&&([z,B]=_u(p,!0),j.addEventListener("progress",z)),T&&j.upload&&([N,H]=_u(T),j.upload.addEventListener("progress",N),j.upload.addEventListener("loadend",H)),(f.cancelToken||f.signal)&&(b=nt=>{j&&(c(!nt||nt.type?new Xl(null,u,j):nt),j.abort(),j=null)},f.cancelToken&&f.cancelToken.subscribe(b),f.signal&&(f.signal.aborted?b():f.signal.addEventListener("abort",b)));const I=Dg(f.url);if(I&&Ft.protocols.indexOf(I)===-1){c(new tt("Unsupported protocol "+I+":",tt.ERR_BAD_REQUEST,u));return}j.send(h||null)})},Hg=(u,a)=>{const{length:s}=u=u?u.filter(Boolean):[];if(a||s){let c=new AbortController,f;const h=function(p){if(!f){f=!0,S();const b=p instanceof Error?p:this.reason;c.abort(b instanceof tt?b:new Xl(b instanceof Error?b.message:b))}};let y=a&&setTimeout(()=>{y=null,h(new tt(`timeout ${a} of ms exceeded`,tt.ETIMEDOUT))},a);const S=()=>{u&&(y&&clearTimeout(y),y=null,u.forEach(p=>{p.unsubscribe?p.unsubscribe(h):p.removeEventListener("abort",h)}),u=null)};u.forEach(p=>p.addEventListener("abort",h));const{signal:T}=c;return T.unsubscribe=()=>x.asap(S),T}},Yg=function*(u,a){let s=u.byteLength;if(s<a){yield u;return}let c=0,f;for(;c<s;)f=c+a,yield u.slice(c,f),c=f},Xg=async function*(u,a){for await(const s of Gg(u))yield*Yg(s,a)},Gg=async function*(u){if(u[Symbol.asyncIterator]){yield*u;return}const a=u.getReader();try{for(;;){const{done:s,value:c}=await a.read();if(s)break;yield c}}finally{await a.cancel()}},Vh=(u,a,s,c)=>{const f=Xg(u,a);let h=0,y,S=T=>{y||(y=!0,c&&c(T))};return new ReadableStream({async pull(T){try{const{done:p,value:b}=await f.next();if(p){S(),T.close();return}let N=b.byteLength;if(s){let z=h+=N;s(z)}T.enqueue(new Uint8Array(b))}catch(p){throw S(p),p}},cancel(T){return S(T),f.return()}},{highWaterMark:2})},wu=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",_y=wu&&typeof ReadableStream=="function",Vg=wu&&(typeof TextEncoder=="function"?(u=>a=>u.encode(a))(new TextEncoder):async u=>new Uint8Array(await new Response(u).arrayBuffer())),Ay=(u,...a)=>{try{return!!u(...a)}catch{return!1}},kg=_y&&Ay(()=>{let u=!1;const a=new Request(Ft.origin,{body:new ReadableStream,method:"POST",get duplex(){return u=!0,"half"}}).headers.has("Content-Type");return u&&!a}),kh=64*1024,fr=_y&&Ay(()=>x.isReadableStream(new Response("").body)),Au={stream:fr&&(u=>u.body)};wu&&(u=>{["text","arrayBuffer","blob","formData","stream"].forEach(a=>{!Au[a]&&(Au[a]=x.isFunction(u[a])?s=>s[a]():(s,c)=>{throw new tt(`Response type '${a}' is not supported`,tt.ERR_NOT_SUPPORT,c)})})})(new Response);const Qg=async u=>{if(u==null)return 0;if(x.isBlob(u))return u.size;if(x.isSpecCompliantForm(u))return(await new Request(Ft.origin,{method:"POST",body:u}).arrayBuffer()).byteLength;if(x.isArrayBufferView(u)||x.isArrayBuffer(u))return u.byteLength;if(x.isURLSearchParams(u)&&(u=u+""),x.isString(u))return(await Vg(u)).byteLength},Zg=async(u,a)=>{const s=x.toFiniteNumber(u.getContentLength());return s??Qg(a)},Kg=wu&&(async u=>{let{url:a,method:s,data:c,signal:f,cancelToken:h,timeout:y,onDownloadProgress:S,onUploadProgress:T,responseType:p,headers:b,withCredentials:N="same-origin",fetchOptions:z}=Ey(u);p=p?(p+"").toLowerCase():"text";let H=Hg([f,h&&h.toAbortSignal()],y),B;const q=H&&H.unsubscribe&&(()=>{H.unsubscribe()});let j;try{if(T&&kg&&s!=="get"&&s!=="head"&&(j=await Zg(b,c))!==0){let $=new Request(a,{method:"POST",body:c,duplex:"half"}),At;if(x.isFormData(c)&&(At=$.headers.get("content-type"))&&b.setContentType(At),$.body){const[xt,jt]=Yh(j,_u(Xh(T)));c=Vh($.body,kh,xt,jt)}}x.isString(N)||(N=N?"include":"omit");const Q="credentials"in Request.prototype;B=new Request(a,{...z,signal:H,method:s.toUpperCase(),headers:b.normalize().toJSON(),body:c,duplex:"half",credentials:Q?N:void 0});let I=await fetch(B,z);const nt=fr&&(p==="stream"||p==="response");if(fr&&(S||nt&&q)){const $={};["status","statusText","headers"].forEach(ie=>{$[ie]=I[ie]});const At=x.toFiniteNumber(I.headers.get("content-length")),[xt,jt]=S&&Yh(At,_u(Xh(S),!0))||[];I=new Response(Vh(I.body,kh,xt,()=>{jt&&jt(),q&&q()}),$)}p=p||"text";let Ot=await Au[x.findKey(Au,p)||"text"](I,u);return!nt&&q&&q(),await new Promise(($,At)=>{by($,At,{data:Ot,headers:ae.from(I.headers),status:I.status,statusText:I.statusText,config:u,request:B})})}catch(Q){throw q&&q(),Q&&Q.name==="TypeError"&&/Load failed|fetch/i.test(Q.message)?Object.assign(new tt("Network Error",tt.ERR_NETWORK,u,B),{cause:Q.cause||Q}):tt.from(Q,Q&&Q.code,u,B)}}),dr={http:cg,xhr:Lg,fetch:Kg};x.forEach(dr,(u,a)=>{if(u){try{Object.defineProperty(u,"name",{value:a})}catch{}Object.defineProperty(u,"adapterName",{value:a})}});const Qh=u=>`- ${u}`,Jg=u=>x.isFunction(u)||u===null||u===!1,Ty={getAdapter:u=>{u=x.isArray(u)?u:[u];const{length:a}=u;let s,c;const f={};for(let h=0;h<a;h++){s=u[h];let y;if(c=s,!Jg(s)&&(c=dr[(y=String(s)).toLowerCase()],c===void 0))throw new tt(`Unknown adapter '${y}'`);if(c)break;f[y||"#"+h]=c}if(!c){const h=Object.entries(f).map(([S,T])=>`adapter ${S} `+(T===!1?"is not supported by the environment":"is not available in the build"));let y=a?h.length>1?`since :
`+h.map(Qh).join(`
`):" "+Qh(h[0]):"as no adapter specified";throw new tt("There is no suitable adapter to dispatch the request "+y,"ERR_NOT_SUPPORT")}return c},adapters:dr};function nr(u){if(u.cancelToken&&u.cancelToken.throwIfRequested(),u.signal&&u.signal.aborted)throw new Xl(null,u)}function Zh(u){return nr(u),u.headers=ae.from(u.headers),u.data=er.call(u,u.transformRequest),["post","put","patch"].indexOf(u.method)!==-1&&u.headers.setContentType("application/x-www-form-urlencoded",!1),Ty.getAdapter(u.adapter||Ja.adapter)(u).then(function(c){return nr(u),c.data=er.call(u,u.transformResponse,c),c.headers=ae.from(c.headers),c},function(c){return vy(c)||(nr(u),c&&c.response&&(c.response.data=er.call(u,u.transformResponse,c.response),c.response.headers=ae.from(c.response.headers))),Promise.reject(c)})}const Ry="1.10.0",Du={};["object","boolean","number","function","string","symbol"].forEach((u,a)=>{Du[u]=function(c){return typeof c===u||"a"+(a<1?"n ":" ")+u}});const Kh={};Du.transitional=function(a,s,c){function f(h,y){return"[Axios v"+Ry+"] Transitional option '"+h+"'"+y+(c?". "+c:"")}return(h,y,S)=>{if(a===!1)throw new tt(f(y," has been removed"+(s?" in "+s:"")),tt.ERR_DEPRECATED);return s&&!Kh[y]&&(Kh[y]=!0,console.warn(f(y," has been deprecated since v"+s+" and will be removed in the near future"))),a?a(h,y,S):!0}};Du.spelling=function(a){return(s,c)=>(console.warn(`${c} is likely a misspelling of ${a}`),!0)};function Wg(u,a,s){if(typeof u!="object")throw new tt("options must be an object",tt.ERR_BAD_OPTION_VALUE);const c=Object.keys(u);let f=c.length;for(;f-- >0;){const h=c[f],y=a[h];if(y){const S=u[h],T=S===void 0||y(S,h,u);if(T!==!0)throw new tt("option "+h+" must be "+T,tt.ERR_BAD_OPTION_VALUE);continue}if(s!==!0)throw new tt("Unknown option "+h,tt.ERR_BAD_OPTION)}}const mu={assertOptions:Wg,validators:Du},qe=mu.validators;let Fn=class{constructor(a){this.defaults=a||{},this.interceptors={request:new Lh,response:new Lh}}async request(a,s){try{return await this._request(a,s)}catch(c){if(c instanceof Error){let f={};Error.captureStackTrace?Error.captureStackTrace(f):f=new Error;const h=f.stack?f.stack.replace(/^.+\n/,""):"";try{c.stack?h&&!String(c.stack).endsWith(h.replace(/^.+\n.+\n/,""))&&(c.stack+=`
`+h):c.stack=h}catch{}}throw c}}_request(a,s){typeof a=="string"?(s=s||{},s.url=a):s=a||{},s=$n(this.defaults,s);const{transitional:c,paramsSerializer:f,headers:h}=s;c!==void 0&&mu.assertOptions(c,{silentJSONParsing:qe.transitional(qe.boolean),forcedJSONParsing:qe.transitional(qe.boolean),clarifyTimeoutError:qe.transitional(qe.boolean)},!1),f!=null&&(x.isFunction(f)?s.paramsSerializer={serialize:f}:mu.assertOptions(f,{encode:qe.function,serialize:qe.function},!0)),s.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?s.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:s.allowAbsoluteUrls=!0),mu.assertOptions(s,{baseUrl:qe.spelling("baseURL"),withXsrfToken:qe.spelling("withXSRFToken")},!0),s.method=(s.method||this.defaults.method||"get").toLowerCase();let y=h&&x.merge(h.common,h[s.method]);h&&x.forEach(["delete","get","head","post","put","patch","common"],B=>{delete h[B]}),s.headers=ae.concat(y,h);const S=[];let T=!0;this.interceptors.request.forEach(function(q){typeof q.runWhen=="function"&&q.runWhen(s)===!1||(T=T&&q.synchronous,S.unshift(q.fulfilled,q.rejected))});const p=[];this.interceptors.response.forEach(function(q){p.push(q.fulfilled,q.rejected)});let b,N=0,z;if(!T){const B=[Zh.bind(this),void 0];for(B.unshift.apply(B,S),B.push.apply(B,p),z=B.length,b=Promise.resolve(s);N<z;)b=b.then(B[N++],B[N++]);return b}z=S.length;let H=s;for(N=0;N<z;){const B=S[N++],q=S[N++];try{H=B(H)}catch(j){q.call(this,j);break}}try{b=Zh.call(this,H)}catch(B){return Promise.reject(B)}for(N=0,z=p.length;N<z;)b=b.then(p[N++],p[N++]);return b}getUri(a){a=$n(this.defaults,a);const s=Sy(a.baseURL,a.url,a.allowAbsoluteUrls);return my(s,a.params,a.paramsSerializer)}};x.forEach(["delete","get","head","options"],function(a){Fn.prototype[a]=function(s,c){return this.request($n(c||{},{method:a,url:s,data:(c||{}).data}))}});x.forEach(["post","put","patch"],function(a){function s(c){return function(h,y,S){return this.request($n(S||{},{method:a,headers:c?{"Content-Type":"multipart/form-data"}:{},url:h,data:y}))}}Fn.prototype[a]=s(),Fn.prototype[a+"Form"]=s(!0)});let Fg=class Oy{constructor(a){if(typeof a!="function")throw new TypeError("executor must be a function.");let s;this.promise=new Promise(function(h){s=h});const c=this;this.promise.then(f=>{if(!c._listeners)return;let h=c._listeners.length;for(;h-- >0;)c._listeners[h](f);c._listeners=null}),this.promise.then=f=>{let h;const y=new Promise(S=>{c.subscribe(S),h=S}).then(f);return y.cancel=function(){c.unsubscribe(h)},y},a(function(h,y,S){c.reason||(c.reason=new Xl(h,y,S),s(c.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(a){if(this.reason){a(this.reason);return}this._listeners?this._listeners.push(a):this._listeners=[a]}unsubscribe(a){if(!this._listeners)return;const s=this._listeners.indexOf(a);s!==-1&&this._listeners.splice(s,1)}toAbortSignal(){const a=new AbortController,s=c=>{a.abort(c)};return this.subscribe(s),a.signal.unsubscribe=()=>this.unsubscribe(s),a.signal}static source(){let a;return{token:new Oy(function(f){a=f}),cancel:a}}};function $g(u){return function(s){return u.apply(null,s)}}function Pg(u){return x.isObject(u)&&u.isAxiosError===!0}const hr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(hr).forEach(([u,a])=>{hr[a]=u});function xy(u){const a=new Fn(u),s=ly(Fn.prototype.request,a);return x.extend(s,Fn.prototype,a,{allOwnKeys:!0}),x.extend(s,a,null,{allOwnKeys:!0}),s.create=function(f){return xy($n(u,f))},s}const Dt=xy(Ja);Dt.Axios=Fn;Dt.CanceledError=Xl;Dt.CancelToken=Fg;Dt.isCancel=vy;Dt.VERSION=Ry;Dt.toFormData=Nu;Dt.AxiosError=tt;Dt.Cancel=Dt.CanceledError;Dt.all=function(a){return Promise.all(a)};Dt.spread=$g;Dt.isAxiosError=Pg;Dt.mergeConfig=$n;Dt.AxiosHeaders=ae;Dt.formToJSON=u=>gy(x.isHTMLForm(u)?new FormData(u):u);Dt.getAdapter=Ty.getAdapter;Dt.HttpStatusCode=hr;Dt.default=Dt;const{Axios:hb,AxiosError:yb,CanceledError:mb,isCancel:pb,CancelToken:gb,VERSION:vb,all:bb,Cancel:Sb,isAxiosError:Eb,spread:_b,toFormData:Ab,AxiosHeaders:Tb,HttpStatusCode:Rb,formToJSON:Ob,getAdapter:xb,mergeConfig:Nb}=Dt;class Ig{api;authToken;constructor(a){this.authToken=a.token,this.api=Dt.create({baseURL:a.baseURL||"http://localhost:5000/api/v1",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.authToken}`},timeout:3e4}),this.api.interceptors.response.use(s=>s,s=>Promise.reject(this.handleError(s)))}handleError(a){if(a.response){const s=a.response.data;return{error:s?.error||"API Error",code:s?.code||"UNKNOWN_ERROR",details:{status:a.response.status,statusText:a.response.statusText,...s?.details}}}return a.request?{error:"Network Error - Unable to reach server",code:"NETWORK_ERROR",details:{message:a.message}}:{error:a.message||"Unknown Error",code:"UNKNOWN_ERROR"}}async checkAuth(){try{return await this.getProjects(),!0}catch(a){if(a.details?.status===401)return!1;throw a}}updateAuthToken(a){this.authToken=a,this.api.defaults.headers.Authorization=`Bearer ${a}`}async getProjects(){return(await this.api.get("/projects")).data.projects}async getProject(a){return(await this.api.get(`/projects/${a}`)).data}async createProject(a){return(await this.api.post("/projects",a)).data}async updateProject(a,s){return(await this.api.put(`/projects/${a}`,s)).data}async deleteProject(a){await this.api.delete(`/projects/${a}`)}async getAgents(){return(await this.api.get("/agents")).data.agents}async getAgentStatus(a){return(await this.api.get(`/agents/${a}/status`)).data}async executeWorkflow(a){return(await this.api.post("/agents/execute",a)).data}async analyzeProject(a){return(await this.api.post("/agents/prompt-engineer/analyze",a)).data}async suggestStrategy(a){return(await this.api.post("/agents/prompt-engineer/suggest-strategy",a)).data}async scoreContext(a){return(await this.api.post("/agents/prompt-engineer/score-context",a)).data}async getPromptStrategy(a,s){const c=await this.analyzeProject({project_id:a,current_stage:"research",user_objectives:s}),f=await this.suggestStrategy({analysis_id:"temp_id",domain_context:c.analysis.current_objective,priority_goals:s});return{analysis:c,strategy:f}}}let yr=null;const tv=u=>{const a=new Ig(u);return yr=a,a},Le=()=>{if(!yr)throw new Error("API client not initialized. Call createClient() first.");return yr},He=Object.create(null);He.open="0";He.close="1";He.ping="2";He.pong="3";He.message="4";He.upgrade="5";He.noop="6";const pu=Object.create(null);Object.keys(He).forEach(u=>{pu[He[u]]=u});const mr={type:"error",data:"parser error"},Ny=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",wy=typeof ArrayBuffer=="function",Dy=u=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(u):u&&u.buffer instanceof ArrayBuffer,Rr=({type:u,data:a},s,c)=>Ny&&a instanceof Blob?s?c(a):Jh(a,c):wy&&(a instanceof ArrayBuffer||Dy(a))?s?c(a):Jh(new Blob([a]),c):c(He[u]+(a||"")),Jh=(u,a)=>{const s=new FileReader;return s.onload=function(){const c=s.result.split(",")[1];a("b"+(c||""))},s.readAsDataURL(u)};function Wh(u){return u instanceof Uint8Array?u:u instanceof ArrayBuffer?new Uint8Array(u):new Uint8Array(u.buffer,u.byteOffset,u.byteLength)}let lr;function ev(u,a){if(Ny&&u.data instanceof Blob)return u.data.arrayBuffer().then(Wh).then(a);if(wy&&(u.data instanceof ArrayBuffer||Dy(u.data)))return a(Wh(u.data));Rr(u,!1,s=>{lr||(lr=new TextEncoder),a(lr.encode(s))})}const Fh="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",ka=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let u=0;u<Fh.length;u++)ka[Fh.charCodeAt(u)]=u;const nv=u=>{let a=u.length*.75,s=u.length,c,f=0,h,y,S,T;u[u.length-1]==="="&&(a--,u[u.length-2]==="="&&a--);const p=new ArrayBuffer(a),b=new Uint8Array(p);for(c=0;c<s;c+=4)h=ka[u.charCodeAt(c)],y=ka[u.charCodeAt(c+1)],S=ka[u.charCodeAt(c+2)],T=ka[u.charCodeAt(c+3)],b[f++]=h<<2|y>>4,b[f++]=(y&15)<<4|S>>2,b[f++]=(S&3)<<6|T&63;return p},lv=typeof ArrayBuffer=="function",Or=(u,a)=>{if(typeof u!="string")return{type:"message",data:Cy(u,a)};const s=u.charAt(0);return s==="b"?{type:"message",data:av(u.substring(1),a)}:pu[s]?u.length>1?{type:pu[s],data:u.substring(1)}:{type:pu[s]}:mr},av=(u,a)=>{if(lv){const s=nv(u);return Cy(s,a)}else return{base64:!0,data:u}},Cy=(u,a)=>{switch(a){case"blob":return u instanceof Blob?u:new Blob([u]);case"arraybuffer":default:return u instanceof ArrayBuffer?u:u.buffer}},Uy="",iv=(u,a)=>{const s=u.length,c=new Array(s);let f=0;u.forEach((h,y)=>{Rr(h,!1,S=>{c[y]=S,++f===s&&a(c.join(Uy))})})},uv=(u,a)=>{const s=u.split(Uy),c=[];for(let f=0;f<s.length;f++){const h=Or(s[f],a);if(c.push(h),h.type==="error")break}return c};function sv(){return new TransformStream({transform(u,a){ev(u,s=>{const c=s.length;let f;if(c<126)f=new Uint8Array(1),new DataView(f.buffer).setUint8(0,c);else if(c<65536){f=new Uint8Array(3);const h=new DataView(f.buffer);h.setUint8(0,126),h.setUint16(1,c)}else{f=new Uint8Array(9);const h=new DataView(f.buffer);h.setUint8(0,127),h.setBigUint64(1,BigInt(c))}u.data&&typeof u.data!="string"&&(f[0]|=128),a.enqueue(f),a.enqueue(s)})}})}let ar;function ou(u){return u.reduce((a,s)=>a+s.length,0)}function fu(u,a){if(u[0].length===a)return u.shift();const s=new Uint8Array(a);let c=0;for(let f=0;f<a;f++)s[f]=u[0][c++],c===u[0].length&&(u.shift(),c=0);return u.length&&c<u[0].length&&(u[0]=u[0].slice(c)),s}function cv(u,a){ar||(ar=new TextDecoder);const s=[];let c=0,f=-1,h=!1;return new TransformStream({transform(y,S){for(s.push(y);;){if(c===0){if(ou(s)<1)break;const T=fu(s,1);h=(T[0]&128)===128,f=T[0]&127,f<126?c=3:f===126?c=1:c=2}else if(c===1){if(ou(s)<2)break;const T=fu(s,2);f=new DataView(T.buffer,T.byteOffset,T.length).getUint16(0),c=3}else if(c===2){if(ou(s)<8)break;const T=fu(s,8),p=new DataView(T.buffer,T.byteOffset,T.length),b=p.getUint32(0);if(b>Math.pow(2,21)-1){S.enqueue(mr);break}f=b*Math.pow(2,32)+p.getUint32(4),c=3}else{if(ou(s)<f)break;const T=fu(s,f);S.enqueue(Or(h?T:ar.decode(T),a)),c=0}if(f===0||f>u){S.enqueue(mr);break}}}})}const zy=4;function zt(u){if(u)return rv(u)}function rv(u){for(var a in zt.prototype)u[a]=zt.prototype[a];return u}zt.prototype.on=zt.prototype.addEventListener=function(u,a){return this._callbacks=this._callbacks||{},(this._callbacks["$"+u]=this._callbacks["$"+u]||[]).push(a),this};zt.prototype.once=function(u,a){function s(){this.off(u,s),a.apply(this,arguments)}return s.fn=a,this.on(u,s),this};zt.prototype.off=zt.prototype.removeListener=zt.prototype.removeAllListeners=zt.prototype.removeEventListener=function(u,a){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var s=this._callbacks["$"+u];if(!s)return this;if(arguments.length==1)return delete this._callbacks["$"+u],this;for(var c,f=0;f<s.length;f++)if(c=s[f],c===a||c.fn===a){s.splice(f,1);break}return s.length===0&&delete this._callbacks["$"+u],this};zt.prototype.emit=function(u){this._callbacks=this._callbacks||{};for(var a=new Array(arguments.length-1),s=this._callbacks["$"+u],c=1;c<arguments.length;c++)a[c-1]=arguments[c];if(s){s=s.slice(0);for(var c=0,f=s.length;c<f;++c)s[c].apply(this,a)}return this};zt.prototype.emitReserved=zt.prototype.emit;zt.prototype.listeners=function(u){return this._callbacks=this._callbacks||{},this._callbacks["$"+u]||[]};zt.prototype.hasListeners=function(u){return!!this.listeners(u).length};const Cu=typeof Promise=="function"&&typeof Promise.resolve=="function"?a=>Promise.resolve().then(a):(a,s)=>s(a,0),Re=typeof self<"u"?self:typeof window<"u"?window:Function("return this")(),ov="arraybuffer";function My(u,...a){return a.reduce((s,c)=>(u.hasOwnProperty(c)&&(s[c]=u[c]),s),{})}const fv=Re.setTimeout,dv=Re.clearTimeout;function Uu(u,a){a.useNativeTimers?(u.setTimeoutFn=fv.bind(Re),u.clearTimeoutFn=dv.bind(Re)):(u.setTimeoutFn=Re.setTimeout.bind(Re),u.clearTimeoutFn=Re.clearTimeout.bind(Re))}const hv=1.33;function yv(u){return typeof u=="string"?mv(u):Math.ceil((u.byteLength||u.size)*hv)}function mv(u){let a=0,s=0;for(let c=0,f=u.length;c<f;c++)a=u.charCodeAt(c),a<128?s+=1:a<2048?s+=2:a<55296||a>=57344?s+=3:(c++,s+=4);return s}function By(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}function pv(u){let a="";for(let s in u)u.hasOwnProperty(s)&&(a.length&&(a+="&"),a+=encodeURIComponent(s)+"="+encodeURIComponent(u[s]));return a}function gv(u){let a={},s=u.split("&");for(let c=0,f=s.length;c<f;c++){let h=s[c].split("=");a[decodeURIComponent(h[0])]=decodeURIComponent(h[1])}return a}class vv extends Error{constructor(a,s,c){super(a),this.description=s,this.context=c,this.type="TransportError"}}class xr extends zt{constructor(a){super(),this.writable=!1,Uu(this,a),this.opts=a,this.query=a.query,this.socket=a.socket,this.supportsBinary=!a.forceBase64}onError(a,s,c){return super.emitReserved("error",new vv(a,s,c)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(a){this.readyState==="open"&&this.write(a)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(a){const s=Or(a,this.socket.binaryType);this.onPacket(s)}onPacket(a){super.emitReserved("packet",a)}onClose(a){this.readyState="closed",super.emitReserved("close",a)}pause(a){}createUri(a,s={}){return a+"://"+this._hostname()+this._port()+this.opts.path+this._query(s)}_hostname(){const a=this.opts.hostname;return a.indexOf(":")===-1?a:"["+a+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(a){const s=pv(a);return s.length?"?"+s:""}}class bv extends xr{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(a){this.readyState="pausing";const s=()=>{this.readyState="paused",a()};if(this._polling||!this.writable){let c=0;this._polling&&(c++,this.once("pollComplete",function(){--c||s()})),this.writable||(c++,this.once("drain",function(){--c||s()}))}else s()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(a){const s=c=>{if(this.readyState==="opening"&&c.type==="open"&&this.onOpen(),c.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(c)};uv(a,this.socket.binaryType).forEach(s),this.readyState!=="closed"&&(this._polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this._poll())}doClose(){const a=()=>{this.write([{type:"close"}])};this.readyState==="open"?a():this.once("open",a)}write(a){this.writable=!1,iv(a,s=>{this.doWrite(s,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const a=this.opts.secure?"https":"http",s=this.query||{};return this.opts.timestampRequests!==!1&&(s[this.opts.timestampParam]=By()),!this.supportsBinary&&!s.sid&&(s.b64=1),this.createUri(a,s)}}let jy=!1;try{jy=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}const Sv=jy;function Ev(){}class _v extends bv{constructor(a){if(super(a),typeof location<"u"){const s=location.protocol==="https:";let c=location.port;c||(c=s?"443":"80"),this.xd=typeof location<"u"&&a.hostname!==location.hostname||c!==a.port}}doWrite(a,s){const c=this.request({method:"POST",data:a});c.on("success",s),c.on("error",(f,h)=>{this.onError("xhr post error",f,h)})}doPoll(){const a=this.request();a.on("data",this.onData.bind(this)),a.on("error",(s,c)=>{this.onError("xhr poll error",s,c)}),this.pollXhr=a}}let Ll=class gu extends zt{constructor(a,s,c){super(),this.createRequest=a,Uu(this,c),this._opts=c,this._method=c.method||"GET",this._uri=s,this._data=c.data!==void 0?c.data:null,this._create()}_create(){var a;const s=My(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");s.xdomain=!!this._opts.xd;const c=this._xhr=this.createRequest(s);try{c.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){c.setDisableHeaderCheck&&c.setDisableHeaderCheck(!0);for(let f in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(f)&&c.setRequestHeader(f,this._opts.extraHeaders[f])}}catch{}if(this._method==="POST")try{c.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{c.setRequestHeader("Accept","*/*")}catch{}(a=this._opts.cookieJar)===null||a===void 0||a.addCookies(c),"withCredentials"in c&&(c.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(c.timeout=this._opts.requestTimeout),c.onreadystatechange=()=>{var f;c.readyState===3&&((f=this._opts.cookieJar)===null||f===void 0||f.parseCookies(c.getResponseHeader("set-cookie"))),c.readyState===4&&(c.status===200||c.status===1223?this._onLoad():this.setTimeoutFn(()=>{this._onError(typeof c.status=="number"?c.status:0)},0))},c.send(this._data)}catch(f){this.setTimeoutFn(()=>{this._onError(f)},0);return}typeof document<"u"&&(this._index=gu.requestsCount++,gu.requests[this._index]=this)}_onError(a){this.emitReserved("error",a,this._xhr),this._cleanup(!0)}_cleanup(a){if(!(typeof this._xhr>"u"||this._xhr===null)){if(this._xhr.onreadystatechange=Ev,a)try{this._xhr.abort()}catch{}typeof document<"u"&&delete gu.requests[this._index],this._xhr=null}}_onLoad(){const a=this._xhr.responseText;a!==null&&(this.emitReserved("data",a),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}};Ll.requestsCount=0;Ll.requests={};if(typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",$h);else if(typeof addEventListener=="function"){const u="onpagehide"in Re?"pagehide":"unload";addEventListener(u,$h,!1)}}function $h(){for(let u in Ll.requests)Ll.requests.hasOwnProperty(u)&&Ll.requests[u].abort()}const Av=function(){const u=qy({xdomain:!1});return u&&u.responseType!==null}();class Tv extends _v{constructor(a){super(a);const s=a&&a.forceBase64;this.supportsBinary=Av&&!s}request(a={}){return Object.assign(a,{xd:this.xd},this.opts),new Ll(qy,this.uri(),a)}}function qy(u){const a=u.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!a||Sv))return new XMLHttpRequest}catch{}if(!a)try{return new Re[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch{}}const Ly=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class Rv extends xr{get name(){return"websocket"}doOpen(){const a=this.uri(),s=this.opts.protocols,c=Ly?{}:My(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(c.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(a,s,c)}catch(f){return this.emitReserved("error",f)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=a=>this.onClose({description:"websocket connection closed",context:a}),this.ws.onmessage=a=>this.onData(a.data),this.ws.onerror=a=>this.onError("websocket error",a)}write(a){this.writable=!1;for(let s=0;s<a.length;s++){const c=a[s],f=s===a.length-1;Rr(c,this.supportsBinary,h=>{try{this.doWrite(c,h)}catch{}f&&Cu(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const a=this.opts.secure?"wss":"ws",s=this.query||{};return this.opts.timestampRequests&&(s[this.opts.timestampParam]=By()),this.supportsBinary||(s.b64=1),this.createUri(a,s)}}const ir=Re.WebSocket||Re.MozWebSocket;class Ov extends Rv{createSocket(a,s,c){return Ly?new ir(a,s,c):s?new ir(a,s):new ir(a)}doWrite(a,s){this.ws.send(s)}}class xv extends xr{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(a){return this.emitReserved("error",a)}this._transport.closed.then(()=>{this.onClose()}).catch(a=>{this.onError("webtransport error",a)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(a=>{const s=cv(Number.MAX_SAFE_INTEGER,this.socket.binaryType),c=a.readable.pipeThrough(s).getReader(),f=sv();f.readable.pipeTo(a.writable),this._writer=f.writable.getWriter();const h=()=>{c.read().then(({done:S,value:T})=>{S||(this.onPacket(T),h())}).catch(S=>{})};h();const y={type:"open"};this.query.sid&&(y.data=`{"sid":"${this.query.sid}"}`),this._writer.write(y).then(()=>this.onOpen())})})}write(a){this.writable=!1;for(let s=0;s<a.length;s++){const c=a[s],f=s===a.length-1;this._writer.write(c).then(()=>{f&&Cu(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var a;(a=this._transport)===null||a===void 0||a.close()}}const Nv={websocket:Ov,webtransport:xv,polling:Tv},wv=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,Dv=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function pr(u){if(u.length>8e3)throw"URI too long";const a=u,s=u.indexOf("["),c=u.indexOf("]");s!=-1&&c!=-1&&(u=u.substring(0,s)+u.substring(s,c).replace(/:/g,";")+u.substring(c,u.length));let f=wv.exec(u||""),h={},y=14;for(;y--;)h[Dv[y]]=f[y]||"";return s!=-1&&c!=-1&&(h.source=a,h.host=h.host.substring(1,h.host.length-1).replace(/;/g,":"),h.authority=h.authority.replace("[","").replace("]","").replace(/;/g,":"),h.ipv6uri=!0),h.pathNames=Cv(h,h.path),h.queryKey=Uv(h,h.query),h}function Cv(u,a){const s=/\/{2,9}/g,c=a.replace(s,"/").split("/");return(a.slice(0,1)=="/"||a.length===0)&&c.splice(0,1),a.slice(-1)=="/"&&c.splice(c.length-1,1),c}function Uv(u,a){const s={};return a.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(c,f,h){f&&(s[f]=h)}),s}const gr=typeof addEventListener=="function"&&typeof removeEventListener=="function",vu=[];gr&&addEventListener("offline",()=>{vu.forEach(u=>u())},!1);class Nn extends zt{constructor(a,s){if(super(),this.binaryType=ov,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,a&&typeof a=="object"&&(s=a,a=null),a){const c=pr(a);s.hostname=c.host,s.secure=c.protocol==="https"||c.protocol==="wss",s.port=c.port,c.query&&(s.query=c.query)}else s.host&&(s.hostname=pr(s.host).host);Uu(this,s),this.secure=s.secure!=null?s.secure:typeof location<"u"&&location.protocol==="https:",s.hostname&&!s.port&&(s.port=this.secure?"443":"80"),this.hostname=s.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=s.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},s.transports.forEach(c=>{const f=c.prototype.name;this.transports.push(f),this._transportsByName[f]=c}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},s),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=gv(this.opts.query)),gr&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},vu.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(a){const s=Object.assign({},this.opts.query);s.EIO=zy,s.transport=a,this.id&&(s.sid=this.id);const c=Object.assign({},this.opts,{query:s,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[a]);return new this._transportsByName[a](c)}_open(){if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}const a=this.opts.rememberUpgrade&&Nn.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1?"websocket":this.transports[0];this.readyState="opening";const s=this.createTransport(a);s.open(),this.setTransport(s)}setTransport(a){this.transport&&this.transport.removeAllListeners(),this.transport=a,a.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",s=>this._onClose("transport close",s))}onOpen(){this.readyState="open",Nn.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush()}_onPacket(a){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",a),this.emitReserved("heartbeat"),a.type){case"open":this.onHandshake(JSON.parse(a.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const s=new Error("server error");s.code=a.data,this._onError(s);break;case"message":this.emitReserved("data",a.data),this.emitReserved("message",a.data);break}}onHandshake(a){this.emitReserved("handshake",a),this.id=a.sid,this.transport.query.sid=a.sid,this._pingInterval=a.pingInterval,this._pingTimeout=a.pingTimeout,this._maxPayload=a.maxPayload,this.onOpen(),this.readyState!=="closed"&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const a=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+a,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},a),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const a=this._getWritablePackets();this.transport.send(a),this._prevBufferLen=a.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let s=1;for(let c=0;c<this.writeBuffer.length;c++){const f=this.writeBuffer[c].data;if(f&&(s+=yv(f)),c>0&&s>this._maxPayload)return this.writeBuffer.slice(0,c);s+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const a=Date.now()>this._pingTimeoutTime;return a&&(this._pingTimeoutTime=0,Cu(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),a}write(a,s,c){return this._sendPacket("message",a,s,c),this}send(a,s,c){return this._sendPacket("message",a,s,c),this}_sendPacket(a,s,c,f){if(typeof s=="function"&&(f=s,s=void 0),typeof c=="function"&&(f=c,c=null),this.readyState==="closing"||this.readyState==="closed")return;c=c||{},c.compress=c.compress!==!1;const h={type:a,data:s,options:c};this.emitReserved("packetCreate",h),this.writeBuffer.push(h),f&&this.once("flush",f),this.flush()}close(){const a=()=>{this._onClose("forced close"),this.transport.close()},s=()=>{this.off("upgrade",s),this.off("upgradeError",s),a()},c=()=>{this.once("upgrade",s),this.once("upgradeError",s)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?c():a()}):this.upgrading?c():a()),this}_onError(a){if(Nn.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==="opening")return this.transports.shift(),this._open();this.emitReserved("error",a),this._onClose("transport error",a)}_onClose(a,s){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing"){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),gr&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const c=vu.indexOf(this._offlineEventListener);c!==-1&&vu.splice(c,1)}this.readyState="closed",this.id=null,this.emitReserved("close",a,s),this.writeBuffer=[],this._prevBufferLen=0}}}Nn.protocol=zy;class zv extends Nn{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),this.readyState==="open"&&this.opts.upgrade)for(let a=0;a<this._upgrades.length;a++)this._probe(this._upgrades[a])}_probe(a){let s=this.createTransport(a),c=!1;Nn.priorWebsocketSuccess=!1;const f=()=>{c||(s.send([{type:"ping",data:"probe"}]),s.once("packet",N=>{if(!c)if(N.type==="pong"&&N.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",s),!s)return;Nn.priorWebsocketSuccess=s.name==="websocket",this.transport.pause(()=>{c||this.readyState!=="closed"&&(b(),this.setTransport(s),s.send([{type:"upgrade"}]),this.emitReserved("upgrade",s),s=null,this.upgrading=!1,this.flush())})}else{const z=new Error("probe error");z.transport=s.name,this.emitReserved("upgradeError",z)}}))};function h(){c||(c=!0,b(),s.close(),s=null)}const y=N=>{const z=new Error("probe error: "+N);z.transport=s.name,h(),this.emitReserved("upgradeError",z)};function S(){y("transport closed")}function T(){y("socket closed")}function p(N){s&&N.name!==s.name&&h()}const b=()=>{s.removeListener("open",f),s.removeListener("error",y),s.removeListener("close",S),this.off("close",T),this.off("upgrading",p)};s.once("open",f),s.once("error",y),s.once("close",S),this.once("close",T),this.once("upgrading",p),this._upgrades.indexOf("webtransport")!==-1&&a!=="webtransport"?this.setTimeoutFn(()=>{c||s.open()},200):s.open()}onHandshake(a){this._upgrades=this._filterUpgrades(a.upgrades),super.onHandshake(a)}_filterUpgrades(a){const s=[];for(let c=0;c<a.length;c++)~this.transports.indexOf(a[c])&&s.push(a[c]);return s}}let Mv=class extends zv{constructor(a,s={}){const c=typeof a=="object"?a:s;(!c.transports||c.transports&&typeof c.transports[0]=="string")&&(c.transports=(c.transports||["polling","websocket","webtransport"]).map(f=>Nv[f]).filter(f=>!!f)),super(a,c)}};function Bv(u,a="",s){let c=u;s=s||typeof location<"u"&&location,u==null&&(u=s.protocol+"//"+s.host),typeof u=="string"&&(u.charAt(0)==="/"&&(u.charAt(1)==="/"?u=s.protocol+u:u=s.host+u),/^(https?|wss?):\/\//.test(u)||(typeof s<"u"?u=s.protocol+"//"+u:u="https://"+u),c=pr(u)),c.port||(/^(http|ws)$/.test(c.protocol)?c.port="80":/^(http|ws)s$/.test(c.protocol)&&(c.port="443")),c.path=c.path||"/";const h=c.host.indexOf(":")!==-1?"["+c.host+"]":c.host;return c.id=c.protocol+"://"+h+":"+c.port+a,c.href=c.protocol+"://"+h+(s&&s.port===c.port?"":":"+c.port),c}const jv=typeof ArrayBuffer=="function",qv=u=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(u):u.buffer instanceof ArrayBuffer,Hy=Object.prototype.toString,Lv=typeof Blob=="function"||typeof Blob<"u"&&Hy.call(Blob)==="[object BlobConstructor]",Hv=typeof File=="function"||typeof File<"u"&&Hy.call(File)==="[object FileConstructor]";function Nr(u){return jv&&(u instanceof ArrayBuffer||qv(u))||Lv&&u instanceof Blob||Hv&&u instanceof File}function bu(u,a){if(!u||typeof u!="object")return!1;if(Array.isArray(u)){for(let s=0,c=u.length;s<c;s++)if(bu(u[s]))return!0;return!1}if(Nr(u))return!0;if(u.toJSON&&typeof u.toJSON=="function"&&arguments.length===1)return bu(u.toJSON(),!0);for(const s in u)if(Object.prototype.hasOwnProperty.call(u,s)&&bu(u[s]))return!0;return!1}function Yv(u){const a=[],s=u.data,c=u;return c.data=vr(s,a),c.attachments=a.length,{packet:c,buffers:a}}function vr(u,a){if(!u)return u;if(Nr(u)){const s={_placeholder:!0,num:a.length};return a.push(u),s}else if(Array.isArray(u)){const s=new Array(u.length);for(let c=0;c<u.length;c++)s[c]=vr(u[c],a);return s}else if(typeof u=="object"&&!(u instanceof Date)){const s={};for(const c in u)Object.prototype.hasOwnProperty.call(u,c)&&(s[c]=vr(u[c],a));return s}return u}function Xv(u,a){return u.data=br(u.data,a),delete u.attachments,u}function br(u,a){if(!u)return u;if(u&&u._placeholder===!0){if(typeof u.num=="number"&&u.num>=0&&u.num<a.length)return a[u.num];throw new Error("illegal attachments")}else if(Array.isArray(u))for(let s=0;s<u.length;s++)u[s]=br(u[s],a);else if(typeof u=="object")for(const s in u)Object.prototype.hasOwnProperty.call(u,s)&&(u[s]=br(u[s],a));return u}const Gv=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],Vv=5;var st;(function(u){u[u.CONNECT=0]="CONNECT",u[u.DISCONNECT=1]="DISCONNECT",u[u.EVENT=2]="EVENT",u[u.ACK=3]="ACK",u[u.CONNECT_ERROR=4]="CONNECT_ERROR",u[u.BINARY_EVENT=5]="BINARY_EVENT",u[u.BINARY_ACK=6]="BINARY_ACK"})(st||(st={}));class kv{constructor(a){this.replacer=a}encode(a){return(a.type===st.EVENT||a.type===st.ACK)&&bu(a)?this.encodeAsBinary({type:a.type===st.EVENT?st.BINARY_EVENT:st.BINARY_ACK,nsp:a.nsp,data:a.data,id:a.id}):[this.encodeAsString(a)]}encodeAsString(a){let s=""+a.type;return(a.type===st.BINARY_EVENT||a.type===st.BINARY_ACK)&&(s+=a.attachments+"-"),a.nsp&&a.nsp!=="/"&&(s+=a.nsp+","),a.id!=null&&(s+=a.id),a.data!=null&&(s+=JSON.stringify(a.data,this.replacer)),s}encodeAsBinary(a){const s=Yv(a),c=this.encodeAsString(s.packet),f=s.buffers;return f.unshift(c),f}}function Ph(u){return Object.prototype.toString.call(u)==="[object Object]"}class wr extends zt{constructor(a){super(),this.reviver=a}add(a){let s;if(typeof a=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");s=this.decodeString(a);const c=s.type===st.BINARY_EVENT;c||s.type===st.BINARY_ACK?(s.type=c?st.EVENT:st.ACK,this.reconstructor=new Qv(s),s.attachments===0&&super.emitReserved("decoded",s)):super.emitReserved("decoded",s)}else if(Nr(a)||a.base64)if(this.reconstructor)s=this.reconstructor.takeBinaryData(a),s&&(this.reconstructor=null,super.emitReserved("decoded",s));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+a)}decodeString(a){let s=0;const c={type:Number(a.charAt(0))};if(st[c.type]===void 0)throw new Error("unknown packet type "+c.type);if(c.type===st.BINARY_EVENT||c.type===st.BINARY_ACK){const h=s+1;for(;a.charAt(++s)!=="-"&&s!=a.length;);const y=a.substring(h,s);if(y!=Number(y)||a.charAt(s)!=="-")throw new Error("Illegal attachments");c.attachments=Number(y)}if(a.charAt(s+1)==="/"){const h=s+1;for(;++s&&!(a.charAt(s)===","||s===a.length););c.nsp=a.substring(h,s)}else c.nsp="/";const f=a.charAt(s+1);if(f!==""&&Number(f)==f){const h=s+1;for(;++s;){const y=a.charAt(s);if(y==null||Number(y)!=y){--s;break}if(s===a.length)break}c.id=Number(a.substring(h,s+1))}if(a.charAt(++s)){const h=this.tryParse(a.substr(s));if(wr.isPayloadValid(c.type,h))c.data=h;else throw new Error("invalid payload")}return c}tryParse(a){try{return JSON.parse(a,this.reviver)}catch{return!1}}static isPayloadValid(a,s){switch(a){case st.CONNECT:return Ph(s);case st.DISCONNECT:return s===void 0;case st.CONNECT_ERROR:return typeof s=="string"||Ph(s);case st.EVENT:case st.BINARY_EVENT:return Array.isArray(s)&&(typeof s[0]=="number"||typeof s[0]=="string"&&Gv.indexOf(s[0])===-1);case st.ACK:case st.BINARY_ACK:return Array.isArray(s)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class Qv{constructor(a){this.packet=a,this.buffers=[],this.reconPack=a}takeBinaryData(a){if(this.buffers.push(a),this.buffers.length===this.reconPack.attachments){const s=Xv(this.reconPack,this.buffers);return this.finishedReconstruction(),s}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const Zv=Object.freeze(Object.defineProperty({__proto__:null,Decoder:wr,Encoder:kv,get PacketType(){return st},protocol:Vv},Symbol.toStringTag,{value:"Module"}));function we(u,a,s){return u.on(a,s),function(){u.off(a,s)}}const Kv=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class Yy extends zt{constructor(a,s,c){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=a,this.nsp=s,c&&c.auth&&(this.auth=c.auth),this._opts=Object.assign({},c),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const a=this.io;this.subs=[we(a,"open",this.onopen.bind(this)),we(a,"packet",this.onpacket.bind(this)),we(a,"error",this.onerror.bind(this)),we(a,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...a){return a.unshift("message"),this.emit.apply(this,a),this}emit(a,...s){var c,f,h;if(Kv.hasOwnProperty(a))throw new Error('"'+a.toString()+'" is a reserved event name');if(s.unshift(a),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(s),this;const y={type:st.EVENT,data:s};if(y.options={},y.options.compress=this.flags.compress!==!1,typeof s[s.length-1]=="function"){const b=this.ids++,N=s.pop();this._registerAckCallback(b,N),y.id=b}const S=(f=(c=this.io.engine)===null||c===void 0?void 0:c.transport)===null||f===void 0?void 0:f.writable,T=this.connected&&!(!((h=this.io.engine)===null||h===void 0)&&h._hasPingExpired());return this.flags.volatile&&!S||(T?(this.notifyOutgoingListeners(y),this.packet(y)):this.sendBuffer.push(y)),this.flags={},this}_registerAckCallback(a,s){var c;const f=(c=this.flags.timeout)!==null&&c!==void 0?c:this._opts.ackTimeout;if(f===void 0){this.acks[a]=s;return}const h=this.io.setTimeoutFn(()=>{delete this.acks[a];for(let S=0;S<this.sendBuffer.length;S++)this.sendBuffer[S].id===a&&this.sendBuffer.splice(S,1);s.call(this,new Error("operation has timed out"))},f),y=(...S)=>{this.io.clearTimeoutFn(h),s.apply(this,S)};y.withError=!0,this.acks[a]=y}emitWithAck(a,...s){return new Promise((c,f)=>{const h=(y,S)=>y?f(y):c(S);h.withError=!0,s.push(h),this.emit(a,...s)})}_addToQueue(a){let s;typeof a[a.length-1]=="function"&&(s=a.pop());const c={id:this._queueSeq++,tryCount:0,pending:!1,args:a,flags:Object.assign({fromQueue:!0},this.flags)};a.push((f,...h)=>c!==this._queue[0]?void 0:(f!==null?c.tryCount>this._opts.retries&&(this._queue.shift(),s&&s(f)):(this._queue.shift(),s&&s(null,...h)),c.pending=!1,this._drainQueue())),this._queue.push(c),this._drainQueue()}_drainQueue(a=!1){if(!this.connected||this._queue.length===0)return;const s=this._queue[0];s.pending&&!a||(s.pending=!0,s.tryCount++,this.flags=s.flags,this.emit.apply(this,s.args))}packet(a){a.nsp=this.nsp,this.io._packet(a)}onopen(){typeof this.auth=="function"?this.auth(a=>{this._sendConnectPacket(a)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(a){this.packet({type:st.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},a):a})}onerror(a){this.connected||this.emitReserved("connect_error",a)}onclose(a,s){this.connected=!1,delete this.id,this.emitReserved("disconnect",a,s),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(a=>{if(!this.sendBuffer.some(c=>String(c.id)===a)){const c=this.acks[a];delete this.acks[a],c.withError&&c.call(this,new Error("socket has been disconnected"))}})}onpacket(a){if(a.nsp===this.nsp)switch(a.type){case st.CONNECT:a.data&&a.data.sid?this.onconnect(a.data.sid,a.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case st.EVENT:case st.BINARY_EVENT:this.onevent(a);break;case st.ACK:case st.BINARY_ACK:this.onack(a);break;case st.DISCONNECT:this.ondisconnect();break;case st.CONNECT_ERROR:this.destroy();const c=new Error(a.data.message);c.data=a.data.data,this.emitReserved("connect_error",c);break}}onevent(a){const s=a.data||[];a.id!=null&&s.push(this.ack(a.id)),this.connected?this.emitEvent(s):this.receiveBuffer.push(Object.freeze(s))}emitEvent(a){if(this._anyListeners&&this._anyListeners.length){const s=this._anyListeners.slice();for(const c of s)c.apply(this,a)}super.emit.apply(this,a),this._pid&&a.length&&typeof a[a.length-1]=="string"&&(this._lastOffset=a[a.length-1])}ack(a){const s=this;let c=!1;return function(...f){c||(c=!0,s.packet({type:st.ACK,id:a,data:f}))}}onack(a){const s=this.acks[a.id];typeof s=="function"&&(delete this.acks[a.id],s.withError&&a.data.unshift(null),s.apply(this,a.data))}onconnect(a,s){this.id=a,this.recovered=s&&this._pid===s,this._pid=s,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(a=>this.emitEvent(a)),this.receiveBuffer=[],this.sendBuffer.forEach(a=>{this.notifyOutgoingListeners(a),this.packet(a)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(a=>a()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:st.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(a){return this.flags.compress=a,this}get volatile(){return this.flags.volatile=!0,this}timeout(a){return this.flags.timeout=a,this}onAny(a){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(a),this}prependAny(a){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(a),this}offAny(a){if(!this._anyListeners)return this;if(a){const s=this._anyListeners;for(let c=0;c<s.length;c++)if(a===s[c])return s.splice(c,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(a){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(a),this}prependAnyOutgoing(a){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(a),this}offAnyOutgoing(a){if(!this._anyOutgoingListeners)return this;if(a){const s=this._anyOutgoingListeners;for(let c=0;c<s.length;c++)if(a===s[c])return s.splice(c,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(a){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const s=this._anyOutgoingListeners.slice();for(const c of s)c.apply(this,a.data)}}}function Gl(u){u=u||{},this.ms=u.min||100,this.max=u.max||1e4,this.factor=u.factor||2,this.jitter=u.jitter>0&&u.jitter<=1?u.jitter:0,this.attempts=0}Gl.prototype.duration=function(){var u=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var a=Math.random(),s=Math.floor(a*this.jitter*u);u=(Math.floor(a*10)&1)==0?u-s:u+s}return Math.min(u,this.max)|0};Gl.prototype.reset=function(){this.attempts=0};Gl.prototype.setMin=function(u){this.ms=u};Gl.prototype.setMax=function(u){this.max=u};Gl.prototype.setJitter=function(u){this.jitter=u};class Sr extends zt{constructor(a,s){var c;super(),this.nsps={},this.subs=[],a&&typeof a=="object"&&(s=a,a=void 0),s=s||{},s.path=s.path||"/socket.io",this.opts=s,Uu(this,s),this.reconnection(s.reconnection!==!1),this.reconnectionAttempts(s.reconnectionAttempts||1/0),this.reconnectionDelay(s.reconnectionDelay||1e3),this.reconnectionDelayMax(s.reconnectionDelayMax||5e3),this.randomizationFactor((c=s.randomizationFactor)!==null&&c!==void 0?c:.5),this.backoff=new Gl({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(s.timeout==null?2e4:s.timeout),this._readyState="closed",this.uri=a;const f=s.parser||Zv;this.encoder=new f.Encoder,this.decoder=new f.Decoder,this._autoConnect=s.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(a){return arguments.length?(this._reconnection=!!a,a||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(a){return a===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=a,this)}reconnectionDelay(a){var s;return a===void 0?this._reconnectionDelay:(this._reconnectionDelay=a,(s=this.backoff)===null||s===void 0||s.setMin(a),this)}randomizationFactor(a){var s;return a===void 0?this._randomizationFactor:(this._randomizationFactor=a,(s=this.backoff)===null||s===void 0||s.setJitter(a),this)}reconnectionDelayMax(a){var s;return a===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=a,(s=this.backoff)===null||s===void 0||s.setMax(a),this)}timeout(a){return arguments.length?(this._timeout=a,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(a){if(~this._readyState.indexOf("open"))return this;this.engine=new Mv(this.uri,this.opts);const s=this.engine,c=this;this._readyState="opening",this.skipReconnect=!1;const f=we(s,"open",function(){c.onopen(),a&&a()}),h=S=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",S),a?a(S):this.maybeReconnectOnOpen()},y=we(s,"error",h);if(this._timeout!==!1){const S=this._timeout,T=this.setTimeoutFn(()=>{f(),h(new Error("timeout")),s.close()},S);this.opts.autoUnref&&T.unref(),this.subs.push(()=>{this.clearTimeoutFn(T)})}return this.subs.push(f),this.subs.push(y),this}connect(a){return this.open(a)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const a=this.engine;this.subs.push(we(a,"ping",this.onping.bind(this)),we(a,"data",this.ondata.bind(this)),we(a,"error",this.onerror.bind(this)),we(a,"close",this.onclose.bind(this)),we(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(a){try{this.decoder.add(a)}catch(s){this.onclose("parse error",s)}}ondecoded(a){Cu(()=>{this.emitReserved("packet",a)},this.setTimeoutFn)}onerror(a){this.emitReserved("error",a)}socket(a,s){let c=this.nsps[a];return c?this._autoConnect&&!c.active&&c.connect():(c=new Yy(this,a,s),this.nsps[a]=c),c}_destroy(a){const s=Object.keys(this.nsps);for(const c of s)if(this.nsps[c].active)return;this._close()}_packet(a){const s=this.encoder.encode(a);for(let c=0;c<s.length;c++)this.engine.write(s[c],a.options)}cleanup(){this.subs.forEach(a=>a()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(a,s){var c;this.cleanup(),(c=this.engine)===null||c===void 0||c.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",a,s),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const a=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const s=this.backoff.duration();this._reconnecting=!0;const c=this.setTimeoutFn(()=>{a.skipReconnect||(this.emitReserved("reconnect_attempt",a.backoff.attempts),!a.skipReconnect&&a.open(f=>{f?(a._reconnecting=!1,a.reconnect(),this.emitReserved("reconnect_error",f)):a.onreconnect()}))},s);this.opts.autoUnref&&c.unref(),this.subs.push(()=>{this.clearTimeoutFn(c)})}}onreconnect(){const a=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",a)}}const Va={};function Su(u,a){typeof u=="object"&&(a=u,u=void 0),a=a||{};const s=Bv(u,a.path||"/socket.io"),c=s.source,f=s.id,h=s.path,y=Va[f]&&h in Va[f].nsps,S=a.forceNew||a["force new connection"]||a.multiplex===!1||y;let T;return S?T=new Sr(c,a):(Va[f]||(Va[f]=new Sr(c,a)),T=Va[f]),s.query&&!a.query&&(a.query=s.queryKey),T.socket(s.path,a)}Object.assign(Su,{Manager:Sr,Socket:Yy,io:Su,connect:Su});class Xy{socket=null;url;reconnectAttempts=0;maxReconnectAttempts=5;reconnectDelay=1e3;listeners=new Map;constructor(a="http://localhost:5000"){this.url=a}connect(){return new Promise((a,s)=>{if(this.socket?.connected){a();return}this.socket=Su(this.url,{transports:["websocket","polling"],timeout:1e4,reconnection:!0,reconnectionAttempts:this.maxReconnectAttempts,reconnectionDelay:this.reconnectDelay}),this.socket.on("connect",()=>{console.log("Connected to SynergyAI backend"),this.reconnectAttempts=0,this.reconnectDelay=1e3,this.notifyListeners("connect"),a()}),this.socket.on("disconnect",c=>{console.log("Disconnected from SynergyAI backend:",c),this.notifyListeners("disconnect")}),this.socket.on("connect_error",c=>{console.error("WebSocket connection error:",c),this.handleReconnect(),this.notifyListeners("connect_error",c),s(c)}),this.setupEventListeners()})}setupEventListeners(){this.socket&&(this.socket.on("agent_status_update",a=>{console.log("Agent status changed:",a),this.notifyListeners("agent_status_update",a)}),this.socket.on("workflow_progress",a=>{console.log("Workflow progress:",a),this.notifyListeners("workflow_progress",a)}),this.socket.on("ai_execution_complete",a=>{console.log("AI execution finished:",a),this.notifyListeners("ai_execution_complete",a)}))}handleReconnect(){this.reconnectAttempts++,this.reconnectAttempts<=this.maxReconnectAttempts&&(console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`),this.reconnectDelay=Math.min(this.reconnectDelay*2,3e4))}notifyListeners(a,s){const c=this.listeners.get(a);c&&c.forEach(f=>{try{f(s)}catch(h){console.error(`Error in WebSocket event listener for ${a}:`,h)}})}on(a,s){this.listeners.has(a)||this.listeners.set(a,new Set),this.listeners.get(a).add(s)}off(a,s){const c=this.listeners.get(a);c&&(c.delete(s),c.size===0&&this.listeners.delete(a))}onAgentStatusUpdate(a){return this.on("agent_status_update",a),()=>this.off("agent_status_update",a)}onWorkflowProgress(a){return this.on("workflow_progress",a),()=>this.off("workflow_progress",a)}onAIExecutionComplete(a){return this.on("ai_execution_complete",a),()=>this.off("ai_execution_complete",a)}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null),this.listeners.clear()}isConnected(){return this.socket?.connected??!1}getConnectionState(){return this.socket?this.socket.connected?"connected":this.socket.disconnected?"disconnected":"connecting":"disconnected"}}let Eu=null;const Jv=u=>{const a=new Xy(u);return Eu=a,a},Ih=()=>(Eu||(console.warn("WebSocket client not initialized, creating default client"),Eu=new Xy),Eu),ur={api:{timeout:3e4},websocket:{reconnectAttempts:5,reconnectDelay:1e3}},Wv=()=>({api:{baseURL:"http://localhost:5000/api/v1",timeout:ur.api.timeout},websocket:{url:"http://localhost:5000",reconnectAttempts:ur.websocket.reconnectAttempts,reconnectDelay:ur.websocket.reconnectDelay},auth:{token:"my-secret-token"},features:{analytics:!1,errorReporting:!1,debugWebSocket:!1},development:{devMode:!0}}),Hl=Wv(),Gy=()=>{const u=[];try{new URL(Hl.api.baseURL)}catch{u.push("API base URL is not a valid URL")}try{new URL(Hl.websocket.url)}catch{u.push("WebSocket URL is not a valid URL")}return{valid:u.length===0,errors:u}};{console.log("SynergyAI Configuration:",Hl);const u=Gy();u.valid||console.warn("Configuration validation errors:",u.errors)}const ty=u=>{let a;const s=new Set,c=(p,b)=>{const N=typeof p=="function"?p(a):p;if(!Object.is(N,a)){const z=a;a=b??(typeof N!="object"||N===null)?N:Object.assign({},a,N),s.forEach(H=>H(a,z))}},f=()=>a,S={setState:c,getState:f,getInitialState:()=>T,subscribe:p=>(s.add(p),()=>s.delete(p))},T=a=u(c,f,S);return S},Fv=u=>u?ty(u):ty,$v=u=>u;function Pv(u,a=$v){const s=Nh.useSyncExternalStore(u.subscribe,()=>a(u.getState()),()=>a(u.getInitialState()));return Nh.useDebugValue(s),s}const Iv=u=>{const a=Fv(u),s=c=>Pv(a,c);return Object.assign(s,a),s},tb=u=>Iv,ey={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1,VITE_API_BASE_URL:"http://localhost:5000/api/v1",VITE_AUTH_TOKEN:"my-secret-token",VITE_DEBUG_WEBSOCKET:"false",VITE_DEV_MODE:"true",VITE_ENABLE_ANALYTICS:"false",VITE_ENABLE_ERROR_REPORTING:"false",VITE_WEBSOCKET_URL:"http://localhost:5000"},Za=new Map,du=u=>{const a=Za.get(u);return a?Object.fromEntries(Object.entries(a.stores).map(([s,c])=>[s,c.getState()])):{}},eb=(u,a,s)=>{if(u===void 0)return{type:"untracked",connection:a.connect(s)};const c=Za.get(s.name);if(c)return{type:"tracked",store:u,...c};const f={connection:a.connect(s),stores:{}};return Za.set(s.name,f),{type:"tracked",store:u,...f}},nb=(u,a)=>{if(a===void 0)return;const s=Za.get(u);s&&(delete s.stores[a],Object.keys(s.stores).length===0&&Za.delete(u))},lb=u=>{var a,s;if(!u)return;const c=u.split(`
`),f=c.findIndex(y=>y.includes("api.setState"));if(f<0)return;const h=((a=c[f+1])==null?void 0:a.trim())||"";return(s=/.+ (.+) .+/.exec(h))==null?void 0:s[1]},ab=(u,a={})=>(s,c,f)=>{const{enabled:h,anonymousActionType:y,store:S,...T}=a;let p;try{p=(h??(ey?"production":void 0)!=="production")&&window.__REDUX_DEVTOOLS_EXTENSION__}catch{}if(!p)return u(s,c,f);const{connection:b,...N}=eb(S,p,T);let z=!0;f.setState=(q,j,Q)=>{const I=s(q,j);if(!z)return I;const nt=Q===void 0?{type:y||lb(new Error().stack)||"anonymous"}:typeof Q=="string"?{type:Q}:Q;return S===void 0?(b?.send(nt,c()),I):(b?.send({...nt,type:`${S}/${nt.type}`},{...du(T.name),[S]:f.getState()}),I)},f.devtools={cleanup:()=>{b&&typeof b.unsubscribe=="function"&&b.unsubscribe(),nb(T.name,S)}};const H=(...q)=>{const j=z;z=!1,s(...q),z=j},B=u(f.setState,c,f);if(N.type==="untracked"?b?.init(B):(N.stores[N.store]=f,b?.init(Object.fromEntries(Object.entries(N.stores).map(([q,j])=>[q,q===N.store?B:j.getState()])))),f.dispatchFromDevtools&&typeof f.dispatch=="function"){let q=!1;const j=f.dispatch;f.dispatch=(...Q)=>{(ey?"production":void 0)!=="production"&&Q[0].type==="__setState"&&!q&&(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),q=!0),j(...Q)}}return b.subscribe(q=>{var j;switch(q.type){case"ACTION":if(typeof q.payload!="string"){console.error("[zustand devtools middleware] Unsupported action format");return}return sr(q.payload,Q=>{if(Q.type==="__setState"){if(S===void 0){H(Q.state);return}Object.keys(Q.state).length!==1&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format.
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);const I=Q.state[S];if(I==null)return;JSON.stringify(f.getState())!==JSON.stringify(I)&&H(I);return}f.dispatchFromDevtools&&typeof f.dispatch=="function"&&f.dispatch(Q)});case"DISPATCH":switch(q.payload.type){case"RESET":return H(B),S===void 0?b?.init(f.getState()):b?.init(du(T.name));case"COMMIT":if(S===void 0){b?.init(f.getState());return}return b?.init(du(T.name));case"ROLLBACK":return sr(q.state,Q=>{if(S===void 0){H(Q),b?.init(f.getState());return}H(Q[S]),b?.init(du(T.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return sr(q.state,Q=>{if(S===void 0){H(Q);return}JSON.stringify(f.getState())!==JSON.stringify(Q[S])&&H(Q[S])});case"IMPORT_STATE":{const{nextLiftedState:Q}=q.payload,I=(j=Q.computedStates.slice(-1)[0])==null?void 0:j.state;if(!I)return;H(S===void 0?I:I[S]),b?.send(null,Q);return}case"PAUSE_RECORDING":return z=!z}return}}),B},ib=ab,sr=(u,a)=>{let s;try{s=JSON.parse(u)}catch(c){console.error("[zustand devtools middleware] Could not parse the received json",c)}s!==void 0&&a(s)},ny={projects:[],currentProject:null,selectedProjectId:null,agents:[],workflowResults:[],currentStrategy:null,currentAnalysis:null,loading:{isLoading:!1},error:{hasError:!1},sidebarCollapsed:!1,activePanel:"navigator",theme:"light",apiConnected:!1,websocketConnected:!1,lastConnectionCheck:null},ub=tb()(ib(u=>({...ny,setProjects:a=>u({projects:a},!1,"setProjects"),addProject:a=>u(s=>({projects:[...s.projects,a]}),!1,"addProject"),updateProject:(a,s)=>u(c=>({projects:c.projects.map(f=>f.id===a?{...f,...s}:f),currentProject:c.currentProject?.id===a?{...c.currentProject,...s}:c.currentProject}),!1,"updateProject"),deleteProject:a=>u(s=>({projects:s.projects.filter(c=>c.id!==a),currentProject:s.currentProject?.id===a?null:s.currentProject,selectedProjectId:s.selectedProjectId===a?null:s.selectedProjectId}),!1,"deleteProject"),setCurrentProject:a=>u({currentProject:a},!1,"setCurrentProject"),selectProject:a=>u(s=>({selectedProjectId:a,currentProject:a&&s.projects.find(c=>c.id===a)||null}),!1,"selectProject"),setAgents:a=>u({agents:a},!1,"setAgents"),updateAgent:(a,s)=>u(c=>({agents:c.agents.map(f=>f.agent_id===a?{...f,...s}:f)}),!1,"updateAgent"),addWorkflowResult:a=>u(s=>({workflowResults:[...s.workflowResults,a]}),!1,"addWorkflowResult"),clearWorkflowResults:()=>u({workflowResults:[]},!1,"clearWorkflowResults"),setCurrentStrategy:a=>u({currentStrategy:a},!1,"setCurrentStrategy"),setCurrentAnalysis:a=>u({currentAnalysis:a},!1,"setCurrentAnalysis"),setLoading:a=>u(s=>({loading:{...s.loading,...a}}),!1,"setLoading"),setError:a=>u(s=>({error:{...s.error,...a}}),!1,"setError"),clearError:()=>u({error:{hasError:!1}},!1,"clearError"),toggleSidebar:()=>u(a=>({sidebarCollapsed:!a.sidebarCollapsed}),!1,"toggleSidebar"),setSidebarCollapsed:a=>u({sidebarCollapsed:a},!1,"setSidebarCollapsed"),setActivePanel:a=>u({activePanel:a},!1,"setActivePanel"),setTheme:a=>u({theme:a},!1,"setTheme"),setApiConnected:a=>u({apiConnected:a},!1,"setApiConnected"),setWebsocketConnected:a=>u({websocketConnected:a},!1,"setWebsocketConnected"),updateConnectionCheck:()=>u({lastConnectionCheck:Date.now()},!1,"updateConnectionCheck"),reset:()=>u(ny,!1,"reset")}),{name:"synergy-ai-store"})),Vy=()=>{const u=ub(),a=Xt.useCallback(async()=>{try{u.setLoading({isLoading:!0,message:"Connecting to SynergyAI..."});const N=Le(),z=await Promise.race([N.checkAuth(),new Promise((H,B)=>setTimeout(()=>B(new Error("Connection timeout")),5e3))]);if(u.setApiConnected(z),z)try{await Promise.all([s(),y()])}catch(H){console.warn("Failed to load initial data:",H)}try{await Ih().connect(),u.setWebsocketConnected(!0)}catch(H){console.warn("WebSocket connection failed:",H),u.setWebsocketConnected(!1)}u.updateConnectionCheck()}catch(N){console.warn("API connection failed:",N),u.setError({hasError:!0,message:"Backend API is not available",details:N}),u.setApiConnected(!1)}finally{u.setLoading({isLoading:!1})}},[u]),s=Xt.useCallback(async()=>{try{const z=await Le().getProjects();u.setProjects(z)}catch(N){throw u.setError({hasError:!0,message:"Failed to load projects",details:N}),N}},[u]),c=Xt.useCallback(async N=>{try{u.setLoading({isLoading:!0,message:"Creating project..."});const H=await Le().createProject(N);return u.addProject(H),H}catch(z){throw u.setError({hasError:!0,message:"Failed to create project",details:z}),z}finally{u.setLoading({isLoading:!1})}},[u]),f=Xt.useCallback(async(N,z)=>{try{u.setLoading({isLoading:!0,message:"Updating project..."});const B=await Le().updateProject(N,z);return u.updateProject(N,B),B}catch(H){throw u.setError({hasError:!0,message:"Failed to update project",details:H}),H}finally{u.setLoading({isLoading:!1})}},[u]),h=Xt.useCallback(async N=>{try{u.setLoading({isLoading:!0,message:"Deleting project..."}),await Le().deleteProject(N),u.deleteProject(N)}catch(z){throw u.setError({hasError:!0,message:"Failed to delete project",details:z}),z}finally{u.setLoading({isLoading:!1})}},[u]),y=Xt.useCallback(async()=>{try{const z=await Le().getAgents();u.setAgents(z)}catch(N){throw u.setError({hasError:!0,message:"Failed to load agents",details:N}),N}},[u]),S=Xt.useCallback(async N=>{try{u.setLoading({isLoading:!0,message:"Executing workflow..."});const H=await Le().executeWorkflow(N);return u.addWorkflowResult(H),H}catch(z){throw u.setError({hasError:!0,message:"Failed to execute workflow",details:z}),z}finally{u.setLoading({isLoading:!1})}},[u]),T=Xt.useCallback(async N=>{try{u.setLoading({isLoading:!0,message:"Analyzing project..."});const H=await Le().analyzeProject(N);return u.setCurrentAnalysis(H.analysis),H}catch(z){throw u.setError({hasError:!0,message:"Failed to analyze project",details:z}),z}finally{u.setLoading({isLoading:!1})}},[u]),p=Xt.useCallback(async N=>{try{u.setLoading({isLoading:!0,message:"Getting strategy recommendation..."});const H=await Le().suggestStrategy(N);return u.setCurrentStrategy(H.strategy),H}catch(z){throw u.setError({hasError:!0,message:"Failed to get strategy recommendation",details:z}),z}finally{u.setLoading({isLoading:!1})}},[u]),b=Xt.useCallback(async(N,z)=>{try{u.setLoading({isLoading:!0,message:"Getting AI strategy..."});const B=await Le().getPromptStrategy(N,z);return u.setCurrentAnalysis(B.analysis.analysis),u.setCurrentStrategy(B.strategy.strategy),B}catch(H){throw u.setError({hasError:!0,message:"Failed to get AI strategy",details:H}),H}finally{u.setLoading({isLoading:!1})}},[u]);return Xt.useEffect(()=>{if(u.websocketConnected)try{const N=Ih(),z=N.onAgentStatusUpdate(q=>{u.updateAgent(q.agent_id,{status:q.status})}),H=N.onWorkflowProgress(q=>{console.log("Workflow progress:",q)}),B=N.onAIExecutionComplete(q=>{console.log("AI execution complete:",q)});return()=>{z(),H(),B()}}catch(N){console.warn("Failed to set up WebSocket listeners:",N)}},[u,u.websocketConnected]),{...u,initializeConnections:a,loadProjects:s,createProject:c,updateProject:f,deleteProject:h,loadAgents:y,executeWorkflow:S,analyzeProject:T,suggestStrategy:p,getPromptStrategy:b,clearError:u.clearError,selectProject:u.selectProject,setActivePanel:u.setActivePanel}},sb=()=>L.jsxs("div",{className:"p-4 bg-blue-100 border border-blue-300 rounded-lg",children:[L.jsx("h3",{className:"text-lg font-semibold text-blue-900 mb-2",children:"🎉 SynergyAI Frontend Setup Complete!"}),L.jsxs("div",{className:"text-sm text-blue-700 space-y-1",children:[L.jsx("p",{children:"✅ React + TypeScript working"}),L.jsx("p",{children:"✅ TailwindCSS styling active"}),L.jsx("p",{children:"✅ Vite dev server running"}),L.jsx("p",{children:"✅ Component structure ready"})]})]}),cb=()=>{const{apiConnected:u,websocketConnected:a,loadProjects:s,loadAgents:c}=Vy(),[f,h]=Xt.useState([]),[y,S]=Xt.useState(!1),T=async()=>{S(!0),h([]);const p=[];try{p.push("🔄 Testing API connection..."),h([...p]),await s(),p.push("✅ Projects API: Success"),h([...p]),await c(),p.push("✅ Agents API: Success"),h([...p]),p.push("🎉 All tests passed!")}catch(b){p.push(`❌ Test failed: ${b.message||"Unknown error"}`)}finally{h(p),S(!1)}};return L.jsxs("div",{className:"bg-white rounded-lg shadow-md border border-gray-200 p-6",children:[L.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Backend Connection Test"}),L.jsxs("div",{className:"space-y-3 mb-4",children:[L.jsxs("div",{className:"flex items-center space-x-2",children:[L.jsx("div",{className:`w-3 h-3 rounded-full ${u?"bg-green-500":"bg-red-500"}`}),L.jsxs("span",{className:"text-sm",children:["API Connection: ",u?"Connected":"Disconnected"]})]}),L.jsxs("div",{className:"flex items-center space-x-2",children:[L.jsx("div",{className:`w-3 h-3 rounded-full ${a?"bg-green-500":"bg-red-500"}`}),L.jsxs("span",{className:"text-sm",children:["WebSocket: ",a?"Connected":"Disconnected"]})]})]}),L.jsx("button",{onClick:T,disabled:y,className:"bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300 text-white font-medium px-4 py-2 rounded-lg transition-colors duration-200 mb-4",children:y?"Testing...":"Test Backend APIs"}),f.length>0&&L.jsxs("div",{className:"bg-gray-50 rounded-lg p-3",children:[L.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Test Results:"}),L.jsx("div",{className:"space-y-1",children:f.map((p,b)=>L.jsx("div",{className:"text-sm text-gray-600 font-mono",children:p},b))})]})]})};function rb(){const[u,a]=Xt.useState(!1),{initializeConnections:s,loading:c,error:f,apiConnected:h,websocketConnected:y}=Vy();if(Xt.useEffect(()=>{(async()=>{try{const p=Gy();if(!p.valid){console.error("Configuration validation failed:",p.errors),a(!0);return}tv({token:Hl.auth.token,baseURL:Hl.api.baseURL}),Jv(Hl.websocket.url),await new Promise(b=>setTimeout(b,100));try{await s()}catch(b){console.warn("Failed to connect to backend, running in offline mode:",b)}}catch(p){console.error("App initialization error:",p)}finally{a(!0)}})()},[]),!u||c.isLoading)return L.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:L.jsxs("div",{className:"text-center",children:[L.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),L.jsx("p",{className:"text-gray-600",children:c.message||"Loading SynergyAI..."})]})});const S=f.hasError;return L.jsxs("div",{className:"min-h-screen bg-gray-50",children:[S&&L.jsx("div",{className:"bg-red-100 border-b border-red-200 px-6 py-3",children:L.jsxs("div",{className:"flex items-center justify-between",children:[L.jsxs("div",{className:"flex items-center",children:[L.jsx("div",{className:"w-4 h-4 bg-red-500 rounded-full mr-3"}),L.jsxs("span",{className:"text-red-800 text-sm",children:["Backend connection failed: ",f.message]})]}),L.jsx("button",{onClick:()=>window.location.reload(),className:"text-red-600 hover:text-red-800 text-sm underline",children:"Retry"})]})}),L.jsx("header",{className:"bg-white border-b border-gray-200 px-6 py-4",children:L.jsx("div",{className:"flex items-center justify-between",children:L.jsxs("div",{className:"flex items-center space-x-4",children:[L.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"SynergyAI"}),L.jsxs("div",{className:"flex items-center space-x-2",children:[L.jsx("div",{className:`w-2 h-2 rounded-full ${h?"bg-green-500":"bg-red-500"}`}),L.jsxs("span",{className:"text-sm text-gray-600",children:["API ",h?"Connected":"Disconnected"]}),L.jsx("div",{className:`w-2 h-2 rounded-full ${y?"bg-green-500":"bg-red-500"}`}),L.jsxs("span",{className:"text-sm text-gray-600",children:["WebSocket ",y?"Connected":"Disconnected"]})]})]})})}),L.jsx("main",{className:`flex ${S?"h-[calc(100vh-140px)]":"h-[calc(100vh-80px)]"}`,children:L.jsx("div",{className:"flex-1 flex items-center justify-center",children:L.jsxs("div",{className:"text-center",children:[L.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Welcome to SynergyAI"}),L.jsx("p",{className:"text-gray-600 mb-8 max-w-md mx-auto",children:"Your intelligent project workflow system is ready. The three-panel interface will be implemented in the next steps."}),L.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 max-w-4xl mx-auto mb-8",children:[L.jsxs("div",{className:"bg-white rounded-lg shadow-md border border-gray-200 p-6 text-center",children:[L.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"Project Navigator"}),L.jsx("p",{className:"text-sm text-gray-600",children:'The "Why" - Manage your projects and goals'})]}),L.jsxs("div",{className:"bg-white rounded-lg shadow-md border border-gray-200 p-6 text-center",children:[L.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"Workflow Log"}),L.jsx("p",{className:"text-sm text-gray-600",children:'The "What/How" - Track AI agent execution'})]}),L.jsxs("div",{className:"bg-white rounded-lg shadow-md border border-gray-200 p-6 text-center",children:[L.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"Context & Controls"}),L.jsx("p",{className:"text-sm text-gray-600",children:'The "Tools" - AI strategy and controls'})]})]}),L.jsx("div",{className:"max-w-md mx-auto mb-6",children:L.jsx(cb,{})}),L.jsx("div",{className:"mt-8 max-w-md mx-auto",children:L.jsx(sb,{})}),L.jsxs("div",{className:"mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200 max-w-md mx-auto",children:[L.jsx("h4",{className:"font-medium text-blue-900 mb-2",children:"Connection Status"}),L.jsxs("div",{className:"text-sm text-blue-700 space-y-1",children:[L.jsx("div",{children:"✅ Frontend: Running"}),L.jsx("div",{children:"✅ TypeScript: Configured"}),L.jsx("div",{children:"✅ TailwindCSS: Active"}),L.jsx("div",{children:"✅ State Management: Ready"}),L.jsxs("div",{className:h?"text-green-700":"text-orange-700",children:[h?"✅":"⚠️"," Backend API: ",h?"Connected":"Offline"]})]})]})]})})})]})}class ob extends Xt.Component{constructor(a){super(a),this.state={hasError:!1}}static getDerivedStateFromError(a){return{hasError:!0,error:a}}componentDidCatch(a,s){console.error("Error caught by boundary:",a,s),this.setState({error:a,errorInfo:s})}render(){return this.state.hasError?L.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:L.jsx("div",{className:"max-w-md mx-auto text-center",children:L.jsxs("div",{className:"bg-red-100 border border-red-200 rounded-lg p-6",children:[L.jsx("h2",{className:"text-lg font-semibold text-red-800 mb-2",children:"Something went wrong"}),L.jsx("p",{className:"text-red-700 mb-4",children:this.state.error?.message||"An unexpected error occurred"}),L.jsx("button",{onClick:()=>window.location.reload(),className:"bg-red-600 hover:bg-red-700 text-white font-medium px-4 py-2 rounded-lg transition-colors duration-200",children:"Reload Page"}),!1]})})}):this.props.children}}_0.createRoot(document.getElementById("root")).render(L.jsx(Xt.StrictMode,{children:L.jsx(ob,{children:L.jsx(rb,{})})}));

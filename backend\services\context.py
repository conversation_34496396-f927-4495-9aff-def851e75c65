# backend/services/context.py
from typing import Dict, List, Any

class ContextManager:
    def __init__(self):
        # In-memory storage for simplicity. A real implementation would use a database.
        self.contexts: Dict[str, List[Dict[str, Any]]] = {}

    def add_context(self, workflow_id: str, context_item: Dict[str, Any]):
        if workflow_id not in self.contexts:
            self.contexts[workflow_id] = []
        self.contexts[workflow_id].append(context_item)

    def get_relevant_context(self, workflow_id: str, task_type: str) -> List[Dict[str, Any]]:
        # This is a naive implementation. A real system would have sophisticated
        # relevance scoring and filtering based on the task.
        return self.contexts.get(workflow_id, [])

    def clear_context(self, workflow_id: str):
        if workflow_id in self.contexts:
            del self.contexts[workflow_id]
